import React, { useEffect, useState } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  Chip,
  CircularProgress
} from '@mui/material'
import {
  Security as SecurityIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'

interface AuthStatus {
  sessionValid: boolean
  tokenExpiry: Date | null
  lastRefresh: Date | null
  connectionStatus: 'connected' | 'disconnected' | 'error'
  errorCount: number
}

interface AuthMonitorProps {
  showDebugInfo?: boolean
  autoRefreshInterval?: number
}

export const AuthMonitor: React.FC<AuthMonitorProps> = ({
  showDebugInfo = false,
  autoRefreshInterval = 300000 // 5 minutes
}) => {
  const { user, session, refreshSession, isOnline } = useAuth()
  const [authStatus, setAuthStatus] = useState<AuthStatus>({
    sessionValid: false,
    tokenExpiry: null,
    lastRefresh: null,
    connectionStatus: 'disconnected',
    errorCount: 0
  })
  const [showDialog, setShowDialog] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastError, setLastError] = useState<string | null>(null)

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const { data: { session: currentSession }, error } = await supabase.auth.getSession()

        if (error) {
          setAuthStatus(prev => ({
            ...prev,
            connectionStatus: 'error',
            errorCount: prev.errorCount + 1,
            sessionValid: false
          }))
          setLastError(error.message)
          return
        }

        if (currentSession && currentSession.access_token && currentSession.refresh_token) {
          const tokenExpiry = new Date(currentSession.expires_at! * 1000)
          const now = new Date()
          // Add 90 second margin for token expiry (same as Supabase default)
          const marginMs = 90 * 1000
          const isValid = (tokenExpiry.getTime() - now.getTime()) > marginMs &&
                          currentSession.access_token.length > 0 &&
                          currentSession.refresh_token.length > 0

          setAuthStatus({
            sessionValid: isValid,
            tokenExpiry,
            lastRefresh: new Date(),
            connectionStatus: 'connected',
            errorCount: 0
          })
          setLastError(null)
        } else {
          setAuthStatus(prev => ({
            ...prev,
            sessionValid: false,
            tokenExpiry: null,
            connectionStatus: user ? 'connected' : 'disconnected'
          }))
        }
      } catch (error) {
        setAuthStatus(prev => ({
          ...prev,
          connectionStatus: 'error',
          errorCount: prev.errorCount + 1,
          sessionValid: false
        }))
        setLastError(error instanceof Error ? error.message : 'Unknown error')
      }
    }

    // Initial check
    checkAuthStatus()

    // Set up periodic checks
    const interval = setInterval(checkAuthStatus, 60000) // Check every minute

    return () => clearInterval(interval)
  }, [session])

  useEffect(() => {
    if (!user || !session) return

    // Set up auto-refresh - align with Supabase's default behavior
    const autoRefresh = setInterval(async () => {
      if (authStatus.sessionValid && isOnline) {
        const tokenExpiry = authStatus.tokenExpiry
        if (tokenExpiry) {
          const timeUntilExpiry = tokenExpiry.getTime() - Date.now()
          // Refresh if token expires in less than 3 minutes (180000ms)
          // This aligns with Supabase's EXPIRY_MARGIN_MS (90s * 3 ticks)
          if (timeUntilExpiry < 180000 && timeUntilExpiry > 0) {
            try {
              await refreshSession()
            } catch (error) {
              console.error('Auto-refresh failed:', error)
              setLastError(`Auto-refresh failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
            }
          }
        }
      }
    }, autoRefreshInterval)

    return () => clearInterval(autoRefresh)
  }, [user, session, authStatus.sessionValid, authStatus.tokenExpiry, isOnline, refreshSession, autoRefreshInterval])

  const handleManualRefresh = async () => {
    setIsRefreshing(true)
    try {
      const result = await refreshSession()
      if (result.error) {
        setLastError(result.error.userMessage || result.error.message)
      }
    } catch (error) {
      setLastError(error instanceof Error ? error.message : 'Refresh failed')
    } finally {
      setIsRefreshing(false)
    }
  }

  const getStatusColor = () => {
    if (!isOnline) return 'warning'
    if (authStatus.connectionStatus === 'error') return 'error'
    if (!authStatus.sessionValid) return 'warning'
    return 'success'
  }

  const getStatusText = () => {
    if (!isOnline) return 'Offline'
    if (authStatus.connectionStatus === 'error') return 'Connection Error'
    if (!authStatus.sessionValid) return 'Session Invalid'
    return 'Connected'
  }

  const formatTimeRemaining = () => {
    if (!authStatus.tokenExpiry) return 'Unknown'
    const timeRemaining = authStatus.tokenExpiry.getTime() - Date.now()
    if (timeRemaining <= 0) return 'Expired'
    
    const minutes = Math.floor(timeRemaining / 60000)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }

  if (!showDebugInfo && authStatus.connectionStatus === 'connected' && authStatus.sessionValid) {
    return null // Don't show anything if everything is working fine
  }

  return (
    <>
      {/* Status Indicator */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          zIndex: 9998,
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}
      >
        {(authStatus.connectionStatus === 'error' || !authStatus.sessionValid || !isOnline) && (
          <Alert
            severity={getStatusColor()}
            icon={
              authStatus.connectionStatus === 'error' ? <WarningIcon /> : 
              !authStatus.sessionValid ? <SecurityIcon /> : 
              <CheckCircleIcon />
            }
            action={
              <Button
                color="inherit"
                size="small"
                onClick={() => setShowDialog(true)}
              >
                Details
              </Button>
            }
            sx={{ cursor: 'pointer' }}
            onClick={() => setShowDialog(true)}
          >
            <Typography variant="caption">
              Auth: {getStatusText()}
            </Typography>
          </Alert>
        )}

        {showDebugInfo && (
          <Chip
            icon={<SecurityIcon />}
            label={`Auth: ${getStatusText()}`}
            color={getStatusColor()}
            size="small"
            onClick={() => setShowDialog(true)}
            sx={{ cursor: 'pointer' }}
          />
        )}
      </Box>

      {/* Detailed Status Dialog */}
      <Dialog
        open={showDialog}
        onClose={() => setShowDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SecurityIcon />
            Authentication Status
          </Box>
        </DialogTitle>
        
        <DialogContent>
          <List>
            <ListItem>
              <ListItemText
                primary="Connection Status"
                secondary={authStatus.connectionStatus}
                secondaryTypographyProps={{
                  component: 'div'
                }}
              />
              <Chip
                label={authStatus.connectionStatus}
                color={
                  authStatus.connectionStatus === 'connected' ? 'success' :
                  authStatus.connectionStatus === 'error' ? 'error' : 'warning'
                }
                size="small"
              />
            </ListItem>

            <ListItem>
              <ListItemText
                primary="Network Status"
                secondary={isOnline ? 'Online' : 'Offline'}
                secondaryTypographyProps={{
                  component: 'div'
                }}
              />
              <Chip
                label={isOnline ? 'Online' : 'Offline'}
                color={isOnline ? 'success' : 'error'}
                size="small"
              />
            </ListItem>

            <ListItem>
              <ListItemText
                primary="Session Valid"
                secondary={authStatus.sessionValid ? 'Valid' : 'Invalid'}
                secondaryTypographyProps={{
                  component: 'div'
                }}
              />
              <Chip
                label={authStatus.sessionValid ? 'Valid' : 'Invalid'}
                color={authStatus.sessionValid ? 'success' : 'error'}
                size="small"
              />
            </ListItem>

            {authStatus.tokenExpiry && (
              <ListItem>
                <ListItemText
                  primary="Token Expires"
                  secondary={`${authStatus.tokenExpiry.toLocaleString()} (${formatTimeRemaining()} remaining)`}
                />
              </ListItem>
            )}

            {authStatus.lastRefresh && (
              <ListItem>
                <ListItemText
                  primary="Last Refresh"
                  secondary={authStatus.lastRefresh.toLocaleString()}
                />
              </ListItem>
            )}

            <ListItem>
              <ListItemText
                primary="Error Count"
                secondary={authStatus.errorCount}
              />
            </ListItem>

            {lastError && (
              <ListItem>
                <ListItemText
                  primary="Last Error"
                  secondaryTypographyProps={{
                    component: 'div'
                  }}
                />
                <Box sx={{ mt: 1, width: '100%' }}>
                  <Alert severity="error">
                    {lastError}
                  </Alert>
                </Box>
              </ListItem>
            )}
          </List>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={handleManualRefresh}
            disabled={isRefreshing || !isOnline}
            startIcon={
              isRefreshing ? (
                <CircularProgress size={16} />
              ) : (
                <RefreshIcon />
              )
            }
          >
            {isRefreshing ? 'Refreshing...' : 'Refresh Session'}
          </Button>
          <Button onClick={() => setShowDialog(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default AuthMonitor
