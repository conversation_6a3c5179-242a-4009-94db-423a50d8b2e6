import React, { useState, useEffect } from 'react'
import {
  Alert,
  Snackbar,
  Box,
  Typography,
  Button,
  CircularProgress
} from '@mui/material'
import {
  WifiOff as WifiOffIcon,
  Wifi as WifiIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'
import { useAuth } from '../contexts/AuthContext'
import { NetworkChecker } from '../utils/authErrorHandler'

interface NetworkStatusProps {
  showPersistentIndicator?: boolean
}

export const NetworkStatus: React.FC<NetworkStatusProps> = ({ 
  showPersistentIndicator = false 
}) => {
  const { isOnline, refreshSession } = useAuth()
  const [showOfflineAlert, setShowOfflineAlert] = useState(false)
  const [showOnlineAlert, setShowOnlineAlert] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [wasOffline, setWasOffline] = useState(false)

  useEffect(() => {
    if (!isOnline && !wasOffline) {
      setShowOfflineAlert(true)
      setWasOffline(true)
    } else if (isOnline && wasOffline) {
      setShowOfflineAlert(false)
      setShowOnlineAlert(true)
      setWasOffline(false)
      
      // Auto-hide online alert after 3 seconds
      setTimeout(() => {
        setShowOnlineAlert(false)
      }, 3000)
    }
  }, [isOnline, wasOffline])

  const handleRetryConnection = async () => {
    setIsRefreshing(true)
    try {
      const isConnected = await NetworkChecker.checkConnectivity()
      if (isConnected) {
        await refreshSession()
      }
    } catch (error) {
      console.error('Failed to retry connection:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleCloseOfflineAlert = () => {
    setShowOfflineAlert(false)
  }

  const handleCloseOnlineAlert = () => {
    setShowOnlineAlert(false)
  }

  return (
    <>
      {/* Persistent Network Indicator */}
      {showPersistentIndicator && (
        <Box
          sx={{
            position: 'fixed',
            top: 16,
            right: 16,
            zIndex: 9999,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            padding: 1,
            borderRadius: 1,
            backgroundColor: isOnline ? 'success.main' : 'error.main',
            color: 'white',
            fontSize: '0.875rem'
          }}
        >
          {isOnline ? <WifiIcon fontSize="small" /> : <WifiOffIcon fontSize="small" />}
          <Typography variant="caption">
            {isOnline ? 'Online' : 'Offline'}
          </Typography>
        </Box>
      )}

      {/* Offline Alert */}
      <Snackbar
        open={showOfflineAlert}
        onClose={handleCloseOfflineAlert}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        sx={{ zIndex: 9999 }}
      >
        <Alert
          severity="error"
          onClose={handleCloseOfflineAlert}
          icon={<WifiOffIcon />}
          action={
            <Button
              color="inherit"
              size="small"
              onClick={handleRetryConnection}
              disabled={isRefreshing}
              startIcon={
                isRefreshing ? (
                  <CircularProgress size={16} color="inherit" />
                ) : (
                  <RefreshIcon />
                )
              }
            >
              {isRefreshing ? 'Retrying...' : 'Retry'}
            </Button>
          }
          sx={{
            '& .MuiAlert-message': {
              display: 'flex',
              flexDirection: 'column',
              gap: 0.5
            }
          }}
        >
          <Typography variant="body2" fontWeight="bold">
            Connection Lost
          </Typography>
          <Typography variant="caption">
            You're currently offline. Some features may not work properly.
          </Typography>
        </Alert>
      </Snackbar>

      {/* Online Alert */}
      <Snackbar
        open={showOnlineAlert}
        onClose={handleCloseOnlineAlert}
        autoHideDuration={3000}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        sx={{ zIndex: 9999 }}
      >
        <Alert
          severity="success"
          onClose={handleCloseOnlineAlert}
          icon={<WifiIcon />}
          sx={{
            '& .MuiAlert-message': {
              display: 'flex',
              flexDirection: 'column',
              gap: 0.5
            }
          }}
        >
          <Typography variant="body2" fontWeight="bold">
            Connection Restored
          </Typography>
          <Typography variant="caption">
            You're back online. All features are now available.
          </Typography>
        </Alert>
      </Snackbar>
    </>
  )
}

export default NetworkStatus
