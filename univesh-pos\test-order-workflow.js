/**
 * Complete Order Management Workflow Test
 * 
 * This script tests the complete order workflow:
 * 1. Create orders through the menu system
 * 2. Track orders in ongoing orders
 * 3. Update order statuses
 * 4. Complete orders
 */

import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testOrderWorkflow() {
  console.log('🧪 Testing Complete Order Management Workflow...\n');
  
  try {
    // Authenticate
    console.log('🔐 Step 1: Authenticate...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (authError) {
      console.error('❌ Authentication failed:', authError);
      return false;
    }
    
    console.log('✅ Authenticated successfully');
    
    // Get admin profile
    const { data: adminProfile } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', '<EMAIL>')
      .single();
    
    const adminId = adminProfile.id;
    
    // Get some products for testing
    console.log('\n🍽️ Step 2: Get products for order...');
    const { data: products } = await supabase
      .from('products')
      .select('*')
      .eq('is_active', true)
      .limit(5);
    
    console.log(`✅ Found ${products?.length || 0} products for testing`);
    
    // Create a test order
    console.log('\n📋 Step 3: Create test order...');
    
    const orderItems = [
      { product: products[0], quantity: 2 },
      { product: products[1], quantity: 1 },
      { product: products[2], quantity: 3 }
    ];
    
    const subtotal = orderItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
    const taxAmount = subtotal * 0.05;
    const total = subtotal + taxAmount;
    
    const orderData = {
      table_number: 'T10',
      order_type: 'dine_in',
      status: 'pending',
      subtotal_amount: subtotal,
      tax_amount: taxAmount,
      discount_amount: 0,
      complementary_amount: 0,
      total_amount: total,
      employee_id: adminId,
      customer_id: null,
      notes: 'Test order from workflow test'
    };
    
    const { data: orderResult, error: orderError } = await supabase
      .from('orders')
      .insert(orderData)
      .select()
      .single();
    
    if (orderError) {
      console.error('❌ Error creating order:', orderError);
      return false;
    }
    
    console.log(`✅ Order created: ${orderResult.id}`);
    console.log(`   Table: ${orderResult.table_number}`);
    console.log(`   Total: $${orderResult.total_amount.toFixed(2)}`);
    
    // Create order items
    console.log('\n📦 Step 4: Create order items...');
    
    const orderItemsData = orderItems.map(item => ({
      order_id: orderResult.id,
      product_id: item.product.id,
      quantity: item.quantity,
      unit_price_at_order: item.product.price,
      item_subtotal: item.product.price * item.quantity
    }));
    
    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItemsData);
    
    if (itemsError) {
      console.error('❌ Error creating order items:', itemsError);
      return false;
    }
    
    console.log(`✅ Order items created: ${orderItemsData.length} items`);
    orderItemsData.forEach((item, index) => {
      const product = orderItems[index].product;
      console.log(`   - ${product.name} x${item.quantity} = $${item.item_subtotal.toFixed(2)}`);
    });
    
    // Test ongoing orders query
    console.log('\n📊 Step 5: Test ongoing orders query...');
    
    // Test ongoing orders query with manual join
    const { data: ongoingOrders, error: ongoingError } = await supabase
      .from('orders')
      .select(`
        *,
        order_items(
          id,
          product_id,
          quantity,
          unit_price_at_order,
          item_subtotal,
          products(name, description)
        )
      `)
      .in('status', ['pending', 'preparing', 'ready'])
      .order('created_at', { ascending: false });

    // Get employee names separately
    if (ongoingOrders && ongoingOrders.length > 0) {
      const employeeIds = [...new Set(ongoingOrders.map(order => order.employee_id))];
      const { data: employees } = await supabase
        .from('profiles')
        .select('id, full_name')
        .in('id', employeeIds);

      // Add employee names to orders
      ongoingOrders.forEach(order => {
        const employee = employees?.find(emp => emp.id === order.employee_id);
        order.employee_name = employee?.full_name || 'Unknown';
      });
    }
    
    if (ongoingError) {
      console.error('❌ Error fetching ongoing orders:', ongoingError);
    } else {
      console.log(`✅ Ongoing orders found: ${ongoingOrders?.length || 0}`);
      
      const testOrder = ongoingOrders?.find(order => order.id === orderResult.id);
      if (testOrder) {
        console.log(`✅ Test order found in ongoing orders`);
        console.log(`   Items: ${testOrder.order_items?.length || 0}`);
        console.log(`   Employee: ${testOrder.employee_name}`);
      } else {
        console.log('❌ Test order not found in ongoing orders');
      }
    }
    
    // Test order status updates
    console.log('\n🔄 Step 6: Test order status updates...');
    
    const statusFlow = ['preparing', 'ready', 'completed'];
    
    for (const status of statusFlow) {
      console.log(`   Updating to: ${status}`);
      
      const { error: updateError } = await supabase
        .from('orders')
        .update({ 
          status: status,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderResult.id);
      
      if (updateError) {
        console.error(`❌ Error updating to ${status}:`, updateError);
      } else {
        console.log(`✅ Updated to ${status}`);
      }
      
      // Small delay between updates
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Verify final status
    console.log('\n✅ Step 7: Verify final order status...');
    
    const { data: finalOrder, error: finalError } = await supabase
      .from('orders')
      .select('*')
      .eq('id', orderResult.id)
      .single();
    
    if (finalError) {
      console.error('❌ Error fetching final order:', finalError);
    } else {
      console.log(`✅ Final order status: ${finalOrder.status}`);
      console.log(`   Created: ${new Date(finalOrder.created_at).toLocaleString()}`);
      console.log(`   Updated: ${new Date(finalOrder.updated_at).toLocaleString()}`);
    }
    
    // Test payment creation
    console.log('\n💳 Step 8: Test payment creation...');
    
    const paymentData = {
      order_id: orderResult.id,
      amount: orderResult.total_amount,
      payment_method: 'Card',
      status: 'completed',
      transaction_reference: `TXN_${Date.now()}`,
      processed_by_employee_id: adminId
    };
    
    const { data: paymentResult, error: paymentError } = await supabase
      .from('payments')
      .insert(paymentData)
      .select()
      .single();
    
    if (paymentError) {
      console.error('❌ Error creating payment:', paymentError);
    } else {
      console.log(`✅ Payment created: ${paymentResult.id}`);
      console.log(`   Amount: $${paymentResult.amount.toFixed(2)}`);
      console.log(`   Method: ${paymentResult.payment_method}`);
      console.log(`   Reference: ${paymentResult.transaction_reference}`);
    }
    
    // Test order history query
    console.log('\n📈 Step 9: Test order history query...');
    
    const { data: completedOrders, error: historyError } = await supabase
      .from('orders')
      .select(`
        *,
        payments(amount, payment_method, status),
        order_items(
          quantity,
          unit_price_at_order,
          item_subtotal,
          products(name)
        )
      `)
      .eq('status', 'completed')
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (historyError) {
      console.error('❌ Error fetching order history:', historyError);
    } else {
      console.log(`✅ Completed orders found: ${completedOrders?.length || 0}`);
      
      const testCompletedOrder = completedOrders?.find(order => order.id === orderResult.id);
      if (testCompletedOrder) {
        console.log(`✅ Test order found in completed orders`);
        console.log(`   Payment status: ${testCompletedOrder.payments?.[0]?.status}`);
        console.log(`   Items count: ${testCompletedOrder.order_items?.length || 0}`);
      }
    }
    
    console.log('\n🧹 Step 10: Cleanup test data...');
    
    // Delete test payment
    if (paymentResult) {
      await supabase.from('payments').delete().eq('id', paymentResult.id);
    }
    
    // Delete test order items
    await supabase.from('order_items').delete().eq('order_id', orderResult.id);
    
    // Delete test order
    await supabase.from('orders').delete().eq('id', orderResult.id);
    
    console.log('✅ Test data cleaned up');
    
    return true;
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    return false;
  } finally {
    await supabase.auth.signOut();
    console.log('\n👋 Signed out');
  }
}

// Run the test
testOrderWorkflow().then(success => {
  if (success) {
    console.log('\n🎉 Order Management Workflow Test PASSED!');
    console.log('\n✅ Summary:');
    console.log('   • Order creation working');
    console.log('   • Order items creation working');
    console.log('   • Ongoing orders query working');
    console.log('   • Order status updates working');
    console.log('   • Payment processing working');
    console.log('   • Order history query working');
    console.log('   • Complete workflow functional');
  } else {
    console.log('\n❌ Order Management Workflow Test FAILED!');
  }
}).catch(error => {
  console.error('\n💥 Test execution failed:', error);
});
