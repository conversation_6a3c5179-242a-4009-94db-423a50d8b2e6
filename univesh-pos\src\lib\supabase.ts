import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Enhanced Supabase client configuration with better error handling
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    debug: import.meta.env.DEV
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  },
  global: {
    headers: {
      'X-Client-Info': 'univesh-pos@1.0.0'
    }
  },
  db: {
    schema: 'public'
  }
})

// Database types based on our schema
export interface Database {
  public: {
    Tables: {
      roles: {
        Row: {
          id: string
          role_name: 'admin' | 'manager' | 'cashier'
          created_at: string
        }
        Insert: {
          id?: string
          role_name: 'admin' | 'manager' | 'cashier'
          created_at?: string
        }
        Update: {
          id?: string
          role_name?: 'admin' | 'manager' | 'cashier'
          created_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          role_id: string
          sales_id_number: string
          full_name: string
          email: string
          phone_number: string | null
          date_of_birth: string | null
          designation: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          role_id: string
          sales_id_number: string
          full_name: string
          email: string
          phone_number?: string | null
          date_of_birth?: string | null
          designation?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          role_id?: string
          sales_id_number?: string
          full_name?: string
          email?: string
          phone_number?: string | null
          date_of_birth?: string | null
          designation?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          category_id: string | null
          name: string
          description: string | null
          price: number
          current_stock: number
          image_url: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          category_id?: string | null
          name: string
          description?: string | null
          price: number
          current_stock?: number
          image_url?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          category_id?: string | null
          name?: string
          description?: string | null
          price?: number
          current_stock?: number
          image_url?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          table_number: string | null
          order_type: 'dine_in' | 'delivery' | 'take_away'
          status: 'draft' | 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled'
          subtotal_amount: number
          tax_amount: number
          discount_amount: number
          complementary_amount: number
          total_amount: number
          employee_id: string
          customer_id: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          table_number?: string | null
          order_type: 'dine_in' | 'delivery' | 'take_away'
          status?: 'draft' | 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled'
          subtotal_amount: number
          tax_amount: number
          discount_amount?: number
          complementary_amount?: number
          total_amount: number
          employee_id: string
          customer_id?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          table_number?: string | null
          order_type?: 'dine_in' | 'delivery' | 'take_away'
          status?: 'draft' | 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled'
          subtotal_amount?: number
          tax_amount?: number
          discount_amount?: number
          complementary_amount?: number
          total_amount?: number
          employee_id?: string
          customer_id?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      customers: {
        Row: {
          id: string
          full_name: string
          email: string | null
          phone_number: string | null
          billing_address: string | null
          shipping_address: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          full_name: string
          email?: string | null
          phone_number?: string | null
          billing_address?: string | null
          shipping_address?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string
          email?: string | null
          phone_number?: string | null
          billing_address?: string | null
          shipping_address?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          order_id: string
          amount: number
          payment_method: 'Cash' | 'Card' | 'Credit'
          status: 'completed' | 'pending' | 'refunded' | 'voided'
          transaction_reference: string | null
          processed_by_employee_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          order_id: string
          amount: number
          payment_method: 'Cash' | 'Card' | 'Credit'
          status?: 'completed' | 'pending' | 'refunded' | 'voided'
          transaction_reference?: string | null
          processed_by_employee_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          order_id?: string
          amount?: number
          payment_method?: 'Cash' | 'Card' | 'Credit'
          status?: 'completed' | 'pending' | 'refunded' | 'voided'
          transaction_reference?: string | null
          processed_by_employee_id?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      order_type: 'dine_in' | 'delivery' | 'take_away'
      order_status: 'draft' | 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled'
      payment_method: 'Cash' | 'Card' | 'Credit'
      payment_status: 'completed' | 'pending' | 'refunded' | 'voided'
      variation_type: 'variation' | 'add_on'
    }
  }
}
