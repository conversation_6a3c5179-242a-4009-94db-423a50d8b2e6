{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/runtests.ts", "../../src/vite-env.d.ts", "../../src/components/common/accessiblebutton.tsx", "../../src/components/common/errorboundary.tsx", "../../src/components/common/loadingscreen.tsx", "../../src/components/common/loadingstate.tsx", "../../src/components/common/networkerrorhandler.tsx", "../../src/components/layout/dashboardlayout.tsx", "../../src/contexts/authcontext.tsx", "../../src/hooks/usedashboarddata.ts", "../../src/hooks/useperformancemonitor.ts", "../../src/lib/supabase.ts", "../../src/pages/bills.tsx", "../../src/pages/customermanagement.tsx", "../../src/pages/dashboard.tsx", "../../src/pages/loginpage.tsx", "../../src/pages/manageinventory.tsx", "../../src/pages/menuscreen.tsx", "../../src/pages/ongoingorders.tsx", "../../src/pages/reports.tsx", "../../src/pages/settings.tsx", "../../src/tests/criticaljourneys.test.ts", "../../src/theme/index.ts", "../../src/utils/monitoring.ts", "../../src/utils/testrunner.ts", "../../src/utils/validation.ts"], "errors": true, "version": "5.8.3"}