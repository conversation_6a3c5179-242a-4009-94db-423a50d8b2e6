{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/runtests.ts", "../../src/vite-env.d.ts", "../../src/components/authmonitor.tsx", "../../src/components/networkstatus.tsx", "../../src/components/auth/loginpage.tsx", "../../src/components/auth/protectedroute.tsx", "../../src/components/common/accessiblebutton.tsx", "../../src/components/common/errorboundary.tsx", "../../src/components/common/loadingscreen.tsx", "../../src/components/common/loadingstate.tsx", "../../src/components/common/networkerrorhandler.tsx", "../../src/components/error/errorboundary.tsx", "../../src/components/layout/dashboardlayout.tsx", "../../src/contexts/authcontext.tsx", "../../src/hooks/usedashboarddata.ts", "../../src/hooks/useperformancemonitor.ts", "../../src/hooks/usesystemsettings.ts", "../../src/lib/supabase.ts", "../../src/pages/bills.tsx", "../../src/pages/billswitherrorboundary.tsx", "../../src/pages/customermanagement.tsx", "../../src/pages/customermanagementwitherrorboundary.tsx", "../../src/pages/dashboard.tsx", "../../src/pages/dashboardwitherrorboundary.tsx", "../../src/pages/loginpage.tsx", "../../src/pages/manageinventory.tsx", "../../src/pages/manageinventorywitherrorboundary.tsx", "../../src/pages/menuscreen.tsx", "../../src/pages/menuscreenwitherrorboundary.tsx", "../../src/pages/ongoingorders.tsx", "../../src/pages/ongoingorderswitherrorboundary.tsx", "../../src/pages/reports.tsx", "../../src/pages/reportswitherrorboundary.tsx", "../../src/pages/settings.tsx", "../../src/pages/settingswitherrorboundary.tsx", "../../src/tests/authtest.ts", "../../src/tests/criticaljourneys.test.ts", "../../src/theme/index.ts", "../../src/utils/autherrorhandler.ts", "../../src/utils/browserextensionhandler.ts", "../../src/utils/loadingstatemanager.ts", "../../src/utils/monitoring.ts", "../../src/utils/navigationstatemanager.ts", "../../src/utils/testrunner.ts", "../../src/utils/validation.ts"], "errors": true, "version": "5.8.3"}