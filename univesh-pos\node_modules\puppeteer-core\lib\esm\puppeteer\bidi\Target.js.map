{"version": 3, "file": "Target.js", "sourceRoot": "", "sources": ["../../../../src/bidi/Target.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,MAAM,EAAE,UAAU,EAAC,MAAM,kBAAkB,CAAC;AACpD,OAAO,EAAC,oBAAoB,EAAC,MAAM,qBAAqB,CAAC;AAMzD,OAAO,EAAC,QAAQ,EAAC,MAAM,WAAW,CAAC;AAGnC;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,MAAM;IAC3C,QAAQ,CAAc;IAEtB,YAAY,OAAoB;QAC9B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAEQ,MAAM;QACb,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;IACQ,GAAG;QACV,OAAO,EAAE,CAAC;IACZ,CAAC;IACQ,gBAAgB;QACvB,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;IACQ,IAAI;QACX,OAAO,UAAU,CAAC,OAAO,CAAC;IAC5B,CAAC;IACQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IACQ,cAAc;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;IAC/C,CAAC;IACQ,MAAM;QACb,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,MAAM;IACxC,KAAK,CAAW;IAEhB,YAAY,IAAc;QACxB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;IAEQ,KAAK,CAAC,IAAI;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IACQ,KAAK,CAAC,MAAM;QACnB,OAAO,QAAQ,CAAC,IAAI,CAClB,IAAI,CAAC,cAAc,EAAE,EACrB,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,eAAe,CACvC,CAAC;IACJ,CAAC;IACQ,GAAG;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IAC1B,CAAC;IACQ,gBAAgB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;IACvC,CAAC;IACQ,IAAI;QACX,OAAO,UAAU,CAAC,IAAI,CAAC;IACzB,CAAC;IACQ,OAAO;QACd,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;IACQ,cAAc;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;IACrC,CAAC;IACQ,MAAM;QACb,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,MAAM;IACzC,MAAM,CAAY;IAClB,KAAK,CAAuB;IAE5B,YAAY,KAAgB;QAC1B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAEQ,KAAK,CAAC,IAAI;QACjB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CACxB,IAAI,CAAC,cAAc,EAAE,EACrB,IAAI,CAAC,MAAM,CAAC,eAAe,CAC5B,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IACQ,KAAK,CAAC,MAAM;QACnB,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IAC3E,CAAC;IACQ,GAAG;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IAC3B,CAAC;IACQ,gBAAgB;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;IACxC,CAAC;IACQ,IAAI;QACX,OAAO,UAAU,CAAC,IAAI,CAAC;IACzB,CAAC;IACQ,OAAO;QACd,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;IACQ,cAAc;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,cAAc,EAAE,CAAC;IAC7C,CAAC;IACQ,MAAM;QACb,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,MAAM;IAC1C,OAAO,CAAgB;IAEvB,YAAY,MAAqB;QAC/B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAEQ,KAAK,CAAC,IAAI;QACjB,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;IACQ,KAAK,CAAC,MAAM;QACnB,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;IACQ,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IAC5B,CAAC;IACQ,gBAAgB;QACvB,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;IACQ,IAAI;QACX,OAAO,UAAU,CAAC,KAAK,CAAC;IAC1B,CAAC;IACQ,OAAO;QACd,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;IACQ,cAAc;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,cAAc,EAAE,CAAC;IACpD,CAAC;IACQ,MAAM;QACb,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;CACF"}