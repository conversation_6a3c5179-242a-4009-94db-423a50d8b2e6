import React from 'react'
import { Box, CircularProgress, Typography } from '@mui/material'
import { colors } from '../../theme'

const LoadingScreen: React.FC = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        backgroundColor: colors.background.default,
        gap: 3
      }}
    >
      <CircularProgress
        size={60}
        thickness={4}
        sx={{
          color: colors.primary.main
        }}
      />
      <Typography
        variant="h6"
        sx={{
          color: colors.text.primary,
          fontWeight: 500
        }}
      >
        Loading Univesh POS...
      </Typography>
    </Box>
  )
}

export default LoadingScreen
