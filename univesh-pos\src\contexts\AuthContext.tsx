import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import type { User, Session } from '@supabase/auth-js'
import { supabase } from '../lib/supabase'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SessionManager } from '../utils/authErrorHandler'

interface Profile {
  id: string
  role_id: string
  sales_id_number: string
  full_name: string
  email: string
  phone_number: string | null
  date_of_birth: string | null
  designation: string | null
  created_at: string
  updated_at: string
  role?: {
    id: string
    role_name: 'admin' | 'manager' | 'cashier'
  }
}

interface AuthError {
  message: string
  code?: string
  userMessage?: string
}

interface AuthContextType {
  user: User | null
  profile: Profile | null
  session: Session | null
  loading: boolean
  isOnline: boolean
  signIn: (salesId: string, password: string) => Promise<{ error: AuthError | null }>
  signUp: (salesId: string, password: string, fullName: string, email: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<Profile>) => Promise<{ error: AuthError | null }>
  refreshSession: () => Promise<{ error: AuthError | null }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [isOnline, setIsOnline] = useState(NetworkChecker.getStatus())

  useEffect(() => {
    // Network status monitoring
    const handleNetworkChange = (online: boolean) => {
      setIsOnline(online)
      if (online && session) {
        // Attempt to refresh session when coming back online
        refreshSession()
      }
    }

    NetworkChecker.addListener(handleNetworkChange)

    // Get initial session with error handling
    const initializeAuth = async (retryCount = 0) => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Error getting initial session:', error)
          const errorInfo = AuthErrorHandler.handleAuthError(error)

          // Only retry up to 3 times to prevent infinite loops
          if (errorInfo.shouldRetry && retryCount < 3) {
            console.log(`Retrying auth initialization (attempt ${retryCount + 1}/3)`)
            setTimeout(() => initializeAuth(retryCount + 1), errorInfo.retryDelay || 2000)
            return
          } else {
            // If we've exhausted retries or shouldn't retry, set loading to false
            console.error('Auth initialization failed after retries')
            setLoading(false)
            return
          }
        }

        setSession(session)
        setUser(session?.user ?? null)

        if (session?.user) {
          await fetchProfile(session.user.id)
        } else {
          console.log('No session found, user not authenticated')
          setLoading(false)
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error)
        setLoading(false)
      }
    }

    console.log('Starting auth initialization...')
    initializeAuth()

    // Listen for auth changes with enhanced error handling
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.id)

      setSession(session)
      setUser(session?.user ?? null)

      if (event === 'TOKEN_REFRESHED') {
        console.log('Token refreshed successfully')
        SessionManager.clearRefreshState()
      }

      if (event === 'SIGNED_OUT') {
        setProfile(null)
        setLoading(false)
        SessionManager.clearRefreshState()
        return
      }

      if (session?.user) {
        await fetchProfile(session.user.id)
      } else {
        setProfile(null)
        setLoading(false)
      }
    })

    return () => {
      subscription.unsubscribe()
      NetworkChecker.removeListener(handleNetworkChange)
    }
  }, [])

  const fetchProfile = useCallback(async (userId: string) => {
    try {
      console.log('Fetching profile for user:', userId)
      const { data, error } = await AuthErrorHandler.retryOperation(
        () => supabase
          .from('profiles')
          .select(`
            *,
            role:roles(id, role_name)
          `)
          .eq('id', userId)
          .single(),
        'FETCH_PROFILE'
      )

      if (error) {
        console.error('Error fetching profile:', error)
        const errorInfo = AuthErrorHandler.handleAuthError(error)
        console.error('Profile fetch error details:', errorInfo)
        // Even if profile fetch fails, we should still set loading to false
        // The user is authenticated but we couldn't get their profile
        setProfile(null)
      } else {
        console.log('Profile fetched successfully:', data)
        setProfile(data)
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
      const errorInfo = AuthErrorHandler.handleAuthError(error)
      console.error('Profile fetch error details:', errorInfo)
      setProfile(null)
    } finally {
      console.log('Setting loading to false after profile fetch')
      setLoading(false)
    }
  }, [])

  const signIn = async (salesId: string, password: string) => {
    try {
      setLoading(true)

      // Check network connectivity first
      if (!isOnline) {
        return {
          error: {
            message: 'No internet connection',
            code: 'NETWORK_ERROR',
            userMessage: 'Please check your internet connection and try again.'
          }
        }
      }

      // First, find the user by sales_id_number to get their email
      const { data: profileData, error: profileError } = await AuthErrorHandler.retryOperation(
        () => supabase
          .from('profiles')
          .select('email')
          .eq('sales_id_number', salesId)
          .single(),
        'FETCH_PROFILE_FOR_SIGNIN'
      )

      if (profileError || !profileData) {
        const errorInfo = AuthErrorHandler.handleAuthError(profileError)
        return {
          error: {
            message: 'Invalid Sales ID',
            code: 'INVALID_SALES_ID',
            userMessage: 'Sales ID not found. Please check your Sales ID and try again.'
          }
        }
      }

      // Sign in with email and password
      const { error } = await AuthErrorHandler.retryOperation(
        () => supabase.auth.signInWithPassword({
          email: profileData.email,
          password: password
        }),
        'SIGN_IN'
      )

      if (error) {
        const errorInfo = AuthErrorHandler.handleAuthError(error)
        return {
          error: {
            message: errorInfo.message,
            code: errorInfo.code,
            userMessage: errorInfo.userMessage
          }
        }
      }

      return { error: null }
    } catch (error) {
      const errorInfo = AuthErrorHandler.handleAuthError(error)
      return {
        error: {
          message: errorInfo.message,
          code: errorInfo.code,
          userMessage: errorInfo.userMessage
        }
      }
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (salesId: string, password: string, fullName: string, email: string) => {
    try {
      setLoading(true)

      // Check if sales ID already exists
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('sales_id_number', salesId)
        .single()

      if (existingProfile) {
        return { error: { message: 'Sales ID already exists' } }
      }

      // Sign up the user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: email,
        password: password
      })

      if (authError || !authData.user) {
        return { error: authError }
      }

      // Get default cashier role
      const { data: roleData, error: roleError } = await supabase
        .from('roles')
        .select('id')
        .eq('role_name', 'cashier')
        .single()

      if (roleError || !roleData) {
        return { error: { message: 'Failed to assign default role' } }
      }

      // Create profile
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          role_id: roleData.id,
          sales_id_number: salesId,
          full_name: fullName,
          email: email
        })

      if (profileError) {
        return { error: profileError }
      }

      return { error: null }
    } catch (error) {
      return { error }
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      await supabase.auth.signOut()
      setUser(null)
      setProfile(null)
      setSession(null)
      SessionManager.clearRefreshState()
    } catch (error) {
      console.error('Error signing out:', error)
      // Force clear local state even if signOut fails
      setUser(null)
      setProfile(null)
      setSession(null)
      SessionManager.clearRefreshState()
    } finally {
      setLoading(false)
    }
  }

  const refreshSession = async () => {
    try {
      if (!isOnline) {
        return {
          error: {
            message: 'No internet connection',
            code: 'NETWORK_ERROR',
            userMessage: 'Please check your internet connection and try again.'
          }
        }
      }

      const data = await SessionManager.refreshSession(supabase)
      return { error: null }
    } catch (error) {
      const errorInfo = AuthErrorHandler.handleAuthError(error)
      return {
        error: {
          message: errorInfo.message,
          code: errorInfo.code,
          userMessage: errorInfo.userMessage
        }
      }
    }
  }

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) return { error: { message: 'No user logged in' } }

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)

      if (!error) {
        // Refresh profile data
        await fetchProfile(user.id)
      }

      return { error }
    } catch (error) {
      return { error }
    }
  }

  const value = {
    user,
    profile,
    session,
    loading,
    isOnline,
    signIn,
    signUp,
    signOut,
    updateProfile,
    refreshSession
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
