import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import type { User, Session } from '@supabase/auth-js'
import { supabase } from '../lib/supabase'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SessionManager } from '../utils/authErrorHandler'

interface Profile {
  id: string
  role_id: string
  sales_id_number: string
  full_name: string
  email: string
  phone_number: string | null
  date_of_birth: string | null
  designation: string | null
  created_at: string
  updated_at: string
  role?: {
    id: string
    role_name: 'admin' | 'manager' | 'cashier'
  }
}

interface AuthError {
  message: string
  code?: string
  userMessage?: string
}

interface AuthContextType {
  user: User | null
  profile: Profile | null
  session: Session | null
  loading: boolean
  isOnline: boolean
  signIn: (salesId: string, password: string) => Promise<{ error: AuthError | null }>
  signUp: (salesId: string, password: string, fullName: string, email: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<Profile>) => Promise<{ error: AuthError | null }>
  refreshSession: () => Promise<{ error: AuthError | null }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [isOnline, setIsOnline] = useState(NetworkChecker.getStatus())
  const [authTimeout, setAuthTimeout] = useState<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Set up auth timeout - if loading takes more than 15 seconds, force stop loading
    const timeoutId = setTimeout(() => {
      if (loading) {
        console.warn('⏰ [AuthContext] Auth initialization timeout reached (15s), forcing loading to false')
        setLoading(false)
      }
    }, 15000) // 15 second timeout

    setAuthTimeout(timeoutId)

    // Network status monitoring
    const handleNetworkChange = (online: boolean) => {
      setIsOnline(online)
      if (online && session) {
        // Attempt to refresh session when coming back online
        refreshSession()
      }
    }

    NetworkChecker.addListener(handleNetworkChange)

    // Test Supabase connectivity
    const testSupabaseConnection = async (): Promise<boolean> => {
      try {
        console.log('🔗 [AuthContext] Testing Supabase connection...')
        const { error } = await supabase.from('roles').select('count').limit(1)
        if (error) {
          console.error('🔗 [AuthContext] Supabase connection test failed:', error)
          return false
        }
        console.log('✅ [AuthContext] Supabase connection test successful')
        return true
      } catch (error) {
        console.error('🔗 [AuthContext] Supabase connection test error:', error)
        return false
      }
    }

    // Get initial session with error handling
    const initializeAuth = async (retryCount = 0) => {
      try {
        console.log(`🚀 [AuthContext] Starting auth initialization (attempt ${retryCount + 1})`)
        console.log('🚀 [AuthContext] Current states - loading:', loading, 'isOnline:', isOnline)

        // Test Supabase connection first
        if (!await testSupabaseConnection()) {
          console.error('💥 [AuthContext] Supabase connection failed, retrying...')
          if (retryCount < 3) {
            setTimeout(() => initializeAuth(retryCount + 1), 3000)
            return
          } else {
            console.error('💥 [AuthContext] Supabase connection failed after retries')
            setLoading(false)
            return
          }
        }

        const startTime = Date.now()
        const { data: { session }, error } = await supabase.auth.getSession()
        const sessionTime = Date.now() - startTime

        console.log(`🚀 [AuthContext] getSession() completed in ${sessionTime}ms`)
        console.log('🚀 [AuthContext] Session result:', session ? 'Session found' : 'No session', error ? 'Error occurred' : 'No error')

        if (error) {
          console.error('❌ [AuthContext] Error getting initial session:', error)
          const errorInfo = AuthErrorHandler.handleAuthError(error)

          // Only retry up to 3 times to prevent infinite loops
          if (errorInfo.shouldRetry && retryCount < 3) {
            console.log(`🔄 [AuthContext] Retrying auth initialization (attempt ${retryCount + 1}/3)`)
            setTimeout(() => initializeAuth(retryCount + 1), errorInfo.retryDelay || 2000)
            return
          } else {
            // If we've exhausted retries or shouldn't retry, set loading to false
            console.error('💥 [AuthContext] Auth initialization failed after retries')
            setLoading(false)
            console.log('🔄 [AuthContext] Loading set to false due to auth init failure')
            return
          }
        }

        console.log('🔄 [AuthContext] Setting session and user state')
        setSession(session)
        setUser(session?.user ?? null)

        if (session?.user) {
          console.log('👤 [AuthContext] User found, fetching profile for:', session.user.id)
          await fetchProfile(session.user.id)
        } else {
          console.log('👤 [AuthContext] No session found, user not authenticated')
          setLoading(false)
          console.log('🔄 [AuthContext] Loading set to false - no session')
        }
      } catch (error) {
        console.error('💥 [AuthContext] Failed to initialize auth (catch block):', error)
        setLoading(false)
        console.log('🔄 [AuthContext] Loading set to false due to exception')
      }
    }

    console.log('Starting auth initialization...')
    initializeAuth()

    // Listen for auth changes with enhanced error handling
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔔 [AuthContext] Auth state changed:', event, session?.user?.id)
      console.log('🔔 [AuthContext] Current loading state before processing:', loading)

      setSession(session)
      setUser(session?.user ?? null)
      console.log('🔔 [AuthContext] Session and user state updated')

      if (event === 'TOKEN_REFRESHED') {
        console.log('🔄 [AuthContext] Token refreshed successfully')
        SessionManager.clearRefreshState()
      }

      if (event === 'SIGNED_OUT') {
        console.log('👋 [AuthContext] User signed out, clearing state')
        setProfile(null)
        setLoading(false)
        SessionManager.clearRefreshState()
        console.log('🔄 [AuthContext] Loading set to false - signed out')
        return
      }

      if (session?.user) {
        console.log('👤 [AuthContext] User session exists, fetching profile for:', session.user.id)
        await fetchProfile(session.user.id)
      } else {
        console.log('👤 [AuthContext] No user session, clearing profile and loading')
        setProfile(null)
        setLoading(false)
        console.log('🔄 [AuthContext] Loading set to false - no user session')
      }
    })

    return () => {
      subscription.unsubscribe()
      NetworkChecker.removeListener(handleNetworkChange)
      if (authTimeout) {
        clearTimeout(authTimeout)
      }
    }
  }, [])

  const fetchProfile = useCallback(async (userId: string) => {
    try {
      console.log('🔍 [AuthContext] Starting profile fetch for user:', userId)
      console.log('🔍 [AuthContext] Current loading state:', loading)
      console.log('🔍 [AuthContext] Network status:', isOnline)

      const startTime = Date.now()
      // First fetch the profile
      const { data: profileData, error: profileError } = await AuthErrorHandler.retryOperation(
        () => supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single(),
        'FETCH_PROFILE'
      )

      if (profileError) {
        throw profileError
      }

      // For now, let's use a simple approach and map role_id to role_name
      // This avoids the RLS issue with the roles table
      const roleMapping = {
        'c63b305d-d9fc-4c41-93d5-dd656b6305b2': 'admin',
        '8eebeb7d-8384-4958-aafb-254d2001c784': 'manager',
        '90749928-0d0a-4584-a94c-13bb82b97134': 'cashier'
      }

      // Combine the data with mapped role
      const data = {
        ...profileData,
        role: {
          id: profileData.role_id,
          role_name: roleMapping[profileData.role_id] || 'cashier'
        }
      }

      const error = profileError

      const fetchTime = Date.now() - startTime
      console.log(`🔍 [AuthContext] Profile fetch completed in ${fetchTime}ms`)

      if (error) {
        console.error('❌ [AuthContext] Error fetching profile:', error)
        const errorInfo = AuthErrorHandler.handleAuthError(error)
        console.error('❌ [AuthContext] Profile fetch error details:', errorInfo)
        // Even if profile fetch fails, we should still set loading to false
        // The user is authenticated but we couldn't get their profile
        setProfile(null)
        console.log('🔍 [AuthContext] Profile set to null due to error')
      } else {
        console.log('✅ [AuthContext] Profile fetched successfully:', data)
        setProfile(data)
        console.log('🔍 [AuthContext] Profile state updated')
      }
    } catch (error) {
      console.error('💥 [AuthContext] Failed to fetch profile (catch block):', error)
      const errorInfo = AuthErrorHandler.handleAuthError(error)
      console.error('💥 [AuthContext] Profile fetch error details:', errorInfo)
      setProfile(null)
      console.log('🔍 [AuthContext] Profile set to null due to exception')
    } finally {
      console.log('🔄 [AuthContext] Setting loading to false after profile fetch')
      setLoading(false)
      console.log('🔄 [AuthContext] Loading state set to false')
      // Clear timeout since we're done loading
      if (authTimeout) {
        clearTimeout(authTimeout)
        setAuthTimeout(null)
      }
    }
  }, [])

  const signIn = async (salesId: string, password: string) => {
    try {
      setLoading(true)

      // Check network connectivity first
      if (!isOnline) {
        return {
          error: {
            message: 'No internet connection',
            code: 'NETWORK_ERROR',
            userMessage: 'Please check your internet connection and try again.'
          }
        }
      }

      // First, find the user by sales_id_number to get their email
      const { data: profileData, error: profileError } = await AuthErrorHandler.retryOperation(
        () => supabase
          .from('profiles')
          .select('email')
          .eq('sales_id_number', salesId)
          .single(),
        'FETCH_PROFILE_FOR_SIGNIN'
      )

      if (profileError || !profileData) {
        const errorInfo = AuthErrorHandler.handleAuthError(profileError)
        return {
          error: {
            message: 'Invalid Sales ID',
            code: 'INVALID_SALES_ID',
            userMessage: 'Sales ID not found. Please check your Sales ID and try again.'
          }
        }
      }

      // Sign in with email and password
      const { error } = await AuthErrorHandler.retryOperation(
        () => supabase.auth.signInWithPassword({
          email: profileData.email,
          password: password
        }),
        'SIGN_IN'
      )

      if (error) {
        const errorInfo = AuthErrorHandler.handleAuthError(error)
        return {
          error: {
            message: errorInfo.message,
            code: errorInfo.code,
            userMessage: errorInfo.userMessage
          }
        }
      }

      return { error: null }
    } catch (error) {
      const errorInfo = AuthErrorHandler.handleAuthError(error)
      return {
        error: {
          message: errorInfo.message,
          code: errorInfo.code,
          userMessage: errorInfo.userMessage
        }
      }
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (salesId: string, password: string, fullName: string, email: string) => {
    try {
      setLoading(true)

      // Check if sales ID already exists
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('sales_id_number', salesId)
        .single()

      if (existingProfile) {
        return { error: { message: 'Sales ID already exists' } }
      }

      // Sign up the user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: email,
        password: password
      })

      if (authError || !authData.user) {
        return { error: authError }
      }

      // Get default cashier role
      const { data: roleData, error: roleError } = await supabase
        .from('roles')
        .select('id')
        .eq('role_name', 'cashier')
        .single()

      if (roleError || !roleData) {
        return { error: { message: 'Failed to assign default role' } }
      }

      // Create profile
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          role_id: roleData.id,
          sales_id_number: salesId,
          full_name: fullName,
          email: email
        })

      if (profileError) {
        return { error: profileError }
      }

      return { error: null }
    } catch (error) {
      return { error }
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      await supabase.auth.signOut()
      setUser(null)
      setProfile(null)
      setSession(null)
      SessionManager.clearRefreshState()
    } catch (error) {
      console.error('Error signing out:', error)
      // Force clear local state even if signOut fails
      setUser(null)
      setProfile(null)
      setSession(null)
      SessionManager.clearRefreshState()
    } finally {
      setLoading(false)
    }
  }

  const refreshSession = async () => {
    try {
      if (!isOnline) {
        return {
          error: {
            message: 'No internet connection',
            code: 'NETWORK_ERROR',
            userMessage: 'Please check your internet connection and try again.'
          }
        }
      }

      const data = await SessionManager.refreshSession(supabase)
      return { error: null }
    } catch (error) {
      const errorInfo = AuthErrorHandler.handleAuthError(error)
      return {
        error: {
          message: errorInfo.message,
          code: errorInfo.code,
          userMessage: errorInfo.userMessage
        }
      }
    }
  }

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) return { error: { message: 'No user logged in' } }

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)

      if (!error) {
        // Refresh profile data
        await fetchProfile(user.id)
      }

      return { error }
    } catch (error) {
      return { error }
    }
  }

  const value = {
    user,
    profile,
    session,
    loading,
    isOnline,
    signIn,
    signUp,
    signOut,
    updateProfile,
    refreshSession
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
