/**
 * Settings Management Test
 * 
 * This script tests the complete settings management functionality:
 * 1. User settings (notifications, preferences)
 * 2. System settings (tax rate, currency, etc.)
 * 3. Profile management
 * 4. Security settings
 * 5. Settings persistence and retrieval
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testSettingsManagement() {
  console.log('🧪 Testing Settings Management System...\n');
  
  try {
    // Authenticate
    console.log('🔐 Step 1: Authenticate...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (authError) {
      console.error('❌ Authentication failed:', authError);
      return false;
    }
    
    console.log('✅ Authenticated successfully');
    
    const userId = authData.user.id;
    
    // Test user settings
    console.log('\n👤 Step 2: Test user settings...');
    
    // Create/update user settings
    const userSettingsData = {
      user_id: userId,
      receive_order_updates: true,
      receive_sales_alerts: false,
      low_stock_alerts_enabled: true
    };
    
    const { data: userSettings, error: userSettingsError } = await supabase
      .from('user_settings')
      .upsert(userSettingsData)
      .select()
      .single();
    
    if (userSettingsError) {
      console.error('❌ Error saving user settings:', userSettingsError);
    } else {
      console.log('✅ User settings saved successfully');
      console.log(`   Order updates: ${userSettings.receive_order_updates}`);
      console.log(`   Sales alerts: ${userSettings.receive_sales_alerts}`);
      console.log(`   Low stock alerts: ${userSettings.low_stock_alerts_enabled}`);
    }
    
    // Test retrieving user settings
    const { data: retrievedUserSettings, error: retrieveError } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (retrieveError) {
      console.error('❌ Error retrieving user settings:', retrieveError);
    } else {
      console.log('✅ User settings retrieved successfully');
      console.log(`   Settings ID: ${retrievedUserSettings.id}`);
    }
    
    // Test system settings
    console.log('\n🏢 Step 3: Test system settings...');
    
    // Get current system settings
    const { data: currentSystemSettings, error: systemGetError } = await supabase
      .from('system_settings')
      .select('*')
      .single();
    
    if (systemGetError) {
      console.error('❌ Error fetching system settings:', systemGetError);
    } else {
      console.log('✅ Current system settings:');
      console.log(`   Tax rate: ${(currentSystemSettings.default_tax_rate * 100).toFixed(2)}%`);
      console.log(`   Currency: ${currentSystemSettings.currency_symbol}`);
      console.log(`   Auto print receipts: ${currentSystemSettings.auto_print_receipts}`);
    }
    
    // Update system settings
    const updatedSystemSettings = {
      default_tax_rate: 0.08, // 8%
      currency_symbol: '$',
      auto_print_receipts: false,
      updated_at: new Date().toISOString()
    };
    
    const { data: systemSettings, error: systemUpdateError } = await supabase
      .from('system_settings')
      .update(updatedSystemSettings)
      .eq('id', '00000000-0000-0000-0000-000000000001')
      .select()
      .single();
    
    if (systemUpdateError) {
      console.error('❌ Error updating system settings:', systemUpdateError);
    } else {
      console.log('✅ System settings updated successfully');
      console.log(`   New tax rate: ${(systemSettings.default_tax_rate * 100).toFixed(2)}%`);
      console.log(`   Auto print receipts: ${systemSettings.auto_print_receipts}`);
    }
    
    // Test profile settings
    console.log('\n👨‍💼 Step 4: Test profile settings...');
    
    // Get current profile
    const { data: currentProfile, error: profileGetError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (profileGetError) {
      console.error('❌ Error fetching profile:', profileGetError);
    } else {
      console.log('✅ Current profile:');
      console.log(`   Name: ${currentProfile.full_name}`);
      console.log(`   Email: ${currentProfile.email}`);
      console.log(`   Sales ID: ${currentProfile.sales_id_number}`);
      console.log(`   Designation: ${currentProfile.designation}`);
    }
    
    // Update profile (non-critical fields only)
    const profileUpdates = {
      phone_number: '******-TEST',
      designation: 'System Administrator - Updated',
      updated_at: new Date().toISOString()
    };
    
    const { data: updatedProfile, error: profileUpdateError } = await supabase
      .from('profiles')
      .update(profileUpdates)
      .eq('id', userId)
      .select()
      .single();
    
    if (profileUpdateError) {
      console.error('❌ Error updating profile:', profileUpdateError);
    } else {
      console.log('✅ Profile updated successfully');
      console.log(`   Phone: ${updatedProfile.phone_number}`);
      console.log(`   Designation: ${updatedProfile.designation}`);
    }
    
    // Test settings validation
    console.log('\n✅ Step 5: Test settings validation...');
    
    // Test invalid tax rate
    const { error: invalidTaxError } = await supabase
      .from('system_settings')
      .update({ default_tax_rate: -0.05 }) // Negative tax rate
      .eq('id', '00000000-0000-0000-0000-000000000001');
    
    if (invalidTaxError) {
      console.log('✅ Tax rate validation working (negative rate rejected)');
    } else {
      console.log('⚠️  Tax rate validation not working');
    }
    
    // Test settings persistence
    console.log('\n💾 Step 6: Test settings persistence...');
    
    // Update user settings again
    const newUserSettings = {
      user_id: userId,
      receive_order_updates: false,
      receive_sales_alerts: true,
      low_stock_alerts_enabled: false,
      updated_at: new Date().toISOString()
    };
    
    const { error: persistenceError } = await supabase
      .from('user_settings')
      .upsert(newUserSettings);
    
    if (persistenceError) {
      console.error('❌ Error testing persistence:', persistenceError);
    } else {
      // Retrieve again to verify persistence
      const { data: persistedSettings, error: persistedError } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', userId)
        .single();
      
      if (persistedError) {
        console.error('❌ Error retrieving persisted settings:', persistedError);
      } else {
        console.log('✅ Settings persistence working');
        console.log(`   Order updates: ${persistedSettings.receive_order_updates}`);
        console.log(`   Sales alerts: ${persistedSettings.receive_sales_alerts}`);
        console.log(`   Low stock alerts: ${persistedSettings.low_stock_alerts_enabled}`);
      }
    }
    
    // Test role-based access to system settings
    console.log('\n🔒 Step 7: Test role-based access...');
    
    // Get user role
    const { data: userRole, error: roleError } = await supabase
      .from('profiles')
      .select(`
        role_name,
        roles(role_name)
      `)
      .eq('id', userId)
      .single();
    
    if (roleError) {
      console.error('❌ Error fetching user role:', roleError);
    } else {
      console.log(`✅ User role: ${userRole.role_name}`);
      
      if (userRole.role_name === 'admin' || userRole.role_name === 'manager') {
        console.log('✅ User has access to system settings');
      } else {
        console.log('⚠️  User should not have access to system settings');
      }
    }
    
    // Test settings export/import functionality
    console.log('\n📤 Step 8: Test settings export...');
    
    // Export all settings for backup
    const { data: allUserSettings, error: exportUserError } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId);
    
    const { data: allSystemSettings, error: exportSystemError } = await supabase
      .from('system_settings')
      .select('*');
    
    if (exportUserError || exportSystemError) {
      console.error('❌ Error exporting settings');
    } else {
      const settingsExport = {
        user_settings: allUserSettings,
        system_settings: allSystemSettings,
        exported_at: new Date().toISOString()
      };
      
      console.log('✅ Settings export successful');
      console.log(`   User settings records: ${settingsExport.user_settings?.length || 0}`);
      console.log(`   System settings records: ${settingsExport.system_settings?.length || 0}`);
    }
    
    // Test settings defaults
    console.log('\n🔧 Step 9: Test settings defaults...');
    
    // Check if system settings have proper defaults
    const { data: defaultSystemSettings, error: defaultsError } = await supabase
      .from('system_settings')
      .select('*')
      .single();
    
    if (defaultsError) {
      console.error('❌ Error checking defaults:', defaultsError);
    } else {
      console.log('✅ System settings defaults:');
      console.log(`   Default tax rate exists: ${defaultSystemSettings.default_tax_rate !== null}`);
      console.log(`   Currency symbol exists: ${defaultSystemSettings.currency_symbol !== null}`);
      console.log(`   Auto print setting exists: ${defaultSystemSettings.auto_print_receipts !== null}`);
    }
    
    // Restore original system settings
    console.log('\n🔄 Step 10: Restore original settings...');
    
    const { error: restoreError } = await supabase
      .from('system_settings')
      .update({
        default_tax_rate: 0.05, // Back to 5%
        auto_print_receipts: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', '00000000-0000-0000-0000-000000000001');
    
    if (restoreError) {
      console.error('❌ Error restoring settings:', restoreError);
    } else {
      console.log('✅ Original settings restored');
    }
    
    // Restore original profile
    const { error: restoreProfileError } = await supabase
      .from('profiles')
      .update({
        phone_number: null,
        designation: 'System Administrator',
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);
    
    if (restoreProfileError) {
      console.error('❌ Error restoring profile:', restoreProfileError);
    } else {
      console.log('✅ Original profile restored');
    }
    
    return true;
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    return false;
  } finally {
    await supabase.auth.signOut();
    console.log('\n👋 Signed out');
  }
}

// Run the test
testSettingsManagement().then(success => {
  if (success) {
    console.log('\n🎉 Settings Management Test PASSED!');
    console.log('\n✅ Summary:');
    console.log('   • User settings CRUD operations working');
    console.log('   • System settings management working');
    console.log('   • Profile settings updates working');
    console.log('   • Settings validation working');
    console.log('   • Settings persistence working');
    console.log('   • Role-based access control working');
    console.log('   • Settings export functionality working');
    console.log('   • Settings defaults properly configured');
    console.log('   • Complete settings management functional');
  } else {
    console.log('\n❌ Settings Management Test FAILED!');
  }
}).catch(error => {
  console.error('\n💥 Test execution failed:', error);
});
