/**
 * Bills and Payment Processing Test
 * 
 * This script tests the complete bills and payment workflow:
 * 1. Create orders ready for payment
 * 2. Process payments with different methods
 * 3. Generate receipts
 * 4. Update inventory
 * 5. Complete order lifecycle
 */

import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testBillsAndPayment() {
  console.log('🧪 Testing Bills and Payment Processing...\n');
  
  try {
    // Authenticate
    console.log('🔐 Step 1: Authenticate...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (authError) {
      console.error('❌ Authentication failed:', authError);
      return false;
    }
    
    console.log('✅ Authenticated successfully');
    
    // Get admin profile
    const { data: adminProfile } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', '<EMAIL>')
      .single();
    
    const adminId = adminProfile.id;
    
    // Get products for testing
    console.log('\n🍽️ Step 2: Get products for testing...');
    const { data: products } = await supabase
      .from('products')
      .select('*')
      .eq('is_active', true)
      .limit(3);
    
    console.log(`✅ Found ${products?.length || 0} products for testing`);
    
    // Create test orders ready for payment
    console.log('\n📋 Step 3: Create test orders ready for payment...');
    
    const testOrders = [];
    const paymentMethods = ['Cash', 'Card', 'Credit'];
    
    for (let i = 0; i < 3; i++) {
      const orderItems = [
        { product: products[i % products.length], quantity: 2 }
      ];
      
      const subtotal = orderItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
      const taxAmount = subtotal * 0.05;
      const total = subtotal + taxAmount;
      
      const orderData = {
        table_number: `T${i + 20}`,
        order_type: 'dine_in',
        status: 'ready', // Ready for payment
        subtotal_amount: subtotal,
        tax_amount: taxAmount,
        discount_amount: 0,
        complementary_amount: 0,
        total_amount: total,
        employee_id: adminId,
        customer_id: null,
        notes: `Test order ${i + 1} for payment processing`
      };
      
      const { data: orderResult, error: orderError } = await supabase
        .from('orders')
        .insert(orderData)
        .select()
        .single();
      
      if (orderError) {
        console.error(`❌ Error creating order ${i + 1}:`, orderError);
        continue;
      }
      
      // Create order items
      const orderItemsData = orderItems.map(item => ({
        order_id: orderResult.id,
        product_id: item.product.id,
        quantity: item.quantity,
        unit_price_at_order: item.product.price,
        item_subtotal: item.product.price * item.quantity
      }));
      
      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItemsData);
      
      if (itemsError) {
        console.error(`❌ Error creating order items for order ${i + 1}:`, itemsError);
        continue;
      }
      
      testOrders.push({
        order: orderResult,
        items: orderItemsData,
        paymentMethod: paymentMethods[i]
      });
      
      console.log(`✅ Created order ${i + 1}: ${orderResult.id} - $${orderResult.total_amount.toFixed(2)} (${paymentMethods[i]})`);
    }
    
    // Test bills query (orders ready for payment)
    console.log('\n💰 Step 4: Test bills query...');
    
    const { data: billsOrders, error: billsError } = await supabase
      .from('orders')
      .select(`
        *,
        customers(full_name),
        order_items(
          id,
          product_id,
          quantity,
          unit_price_at_order,
          item_subtotal,
          products(name, description)
        )
      `)
      .in('status', ['pending', 'preparing', 'ready'])
      .order('created_at', { ascending: false });
    
    if (billsError) {
      console.error('❌ Error fetching bills:', billsError);
    } else {
      console.log(`✅ Bills query successful: ${billsOrders?.length || 0} orders ready for payment`);
      
      const readyOrders = billsOrders?.filter(order => order.status === 'ready') || [];
      console.log(`   Orders ready for payment: ${readyOrders.length}`);
    }
    
    // Process payments for each test order
    console.log('\n💳 Step 5: Process payments...');
    
    const processedPayments = [];
    
    for (const testOrder of testOrders) {
      console.log(`   Processing payment for order ${testOrder.order.id.slice(0, 8)} with ${testOrder.paymentMethod}...`);
      
      // Create payment record
      const paymentData = {
        order_id: testOrder.order.id,
        amount: testOrder.order.total_amount,
        payment_method: testOrder.paymentMethod,
        status: 'completed',
        transaction_reference: testOrder.paymentMethod === 'Card' ? `TXN_${Date.now()}_${Math.floor(Math.random() * 1000)}` : null,
        processed_by_employee_id: adminId
      };
      
      const { data: paymentResult, error: paymentError } = await supabase
        .from('payments')
        .insert(paymentData)
        .select()
        .single();
      
      if (paymentError) {
        console.error(`❌ Error creating payment:`, paymentError);
        continue;
      }
      
      // Update order status to completed
      const { error: orderUpdateError } = await supabase
        .from('orders')
        .update({
          status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', testOrder.order.id);
      
      if (orderUpdateError) {
        console.error(`❌ Error updating order status:`, orderUpdateError);
        continue;
      }
      
      processedPayments.push({
        order: testOrder.order,
        payment: paymentResult
      });
      
      console.log(`✅ Payment processed: $${paymentResult.amount.toFixed(2)} via ${paymentResult.payment_method}`);
    }
    
    // Test payment history query
    console.log('\n📊 Step 6: Test payment history...');
    
    const { data: paymentHistory, error: historyError } = await supabase
      .from('payments')
      .select(`
        *,
        orders(
          id,
          table_number,
          order_type,
          total_amount,
          created_at
        )
      `)
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (historyError) {
      console.error('❌ Error fetching payment history:', historyError);
    } else {
      console.log(`✅ Payment history query successful: ${paymentHistory?.length || 0} payments found`);
      
      const testPayments = paymentHistory?.filter(payment => 
        processedPayments.some(pp => pp.payment.id === payment.id)
      ) || [];
      
      console.log(`   Test payments in history: ${testPayments.length}`);
      testPayments.forEach(payment => {
        console.log(`   - ${payment.payment_method}: $${payment.amount.toFixed(2)} (${payment.status})`);
      });
    }
    
    // Test receipt generation data
    console.log('\n🧾 Step 7: Test receipt data generation...');
    
    for (const processedPayment of processedPayments.slice(0, 1)) { // Test one receipt
      const { data: receiptData, error: receiptError } = await supabase
        .from('orders')
        .select(`
          *,
          order_items(
            quantity,
            unit_price_at_order,
            item_subtotal,
            products(name, description)
          ),
          payments(
            amount,
            payment_method,
            transaction_reference,
            created_at
          )
        `)
        .eq('id', processedPayment.order.id)
        .single();
      
      if (receiptError) {
        console.error('❌ Error fetching receipt data:', receiptError);
      } else {
        console.log(`✅ Receipt data generated for order ${receiptData.id.slice(0, 8)}`);
        console.log(`   Items: ${receiptData.order_items?.length || 0}`);
        console.log(`   Payment: ${receiptData.payments?.[0]?.payment_method} - $${receiptData.payments?.[0]?.amount.toFixed(2)}`);
        console.log(`   Table: ${receiptData.table_number}`);
        console.log(`   Total: $${receiptData.total_amount.toFixed(2)}`);
      }
    }
    
    // Test payment method analytics
    console.log('\n📈 Step 8: Test payment analytics...');
    
    const { data: paymentStats, error: statsError } = await supabase
      .from('payments')
      .select('payment_method, amount')
      .eq('status', 'completed');
    
    if (statsError) {
      console.error('❌ Error fetching payment stats:', statsError);
    } else {
      const stats = paymentStats?.reduce((acc, payment) => {
        acc[payment.payment_method] = (acc[payment.payment_method] || 0) + Number(payment.amount);
        return acc;
      }, {}) || {};
      
      console.log(`✅ Payment method analytics:`);
      Object.entries(stats).forEach(([method, total]) => {
        console.log(`   ${method}: $${total.toFixed(2)}`);
      });
    }
    
    // Cleanup test data
    console.log('\n🧹 Step 9: Cleanup test data...');
    
    for (const processedPayment of processedPayments) {
      // Delete payment
      await supabase.from('payments').delete().eq('id', processedPayment.payment.id);
      
      // Delete order items
      await supabase.from('order_items').delete().eq('order_id', processedPayment.order.id);
      
      // Delete order
      await supabase.from('orders').delete().eq('id', processedPayment.order.id);
    }
    
    console.log('✅ Test data cleaned up');
    
    return true;
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    return false;
  } finally {
    await supabase.auth.signOut();
    console.log('\n👋 Signed out');
  }
}

// Run the test
testBillsAndPayment().then(success => {
  if (success) {
    console.log('\n🎉 Bills and Payment Processing Test PASSED!');
    console.log('\n✅ Summary:');
    console.log('   • Order creation for payment working');
    console.log('   • Bills query (ready orders) working');
    console.log('   • Payment processing working');
    console.log('   • Multiple payment methods supported');
    console.log('   • Order status updates working');
    console.log('   • Payment history tracking working');
    console.log('   • Receipt data generation working');
    console.log('   • Payment analytics working');
    console.log('   • Complete payment workflow functional');
  } else {
    console.log('\n❌ Bills and Payment Processing Test FAILED!');
  }
}).catch(error => {
  console.error('\n💥 Test execution failed:', error);
});
