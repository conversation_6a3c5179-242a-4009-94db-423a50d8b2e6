/**
 * Debug Loading Screen Issue
 * 
 * This script investigates why the application shows persistent loading screens
 * instead of actual page content by checking:
 * 1. Authentication state resolution
 * 2. Component rendering lifecycle
 * 3. Data fetching hooks
 * 4. JavaScript errors
 * 5. Route protection logic
 */

import puppeteer from 'puppeteer';

async function debugLoadingScreen() {
  console.log('🔍 Debugging Persistent Loading Screen Issue...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Capture all console messages
    const consoleMessages = [];
    const errors = [];
    const warnings = [];
    
    page.on('console', msg => {
      const text = msg.text();
      const type = msg.type();
      
      consoleMessages.push({ type, text, timestamp: Date.now() });
      
      if (type === 'error') {
        errors.push(text);
        console.log(`❌ Console Error: ${text}`);
      } else if (type === 'warning') {
        warnings.push(text);
        console.log(`⚠️  Console Warning: ${text}`);
      } else if (text.includes('AuthContext') || text.includes('Loading') || text.includes('Dashboard')) {
        console.log(`📝 ${type.toUpperCase()}: ${text}`);
      }
    });
    
    // Capture network failures
    page.on('requestfailed', request => {
      console.log(`🌐 Network Failed: ${request.url()} - ${request.failure().errorText}`);
    });
    
    // Capture unhandled promise rejections
    page.on('pageerror', error => {
      console.log(`💥 Page Error: ${error.message}`);
      errors.push(`Page Error: ${error.message}`);
    });
    
    console.log('🌐 Step 1: Navigate to application...');
    await page.goto('http://localhost:5174/', { waitUntil: 'networkidle0' });
    
    // Wait for initial load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🔐 Step 2: Check initial authentication state...');
    
    const initialState = await page.evaluate(() => {
      return {
        url: window.location.href,
        title: document.title,
        hasLoginForm: !!document.querySelector('input[type="text"]'),
        hasLoadingText: document.body.textContent.includes('Loading'),
        bodyTextSample: document.body.textContent.substring(0, 500),
        reactRootExists: !!document.getElementById('root'),
        reactRootContent: document.getElementById('root')?.innerHTML.length || 0
      };
    });
    
    console.log('Initial state:', initialState);
    
    if (!initialState.hasLoginForm) {
      console.log('❌ Login form not found - possible rendering issue');
      return false;
    }
    
    console.log('\n🔑 Step 3: Perform login and monitor state changes...');
    
    // Fill login form
    await page.type('input[type="text"]', 'ADMIN001');
    await page.type('input[type="password"]', 'admin123');
    
    console.log('   Credentials entered, clicking login...');
    
    // Monitor state changes during login
    let stateChecks = 0;
    const maxChecks = 30; // 30 seconds
    
    await page.click('button[type="submit"]');
    
    while (stateChecks < maxChecks) {
      stateChecks++;
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const currentState = await page.evaluate(() => {
        return {
          url: window.location.href,
          hasLoadingText: document.body.textContent.includes('Loading'),
          hasLoginForm: !!document.querySelector('input[type="text"]'),
          hasDashboardContent: document.body.textContent.includes('Dashboard') || 
                              document.body.textContent.includes('Today\'s Sales') ||
                              document.body.textContent.includes('Monthly Income'),
          hasMenuContent: document.body.textContent.includes('Menu & Orders'),
          bodyTextLength: document.body.textContent.length,
          reactErrors: window.__REACT_ERROR_OVERLAY_GLOBAL_HOOK__?.errors || [],
          authContextState: window.__AUTH_DEBUG__ || 'Not available'
        };
      });
      
      console.log(`   Check ${stateChecks}: URL=${currentState.url.split('/').pop() || 'root'}, Loading=${currentState.hasLoadingText}, Dashboard=${currentState.hasDashboardContent}, TextLength=${currentState.bodyTextLength}`);
      
      // Check if we've successfully navigated away from login
      if (!currentState.hasLoginForm && !currentState.hasLoadingText) {
        if (currentState.hasDashboardContent || currentState.hasMenuContent) {
          console.log('✅ Successfully loaded content!');
          break;
        }
      }
      
      // Check for stuck loading state
      if (currentState.hasLoadingText && stateChecks > 10) {
        console.log('⚠️  Stuck in loading state, investigating...');
        
        // Get more detailed state
        const detailedState = await page.evaluate(() => {
          // Try to access React DevTools or component state
          const reactFiber = document.getElementById('root')?._reactInternalFiber ||
                            document.getElementById('root')?._reactInternals;
          
          return {
            reactFiberExists: !!reactFiber,
            documentReadyState: document.readyState,
            windowLoaded: window.performance?.timing?.loadEventEnd > 0,
            supabaseClient: typeof window.supabase !== 'undefined',
            authUser: window.__SUPABASE_AUTH_USER__ || 'Not available',
            componentTree: document.getElementById('root')?.innerHTML.substring(0, 200)
          };
        });
        
        console.log('   Detailed state:', detailedState);
      }
      
      // If we're still on login page after 15 seconds, there's an issue
      if (currentState.hasLoginForm && stateChecks > 15) {
        console.log('❌ Still on login page after 15 seconds - authentication failed');
        break;
      }
    }
    
    console.log('\n🔍 Step 4: Analyze final state...');
    
    const finalState = await page.evaluate(() => {
      return {
        url: window.location.href,
        title: document.title,
        hasLoadingText: document.body.textContent.includes('Loading'),
        contentType: document.body.textContent.includes('Dashboard') ? 'dashboard' :
                    document.body.textContent.includes('Menu') ? 'menu' :
                    document.body.textContent.includes('Login') ? 'login' : 'unknown',
        bodyTextSample: document.body.textContent.substring(0, 300),
        elementCount: document.querySelectorAll('*').length,
        hasReactComponents: document.querySelectorAll('[data-reactroot], [data-react-helmet]').length > 0
      };
    });
    
    console.log('Final state:', finalState);
    
    console.log('\n📊 Step 5: Error analysis...');
    
    console.log(`Total console messages: ${consoleMessages.length}`);
    console.log(`Errors: ${errors.length}`);
    console.log(`Warnings: ${warnings.length}`);
    
    if (errors.length > 0) {
      console.log('\n❌ JavaScript Errors Found:');
      errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    if (warnings.length > 0) {
      console.log('\n⚠️  Warnings Found:');
      warnings.slice(0, 5).forEach((warning, index) => {
        console.log(`   ${index + 1}. ${warning}`);
      });
    }
    
    // Check for specific loading issues
    console.log('\n🔍 Step 6: Specific issue detection...');
    
    const authErrors = consoleMessages.filter(msg => 
      msg.text.includes('auth') && msg.type === 'error'
    );
    
    const networkErrors = consoleMessages.filter(msg => 
      msg.text.includes('fetch') || msg.text.includes('network') || msg.text.includes('supabase')
    );
    
    const reactErrors = consoleMessages.filter(msg => 
      msg.text.includes('React') || msg.text.includes('component') || msg.text.includes('render')
    );
    
    console.log(`Auth-related errors: ${authErrors.length}`);
    console.log(`Network-related issues: ${networkErrors.length}`);
    console.log(`React-related issues: ${reactErrors.length}`);
    
    // Determine root cause
    let rootCause = 'Unknown';
    
    if (finalState.hasLoadingText) {
      if (authErrors.length > 0) {
        rootCause = 'Authentication state not resolving';
      } else if (networkErrors.length > 0) {
        rootCause = 'Network/API issues preventing data load';
      } else if (reactErrors.length > 0) {
        rootCause = 'React component rendering issues';
      } else {
        rootCause = 'Data fetching hooks stuck in loading state';
      }
    } else if (finalState.contentType === 'login') {
      rootCause = 'Authentication failed - credentials or auth flow issue';
    } else if (finalState.contentType === 'unknown') {
      rootCause = 'Route protection logic preventing page display';
    } else {
      rootCause = 'No issue detected - content loaded successfully';
    }
    
    console.log(`\n🎯 Root Cause Analysis: ${rootCause}`);
    
    return {
      success: finalState.contentType !== 'unknown' && !finalState.hasLoadingText,
      rootCause,
      finalState,
      errorCount: errors.length,
      warningCount: warnings.length
    };
    
  } catch (error) {
    console.error('💥 Debug test failed:', error);
    return { success: false, error: error.message };
  } finally {
    // Keep browser open for manual inspection
    console.log('\n🔍 Browser left open for manual inspection...');
    console.log('Press Ctrl+C to close when done debugging');
    
    // Wait indefinitely for manual inspection
    await new Promise(() => {});
  }
}

// Run the debug test
debugLoadingScreen().catch(error => {
  console.error('\n💥 Debug execution failed:', error);
  process.exit(1);
});
