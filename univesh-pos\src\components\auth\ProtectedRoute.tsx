import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { Box, CircularProgress, Typography } from '@mui/material'
import { useAuth } from '../../contexts/AuthContext'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'admin' | 'manager' | 'cashier'
  allowedRoles?: ('admin' | 'manager' | 'cashier')[]
}

const LoadingScreen: React.FC = () => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      backgroundColor: '#f5f5f5'
    }}
  >
    <CircularProgress size={60} sx={{ color: '#1049B8', mb: 2 }} />
    <Typography
      variant="h6"
      sx={{
        color: '#1049B8',
        fontFamily: 'Gilroy, sans-serif',
        fontWeight: 600
      }}
    >
      Loading Univesh POS...
    </Typography>
  </Box>
)

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  allowedRoles
}) => {
  const { user, profile, loading } = useAuth()
  const location = useLocation()

  // Show loading screen while checking authentication
  if (loading) {
    return <LoadingScreen />
  }

  // Redirect to login if not authenticated
  if (!user || !profile) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // Check role-based access
  if (requiredRole && profile.role_name !== requiredRole) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
          p: 3
        }}
      >
        <Typography
          variant="h4"
          sx={{
            color: '#E15F71',
            fontFamily: 'Gilroy, sans-serif',
            fontWeight: 700,
            mb: 2
          }}
        >
          Access Denied
        </Typography>
        <Typography
          variant="body1"
          sx={{
            color: 'text.secondary',
            fontFamily: 'Gilroy, sans-serif',
            textAlign: 'center'
          }}
        >
          You don't have permission to access this page.
          <br />
          Required role: {requiredRole}
          <br />
          Your role: {profile.role_name}
        </Typography>
      </Box>
    )
  }

  // Check allowed roles
  if (allowedRoles && !allowedRoles.includes(profile.role_name!)) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
          p: 3
        }}
      >
        <Typography
          variant="h4"
          sx={{
            color: '#E15F71',
            fontFamily: 'Gilroy, sans-serif',
            fontWeight: 700,
            mb: 2
          }}
        >
          Access Denied
        </Typography>
        <Typography
          variant="body1"
          sx={{
            color: 'text.secondary',
            fontFamily: 'Gilroy, sans-serif',
            textAlign: 'center'
          }}
        >
          You don't have permission to access this page.
          <br />
          Allowed roles: {allowedRoles.join(', ')}
          <br />
          Your role: {profile.role_name}
        </Typography>
      </Box>
    )
  }

  return <>{children}</>
}

export default ProtectedRoute
