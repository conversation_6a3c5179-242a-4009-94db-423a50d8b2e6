/**
 * MenuScreen with Error Boundary Wrapper
 * 
 * This component wraps the MenuScreen with a comprehensive error boundary
 * to handle React errors and provide graceful fallback UI.
 */

import React from 'react'
import ErrorBoundary from '../components/error/ErrorBoundary'
import MenuScreen from './MenuScreen'
import { Box, Typography, Button, Alert } from '@mui/material'
import { Restaurant, Refresh } from '@mui/icons-material'
import { colors } from '../theme'

// Custom fallback UI for MenuScreen errors
const MenuScreenErrorFallback: React.FC = () => {
  const handleRetry = () => {
    window.location.reload()
  }

  const handleGoBack = () => {
    window.history.back()
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
        Menu & Orders
      </Typography>
      
      <Alert 
        severity="error" 
        sx={{ mb: 3 }}
        action={
          <Button 
            color="inherit" 
            size="small" 
            onClick={handleRetry}
            startIcon={<Refresh />}
          >
            Retry
          </Button>
        }
      >
        Unable to load the menu. There was an error displaying the menu items.
      </Alert>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 300,
          textAlign: 'center',
          bgcolor: colors.grey[50],
          borderRadius: 2,
          p: 4
        }}
      >
        <Restaurant 
          sx={{ 
            fontSize: 64, 
            color: colors.grey[400], 
            mb: 2 
          }} 
        />
        
        <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
          Menu Temporarily Unavailable
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          We're having trouble loading the menu items. Please try refreshing the page or contact support if the problem persists.
        </Typography>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            startIcon={<Refresh />}
            onClick={handleRetry}
          >
            Refresh Page
          </Button>
          
          <Button
            variant="outlined"
            onClick={handleGoBack}
          >
            Go Back
          </Button>
        </Box>
      </Box>
    </Box>
  )
}

// Error handler for MenuScreen
const handleMenuScreenError = (error: Error, errorInfo: React.ErrorInfo) => {
  console.error('MenuScreen Error:', {
    error: error.message,
    stack: error.stack,
    componentStack: errorInfo.componentStack,
    timestamp: new Date().toISOString()
  })

  // In production, send to error monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Example: errorMonitoringService.captureException(error, {
    //   tags: { component: 'MenuScreen' },
    //   extra: errorInfo
    // })
  }
}

// Main component with error boundary
const MenuScreenWithErrorBoundary: React.FC = () => {
  return (
    <ErrorBoundary
      fallback={<MenuScreenErrorFallback />}
      onError={handleMenuScreenError}
      componentName="MenuScreen"
      showDetails={process.env.NODE_ENV === 'development'}
    >
      <MenuScreen />
    </ErrorBoundary>
  )
}

export default MenuScreenWithErrorBoundary
