import React from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert
} from '@mui/material'
import {
  TrendingUp,
  TrendingDown,
  AttachMoney,
  ShoppingCart,
  PersonAdd,
  Restaurant
} from '@mui/icons-material'
import { colors } from '../theme'
import { useDashboardData } from '../hooks/useDashboardData'
import LoadingState from '../components/common/LoadingState'
import NetworkErrorHandler from '../components/common/NetworkErrorHandler'

interface MetricCardProps {
  title: string
  value: string
  change: string
  trend: 'up' | 'down'
  icon: React.ReactElement
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, change, trend, icon }) => {
  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box
            sx={{
              p: 1,
              borderRadius: 2,
              backgroundColor: colors.primary.main,
              color: colors.primary.contrastText,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {icon}
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            {trend === 'up' ? (
              <TrendingUp sx={{ color: colors.success.main, fontSize: 20 }} />
            ) : (
              <TrendingDown sx={{ color: colors.error.main, fontSize: 20 }} />
            )}
            <Typography
              variant="body2"
              sx={{
                color: trend === 'up' ? colors.success.main : colors.error.main,
                fontWeight: 600
              }}
            >
              {change}
            </Typography>
          </Box>
        </Box>
        <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
          {value}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {title}
        </Typography>
      </CardContent>
    </Card>
  )
}

const Dashboard: React.FC = () => {
  const { metrics, trendingDishes, bestEmployees, loading, error } = useDashboardData()

  if (loading) {
    return (
      <Box>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
          Dashboard Overview
        </Typography>
        <LoadingState variant="dashboard" message="Loading dashboard data..." />
      </Box>
    )
  }

  if (error) {
    return (
      <Box>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
          Dashboard Overview
        </Typography>
        <NetworkErrorHandler
          error={error}
          onRetry={() => window.location.reload()}
          variant="card"
          showDetails={process.env.NODE_ENV === 'development'}
        />
      </Box>
    )
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
        Dashboard Overview
      </Typography>

      {/* Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Daily Sales"
            value={metrics.dailySales.value}
            change={metrics.dailySales.change}
            trend={metrics.dailySales.trend}
            icon={<AttachMoney />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Income (30 days)"
            value={metrics.totalIncome.value}
            change={metrics.totalIncome.change}
            trend={metrics.totalIncome.trend}
            icon={<TrendingUp />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Today's Orders"
            value={metrics.totalOrders.value}
            change={metrics.totalOrders.change}
            trend={metrics.totalOrders.trend}
            icon={<ShoppingCart />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="New Customers"
            value={metrics.newCustomers.value}
            change={metrics.newCustomers.change}
            trend={metrics.newCustomers.trend}
            icon={<PersonAdd />}
          />
        </Grid>
      </Grid>

      {/* Charts and Tables */}
      <Grid container spacing={3}>
        {/* Trending Dishes */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <Restaurant sx={{ color: colors.primary.main }} />
                Trending Dishes
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Dish Name</TableCell>
                      <TableCell align="right">Sales</TableCell>
                      <TableCell align="right">Qty</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {trendingDishes.length > 0 ? (
                      trendingDishes.map((dish, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {dish.name}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2" sx={{ fontWeight: 600, color: colors.success.main }}>
                              {dish.sales}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Chip
                              label={dish.quantity}
                              size="small"
                              sx={{
                                backgroundColor: colors.primary.main,
                                color: colors.primary.contrastText,
                                fontWeight: 600
                              }}
                            />
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={3} align="center">
                          <Typography variant="body2" color="text.secondary">
                            No sales data available for today
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Best Employees */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUp sx={{ color: colors.success.main }} />
                Best Employees
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Employee</TableCell>
                      <TableCell align="right">Sales</TableCell>
                      <TableCell align="right">Orders</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {bestEmployees.map((employee, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {employee.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {employee.role}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" sx={{ fontWeight: 600, color: colors.success.main }}>
                            {employee.sales}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Chip
                            label={employee.orders}
                            size="small"
                            sx={{
                              backgroundColor: colors.secondary.main,
                              color: colors.secondary.contrastText,
                              fontWeight: 600
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Dashboard
