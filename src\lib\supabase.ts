import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://kufsqfbiilphymiehwet.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NTEzMzIsImV4cCI6MjA1MDAyNzMzMn0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database types
export interface Database {
  public: {
    Tables: {
      roles: {
        Row: {
          id: string
          role_name: 'admin' | 'manager' | 'cashier'
          created_at: string
        }
      }
      profiles: {
        Row: {
          id: string
          role_id: string
          sales_id_number: string
          full_name: string
          email: string
          phone_number?: string
          date_of_birth?: string
          designation?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          role_id: string
          sales_id_number: string
          full_name: string
          email: string
          phone_number?: string
          date_of_birth?: string
          designation?: string
        }
        Update: {
          role_id?: string
          sales_id_number?: string
          full_name?: string
          email?: string
          phone_number?: string
          date_of_birth?: string
          designation?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          description?: string
          created_at: string
          updated_at: string
        }
      }
      products: {
        Row: {
          id: string
          name: string
          description?: string
          price: number
          category_id?: string
          current_stock: number
          image_url?: string
          is_active: boolean
          created_at: string
          updated_at: string
        }
      }
      orders: {
        Row: {
          id: string
          table_number?: string
          order_type: 'dine_in' | 'delivery' | 'take_away'
          status: 'draft' | 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled'
          subtotal_amount: number
          tax_amount: number
          discount_amount: number
          complementary_amount: number
          total_amount: number
          employee_id: string
          customer_id?: string
          notes?: string
          created_at: string
          updated_at: string
        }
      }
      customers: {
        Row: {
          id: string
          full_name: string
          email?: string
          phone_number?: string
          billing_address?: string
          shipping_address?: string
          created_at: string
          updated_at: string
        }
      }
    }
    Views: {
      dashboard_daily_sales: {
        Row: {
          sale_date: string
          total_orders: number
          total_revenue: number
          average_order_value: number
        }
      }
      trending_dishes: {
        Row: {
          id: string
          name: string
          price: number
          total_quantity_sold: number
          total_revenue: number
          order_count: number
        }
      }
    }
  }
}
