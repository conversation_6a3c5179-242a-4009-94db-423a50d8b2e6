// Critical User Journey Tests for Production Validation
import ProductionTestRunner, { TestUtils } from '../utils/testRunner'
import { supabase } from '../lib/supabase'

class CriticalJourneyTests {
  private testRunner: ProductionTestRunner
  private testData = {
    admin: { salesId: 'ADMIN001', password: 'Admin@123' },
    manager: { salesId: 'MGR001', password: 'Manager@123' },
    cashier: { salesId: 'CASH001', password: 'Cashier@123' },
    testCustomer: {
      name: 'Test Customer',
      email: '<EMAIL>',
      phone: '(*************',
      address: '123 Test Street'
    },
    testProduct: {
      name: 'Test Coffee',
      price: 4.99,
      category: 'Beverages'
    }
  }

  constructor() {
    this.testRunner = new ProductionTestRunner()
  }

  // Execute all critical journey tests
  async runAllTests(): Promise<void> {
    try {
      await this.testAuthenticationFlow()
      await this.testOrderProcessingFlow()
      await this.testProductManagementFlow()
      await this.testCustomerManagementFlow()
      await this.testPaymentProcessingFlow()
      await this.testReportsAndAnalytics()
      await this.testErrorHandling()
      await this.testSecurityValidation()
      
      console.log('🎉 All critical journey tests completed!')
      console.log(this.testRunner.generateReport())
    } catch (error) {
      console.error('❌ Critical test failure:', error)
      throw error
    }
  }

  // Test 1: Authentication and Authorization Flow
  async testAuthenticationFlow(): Promise<void> {
    this.testRunner.startSuite('Authentication & Authorization')

    // Test valid login
    await this.testRunner.runTest('Admin Login Success', async () => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'Admin@123'
      })
      
      if (error) throw new Error(`Login failed: ${error.message}`)
      if (!data.user) throw new Error('No user data returned')
      
      // Verify session
      const { data: session } = await supabase.auth.getSession()
      if (!session.session) throw new Error('No active session')
    })

    // Test invalid login
    await this.testRunner.runTest('Invalid Login Rejection', async () => {
      const { error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
      
      if (!error) throw new Error('Invalid login should have failed')
    })

    // Test role-based access
    await this.testRunner.runTest('Role-Based Access Control', async () => {
      // This would test UI elements and navigation restrictions
      // For now, we'll test the profile data structure
      const { data: profile } = await supabase
        .from('profiles')
        .select('*, role:roles(*)')
        .single()
      
      if (!profile?.role) throw new Error('User role not found')
    })

    // Test logout
    await this.testRunner.runTest('Logout Functionality', async () => {
      const { error } = await supabase.auth.signOut()
      if (error) throw new Error(`Logout failed: ${error.message}`)
      
      const { data: session } = await supabase.auth.getSession()
      if (session.session) throw new Error('Session should be cleared after logout')
    })

    this.testRunner.finishSuite()
  }

  // Test 2: Complete Order Processing Flow
  async testOrderProcessingFlow(): Promise<void> {
    this.testRunner.startSuite('Order Processing Flow')

    // Login first
    await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Admin@123'
    })

    await this.testRunner.runTest('Create New Order', async () => {
      const { data: order, error } = await supabase
        .from('orders')
        .insert({
          table_number: 'T001',
          order_type: 'dine_in',
          status: 'draft',
          subtotal_amount: 0,
          tax_amount: 0,
          total_amount: 0,
          employee_id: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single()

      if (error) throw new Error(`Order creation failed: ${error.message}`)
      if (!order) throw new Error('No order data returned')
    })

    await this.testRunner.runTest('Add Items to Order', async () => {
      // Get a test product
      const { data: product } = await supabase
        .from('products')
        .select('*')
        .limit(1)
        .single()

      if (!product) throw new Error('No products available for testing')

      // Get the latest order
      const { data: order } = await supabase
        .from('orders')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (!order) throw new Error('No order found')

      // Add item to order
      const { error } = await supabase
        .from('order_items')
        .insert({
          order_id: order.id,
          product_id: product.id,
          quantity: 2,
          unit_price_at_order: product.price,
          item_subtotal: product.price * 2
        })

      if (error) throw new Error(`Adding item failed: ${error.message}`)
    })

    await this.testRunner.runTest('Calculate Order Totals', async () => {
      // Get order with items
      const { data: order } = await supabase
        .from('orders')
        .select(`
          *,
          order_items(*)
        `)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (!order) throw new Error('No order found')

      const subtotal = order.order_items?.reduce((sum: number, item: any) => 
        sum + item.item_subtotal, 0) || 0
      const taxAmount = subtotal * 0.05
      const total = subtotal + taxAmount

      // Update order totals
      const { error } = await supabase
        .from('orders')
        .update({
          subtotal_amount: subtotal,
          tax_amount: taxAmount,
          total_amount: total,
          status: 'pending'
        })
        .eq('id', order.id)

      if (error) throw new Error(`Order update failed: ${error.message}`)
    })

    this.testRunner.finishSuite()
  }

  // Test 3: Product Management Flow
  async testProductManagementFlow(): Promise<void> {
    this.testRunner.startSuite('Product Management')

    await this.testRunner.runTest('Create Product Category', async () => {
      const { data: category, error } = await supabase
        .from('categories')
        .insert({
          name: 'Test Category',
          description: 'Test category for validation',
          is_active: true
        })
        .select()
        .single()

      if (error) throw new Error(`Category creation failed: ${error.message}`)
      if (!category) throw new Error('No category data returned')
    })

    await this.testRunner.runTest('Create Product', async () => {
      // Get test category
      const { data: category } = await supabase
        .from('categories')
        .select('*')
        .eq('name', 'Test Category')
        .single()

      if (!category) throw new Error('Test category not found')

      const { data: product, error } = await supabase
        .from('products')
        .insert({
          name: 'Test Product',
          description: 'Test product for validation',
          price: 9.99,
          category_id: category.id,
          is_active: true,
          stock_quantity: 100
        })
        .select()
        .single()

      if (error) throw new Error(`Product creation failed: ${error.message}`)
      if (!product) throw new Error('No product data returned')
    })

    await this.testRunner.runTest('Update Product', async () => {
      const { data: product } = await supabase
        .from('products')
        .select('*')
        .eq('name', 'Test Product')
        .single()

      if (!product) throw new Error('Test product not found')

      const { error } = await supabase
        .from('products')
        .update({
          price: 12.99,
          description: 'Updated test product'
        })
        .eq('id', product.id)

      if (error) throw new Error(`Product update failed: ${error.message}`)
    })

    this.testRunner.finishSuite()
  }

  // Test 4: Customer Management Flow
  async testCustomerManagementFlow(): Promise<void> {
    this.testRunner.startSuite('Customer Management')

    await this.testRunner.runTest('Create Customer', async () => {
      const { data: customer, error } = await supabase
        .from('customers')
        .insert({
          name: this.testData.testCustomer.name,
          email: this.testData.testCustomer.email,
          phone: this.testData.testCustomer.phone,
          address: this.testData.testCustomer.address
        })
        .select()
        .single()

      if (error) throw new Error(`Customer creation failed: ${error.message}`)
      if (!customer) throw new Error('No customer data returned')
    })

    await this.testRunner.runTest('Search Customer', async () => {
      const { data: customers, error } = await supabase
        .from('customers')
        .select('*')
        .ilike('name', '%Test Customer%')

      if (error) throw new Error(`Customer search failed: ${error.message}`)
      if (!customers || customers.length === 0) {
        throw new Error('Customer not found in search')
      }
    })

    await this.testRunner.runTest('Update Customer', async () => {
      const { data: customer } = await supabase
        .from('customers')
        .select('*')
        .eq('email', this.testData.testCustomer.email)
        .single()

      if (!customer) throw new Error('Test customer not found')

      const { error } = await supabase
        .from('customers')
        .update({
          phone: '(*************'
        })
        .eq('id', customer.id)

      if (error) throw new Error(`Customer update failed: ${error.message}`)
    })

    this.testRunner.finishSuite()
  }

  // Test 5: Payment Processing Flow
  async testPaymentProcessingFlow(): Promise<void> {
    this.testRunner.startSuite('Payment Processing')

    await this.testRunner.runTest('Process Cash Payment', async () => {
      // Get a pending order
      const { data: order } = await supabase
        .from('orders')
        .select('*')
        .eq('status', 'pending')
        .limit(1)
        .single()

      if (!order) throw new Error('No pending order found')

      // Create payment
      const { data: payment, error } = await supabase
        .from('payments')
        .insert({
          order_id: order.id,
          payment_method: 'Cash',
          amount_paid: order.total_amount,
          processed_by_employee_id: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single()

      if (error) throw new Error(`Payment creation failed: ${error.message}`)
      if (!payment) throw new Error('No payment data returned')

      // Update order status
      const { error: updateError } = await supabase
        .from('orders')
        .update({ status: 'completed' })
        .eq('id', order.id)

      if (updateError) throw new Error(`Order completion failed: ${updateError.message}`)
    })

    this.testRunner.finishSuite()
  }

  // Test 6: Reports and Analytics
  async testReportsAndAnalytics(): Promise<void> {
    this.testRunner.startSuite('Reports & Analytics')

    await this.testRunner.runTest('Generate Sales Report', async () => {
      const today = new Date().toISOString().split('T')[0]
      
      const { data: salesData, error } = await supabase
        .from('orders')
        .select(`
          *,
          payments(*),
          order_items(*)
        `)
        .gte('created_at', `${today}T00:00:00`)
        .lte('created_at', `${today}T23:59:59`)

      if (error) throw new Error(`Sales report failed: ${error.message}`)
      // Data can be empty, that's okay for testing
    })

    await this.testRunner.runTest('Payment Method Analysis', async () => {
      const { data: paymentData, error } = await supabase
        .from('payments')
        .select('payment_method, amount_paid')

      if (error) throw new Error(`Payment analysis failed: ${error.message}`)
      // Data can be empty, that's okay for testing
    })

    this.testRunner.finishSuite()
  }

  // Test 7: Error Handling
  async testErrorHandling(): Promise<void> {
    this.testRunner.startSuite('Error Handling')

    await this.testRunner.runTest('Invalid Data Handling', async () => {
      // Try to create order with invalid data
      const { error } = await supabase
        .from('orders')
        .insert({
          table_number: null,
          order_type: 'invalid_type',
          status: 'invalid_status'
        })

      // Should fail due to constraints
      if (!error) throw new Error('Invalid data should have been rejected')
    })

    await this.testRunner.runTest('Duplicate Key Handling', async () => {
      // Try to create duplicate category
      const { error } = await supabase
        .from('categories')
        .insert({
          name: 'Test Category', // Already exists
          description: 'Duplicate test'
        })

      // Should fail due to unique constraint
      if (!error) throw new Error('Duplicate data should have been rejected')
    })

    this.testRunner.finishSuite()
  }

  // Test 8: Security Validation
  async testSecurityValidation(): Promise<void> {
    this.testRunner.startSuite('Security Validation')

    await this.testRunner.runTest('RLS Policy Enforcement', async () => {
      // Test that users can only access their own data
      const { data: userOrders } = await supabase
        .from('orders')
        .select('*')

      // Should only return orders for current user (if RLS is working)
      // This is a basic test - in production you'd test with different users
    })

    await this.testRunner.runTest('Input Sanitization', async () => {
      // Test XSS prevention
      const maliciousInput = '<script>alert("xss")</script>'
      
      const { data: category, error } = await supabase
        .from('categories')
        .insert({
          name: maliciousInput,
          description: 'XSS test'
        })
        .select()
        .single()

      if (error) throw new Error(`Category creation failed: ${error.message}`)
      
      // Check that script tags are not executed (they should be stored as text)
      if (category && category.name.includes('<script>')) {
        // This is actually okay - the data is stored, but should not be executed in UI
      }
    })

    this.testRunner.finishSuite()
  }

  // Cleanup test data
  async cleanup(): Promise<void> {
    try {
      // Clean up test data
      await supabase.from('payments').delete().ilike('order_id', '%')
      await supabase.from('order_items').delete().ilike('order_id', '%')
      await supabase.from('orders').delete().ilike('table_number', 'T001')
      await supabase.from('products').delete().eq('name', 'Test Product')
      await supabase.from('categories').delete().eq('name', 'Test Category')
      await supabase.from('customers').delete().eq('email', this.testData.testCustomer.email)
      
      console.log('🧹 Test cleanup completed')
    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error)
    }
  }
}

export default CriticalJourneyTests
