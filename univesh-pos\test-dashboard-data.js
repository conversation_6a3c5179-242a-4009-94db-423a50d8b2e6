/**
 * Test Dashboard Data Loading
 * 
 * This script tests the dashboard data fetching functionality
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testDashboardData() {
  console.log('🧪 Testing Dashboard Data Loading...\n');
  
  try {
    // First, authenticate
    console.log('🔐 Step 1: Authenticate...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (authError) {
      console.error('❌ Authentication failed:', authError);
      return false;
    }
    
    console.log('✅ Authenticated successfully');
    
    // Test daily sales data
    console.log('\n📊 Step 2: Test daily sales data...');
    
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
    
    const { data: todayOrders, error: todayError } = await supabase
      .from('orders')
      .select('total_amount')
      .eq('status', 'completed')
      .gte('created_at', startOfDay.toISOString())
      .lt('created_at', endOfDay.toISOString());
    
    if (todayError) {
      console.error('❌ Error fetching today orders:', todayError);
    } else {
      console.log(`✅ Today's orders: ${todayOrders?.length || 0} orders`);
      const todaySales = todayOrders?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0;
      console.log(`💰 Today's sales: $${todaySales.toFixed(2)}`);
    }
    
    // Test trending dishes view
    console.log('\n🍽️ Step 3: Test trending dishes view...');
    
    const { data: trendingDishes, error: dishesError } = await supabase
      .from('trending_dishes')
      .select('*')
      .limit(5);
    
    if (dishesError) {
      console.error('❌ Error fetching trending dishes:', dishesError);
    } else {
      console.log(`✅ Trending dishes: ${trendingDishes?.length || 0} dishes found`);
      if (trendingDishes && trendingDishes.length > 0) {
        trendingDishes.forEach((dish, index) => {
          console.log(`   ${index + 1}. ${dish.name} - $${dish.total_revenue?.toFixed(2)} (${dish.total_quantity_sold} sold)`);
        });
      } else {
        console.log('   No trending dishes data available');
      }
    }
    
    // Test best employees view
    console.log('\n👥 Step 4: Test best employees view...');
    
    const { data: bestEmployees, error: employeesError } = await supabase
      .from('best_employees')
      .select('*')
      .limit(4);
    
    if (employeesError) {
      console.error('❌ Error fetching best employees:', employeesError);
    } else {
      console.log(`✅ Best employees: ${bestEmployees?.length || 0} employees found`);
      if (bestEmployees && bestEmployees.length > 0) {
        bestEmployees.forEach((employee, index) => {
          console.log(`   ${index + 1}. ${employee.full_name} (${employee.designation}) - $${employee.total_sales?.toFixed(2)} (${employee.total_orders} orders)`);
        });
      } else {
        console.log('   No best employees data available');
      }
    }
    
    // Test customers count
    console.log('\n👤 Step 5: Test customers count...');
    
    const { count: customersCount, error: customersError } = await supabase
      .from('customers')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startOfDay.toISOString())
      .lt('created_at', endOfDay.toISOString());
    
    if (customersError) {
      console.error('❌ Error fetching customers count:', customersError);
    } else {
      console.log(`✅ New customers today: ${customersCount || 0}`);
    }
    
    // Test monthly income
    console.log('\n💰 Step 6: Test monthly income...');
    
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    const { data: monthlyOrders, error: monthlyError } = await supabase
      .from('orders')
      .select('total_amount')
      .eq('status', 'completed')
      .gte('created_at', thirtyDaysAgo.toISOString());
    
    if (monthlyError) {
      console.error('❌ Error fetching monthly orders:', monthlyError);
    } else {
      const totalIncome = monthlyOrders?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0;
      console.log(`✅ Monthly income (30 days): $${totalIncome.toFixed(2)} from ${monthlyOrders?.length || 0} orders`);
    }
    
    // Test if we have any sample data
    console.log('\n📋 Step 7: Check for sample data...');
    
    const { data: allOrders, error: allOrdersError } = await supabase
      .from('orders')
      .select('*')
      .limit(5);
    
    if (allOrdersError) {
      console.error('❌ Error fetching all orders:', allOrdersError);
    } else {
      console.log(`📊 Total orders in system: ${allOrders?.length || 0} (showing first 5)`);
      if (allOrders && allOrders.length > 0) {
        allOrders.forEach((order, index) => {
          console.log(`   ${index + 1}. Order ${order.id} - $${order.total_amount} (${order.status})`);
        });
      }
    }
    
    const { data: allProducts, error: allProductsError } = await supabase
      .from('products')
      .select('*')
      .limit(3);
    
    if (allProductsError) {
      console.error('❌ Error fetching products:', allProductsError);
    } else {
      console.log(`🍽️ Products in system: ${allProducts?.length || 0} (showing first 3)`);
      if (allProducts && allProducts.length > 0) {
        allProducts.forEach((product, index) => {
          console.log(`   ${index + 1}. ${product.name} - $${product.price}`);
        });
      }
    }
    
    console.log('\n🔍 Step 8: Test dashboard views directly...');
    
    // Test dashboard_daily_sales view
    const { data: dailySalesView, error: dailySalesError } = await supabase
      .from('dashboard_daily_sales')
      .select('*');
    
    if (dailySalesError) {
      console.error('❌ Error fetching dashboard_daily_sales view:', dailySalesError);
    } else {
      console.log(`📊 Dashboard daily sales view: ${dailySalesView?.length || 0} records`);
    }
    
    return true;
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    return false;
  } finally {
    // Sign out
    await supabase.auth.signOut();
    console.log('\n👋 Signed out');
  }
}

// Run the test
testDashboardData().then(success => {
  if (success) {
    console.log('\n🎉 Dashboard Data Test COMPLETED!');
    console.log('✅ All dashboard data queries tested');
  } else {
    console.log('\n❌ Dashboard Data Test FAILED!');
  }
}).catch(error => {
  console.error('\n💥 Test execution failed:', error);
});
