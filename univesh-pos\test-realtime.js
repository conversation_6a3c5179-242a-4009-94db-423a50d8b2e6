// Real-time Subscription Test Script
// Tests Supabase real-time functionality for the POS system

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testRealtimeSubscriptions() {
  console.log('🔄 Testing Real-time Subscriptions...')
  console.log('=' .repeat(50))

  // Sign in first
  const { error: signInError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123'
  })

  if (signInError) {
    console.error('❌ Failed to sign in:', signInError)
    return
  }

  console.log('✅ Signed in successfully')

  // Test 1: Orders table subscription (critical for POS)
  console.log('\n1️⃣ Testing orders table subscription...')
  
  let orderUpdates = 0
  const orderSubscription = supabase
    .channel('orders-channel')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'orders' },
      (payload) => {
        orderUpdates++
        console.log(`📦 Order update #${orderUpdates}:`, payload.eventType, payload.new?.id || payload.old?.id)
      }
    )
    .subscribe((status) => {
      console.log('📡 Orders subscription status:', status)
    })

  // Test 2: Products table subscription (for inventory updates)
  console.log('\n2️⃣ Testing products table subscription...')
  
  let productUpdates = 0
  const productSubscription = supabase
    .channel('products-channel')
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'products' },
      (payload) => {
        productUpdates++
        console.log(`🍕 Product update #${productUpdates}:`, payload.eventType, payload.new?.name || payload.old?.name)
      }
    )
    .subscribe((status) => {
      console.log('📡 Products subscription status:', status)
    })

  // Wait for subscriptions to be ready
  await new Promise(resolve => setTimeout(resolve, 2000))

  // Test 3: Create test data to trigger real-time updates
  console.log('\n3️⃣ Creating test data to trigger real-time updates...')

  // Create a test customer first
  const { data: testCustomer, error: customerError } = await supabase
    .from('customers')
    .insert({
      full_name: 'Real-time Test Customer',
      email: '<EMAIL>',
      phone_number: '+1234567890'
    })
    .select()
    .single()

  if (customerError) {
    console.error('❌ Failed to create test customer:', customerError)
    return
  }

  console.log('✅ Test customer created:', testCustomer.id)

  // Get admin user profile for order creation
  const { data: adminProfile } = await supabase
    .from('profiles')
    .select('id')
    .eq('email', '<EMAIL>')
    .single()

  // Create a test order
  const { data: testOrder, error: orderError } = await supabase
    .from('orders')
    .insert({
      table_number: 'RT-01',
      order_type: 'dine_in',
      status: 'pending',
      subtotal_amount: 25.99,
      tax_amount: 2.60,
      discount_amount: 0,
      complementary_amount: 0,
      total_amount: 28.59,
      employee_id: adminProfile.id,
      customer_id: testCustomer.id,
      notes: 'Real-time test order'
    })
    .select()
    .single()

  if (orderError) {
    console.error('❌ Failed to create test order:', orderError)
  } else {
    console.log('✅ Test order created:', testOrder.id)

    // Wait a moment for real-time updates
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Update the order status to trigger another real-time event
    console.log('\n4️⃣ Updating order status...')
    const { error: updateError } = await supabase
      .from('orders')
      .update({ status: 'preparing' })
      .eq('id', testOrder.id)

    if (updateError) {
      console.error('❌ Failed to update order:', updateError)
    } else {
      console.log('✅ Order status updated to preparing')
    }

    // Wait for real-time updates
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Clean up test data
    console.log('\n5️⃣ Cleaning up test data...')
    await supabase.from('orders').delete().eq('id', testOrder.id)
    await supabase.from('customers').delete().eq('id', testCustomer.id)
    console.log('✅ Test data cleaned up')
  }

  // Wait a bit more to see if we received real-time updates
  await new Promise(resolve => setTimeout(resolve, 2000))

  // Unsubscribe from channels
  console.log('\n6️⃣ Unsubscribing from channels...')
  await supabase.removeChannel(orderSubscription)
  await supabase.removeChannel(productSubscription)
  console.log('✅ Unsubscribed from all channels')

  // Report results
  console.log('\n📊 Real-time Test Results:')
  console.log(`   Orders updates received: ${orderUpdates}`)
  console.log(`   Product updates received: ${productUpdates}`)

  if (orderUpdates > 0) {
    console.log('✅ Real-time subscriptions are working!')
  } else {
    console.log('⚠️ No real-time updates received - check configuration')
  }

  // Sign out
  await supabase.auth.signOut()
  console.log('\n🔐 Signed out successfully')
}

async function testDashboardViews() {
  console.log('\n📈 Testing Dashboard Views with Sample Data...')
  console.log('=' .repeat(50))

  // Sign in
  const { error: signInError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123'
  })

  if (signInError) {
    console.error('❌ Failed to sign in:', signInError)
    return
  }

  // Test low stock products view (should have data)
  console.log('\n📦 Testing low_stock_products view...')
  const { data: lowStock, error: lowStockError } = await supabase
    .from('low_stock_products')
    .select('*')

  if (lowStockError) {
    console.error('❌ Low stock query failed:', lowStockError)
  } else {
    console.log(`✅ Low stock products: ${lowStock.length} items found`)
    if (lowStock.length > 0) {
      lowStock.forEach(item => {
        console.log(`   - ${item.name}: ${item.current_stock} units (${item.category_name})`)
      })
    }
  }

  // Test customer order history (should show our test data)
  console.log('\n👥 Testing customer_order_history view...')
  const { data: customerHistory, error: customerError } = await supabase
    .from('customer_order_history')
    .select('*')
    .limit(5)

  if (customerError) {
    console.error('❌ Customer history query failed:', customerError)
  } else {
    console.log(`✅ Customer history: ${customerHistory.length} customers found`)
    customerHistory.forEach(customer => {
      console.log(`   - ${customer.full_name}: ${customer.total_orders} orders, $${customer.total_spent || 0}`)
    })
  }

  await supabase.auth.signOut()
}

async function runRealtimeTests() {
  console.log('🚀 Starting Real-time Integration Tests...')
  console.log('🕒 ' + new Date().toLocaleString())
  console.log('=' .repeat(60))

  try {
    await testRealtimeSubscriptions()
    await testDashboardViews()

    console.log('\n' + '=' .repeat(60))
    console.log('🎉 Real-time Integration Tests Complete!')

  } catch (error) {
    console.error('❌ Real-time test suite failed:', error)
  }
}

runRealtimeTests()
