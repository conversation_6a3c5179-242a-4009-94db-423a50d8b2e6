{"version": 3, "file": "Page.d.ts", "sourceRoot": "", "sources": ["../../../../src/bidi/Page.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,IAAI,MAAM,4CAA4C,CAAC;AACnE,OAAO,KAAK,QAAQ,MAAM,mBAAmB,CAAC;AAG9C,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AAErD,OAAO,KAAK,EAAC,cAAc,EAAC,MAAM,iBAAiB,CAAC;AACpD,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,wBAAwB,CAAC;AACzD,OAAO,KAAK,EACV,WAAW,EACX,kBAAkB,EAClB,YAAY,EACZ,UAAU,EACV,kBAAkB,EACnB,MAAM,gBAAgB,CAAC;AACxB,OAAO,EACL,IAAI,EAEJ,KAAK,2BAA2B,EAChC,KAAK,iBAAiB,EACvB,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAC,QAAQ,EAAC,MAAM,oBAAoB,CAAC;AAE5C,OAAO,KAAK,EAEV,iBAAiB,EAClB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAC,OAAO,EAAC,MAAM,mBAAmB,CAAC;AAC1C,OAAO,KAAK,EACV,kBAAkB,EAClB,MAAM,EACN,WAAW,EACX,cAAc,EACd,oBAAoB,EACrB,MAAM,qBAAqB,CAAC;AAE7B,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAC,WAAW,EAAC,MAAM,0BAA0B,CAAC;AACrD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,yBAAyB,CAAC;AACxD,OAAO,KAAK,EAAC,SAAS,EAAC,MAAM,oBAAoB,CAAC;AAOlD,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,uBAAuB,CAAC;AAOpD,OAAO,KAAK,EAAC,WAAW,EAAC,MAAM,cAAc,CAAC;AAC9C,OAAO,KAAK,EAAC,kBAAkB,EAAC,MAAM,qBAAqB,CAAC;AAC5D,OAAO,KAAK,EAAC,cAAc,EAAC,MAAM,iBAAiB,CAAC;AACpD,OAAO,KAAK,EAAC,eAAe,EAAC,MAAM,2BAA2B,CAAC;AAE/D,OAAO,EAAC,SAAS,EAAC,MAAM,YAAY,CAAC;AACrC,OAAO,KAAK,EAAC,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AACxD,OAAO,EAAC,YAAY,EAAE,SAAS,EAAE,eAAe,EAAC,MAAM,YAAY,CAAC;AACpE,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAEhD,OAAO,KAAK,EAAC,aAAa,EAAC,MAAM,gBAAgB,CAAC;AAElD;;;;GAIG;AACH,qBAAa,QAAS,SAAQ,IAAI;;IAChC,MAAM,CAAC,IAAI,CACT,cAAc,EAAE,kBAAkB,EAClC,eAAe,EAAE,eAAe,GAC/B,QAAQ;IAOX,QAAQ,CAAC,cAAc,2BAAkC;IAOzD,QAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC;IAChC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC;IAC1B,QAAQ,CAAC,WAAW,EAAE,eAAe,CAAC;IACtC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC;IAC1B,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAM5B,OAAO,IAAI,cAAc;IAIzB,OAAO;IA6BP;;OAEG;IACH,iBAAiB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAM;IAGhC,YAAY,CACzB,SAAS,EAAE,MAAM,EACjB,iBAAiB,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,iBAAiB,GACvD,OAAO,CAAC,IAAI,CAAC;IA2DD,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAK7C,YAAY,CAAC,SAAS,EACnC,eAAe,EAAE,YAAY,CAAC,SAAS,CAAC,GACvC,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;IAe5B,OAAO,IAAI,WAAW;IAItB,cAAc,IAAI,kBAAkB;IAIpC,SAAS,IAAI,SAAS;IAIzB,YAAY,IAAI,OAAO,CAAC,SAAS,CAAC;IAyB/B,MAAM,IAAI,SAAS,EAAE;IAQrB,QAAQ,IAAI,OAAO;IAIb,KAAK,CAAC,OAAO,CAAC,EAAE;QAAC,eAAe,CAAC,EAAE,OAAO,CAAA;KAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAS3D,MAAM,CACnB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAa1B,2BAA2B,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAIlD,iBAAiB,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAIxC,iBAAiB,IAAI,MAAM;IAI3B,2BAA2B,IAAI,MAAM;IAIrC,mBAAmB,IAAI,OAAO;IAIxB,cAAc,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC;IA0B1D,oBAAoB,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAIrD,gBAAgB,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI9C,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAI1D,oBAAoB,CACjC,QAAQ,CAAC,EAAE,YAAY,EAAE,GACxB,OAAO,CAAC,IAAI,CAAC;IAID,eAAe,CAAC,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAInD,gBAAgB,CAAC,SAAS,CAAC,EAAE;QAC1C,YAAY,EAAE,OAAO,CAAC;QACtB,gBAAgB,EAAE,OAAO,CAAC;KAC3B,GAAG,OAAO,CAAC,IAAI,CAAC;IAIF,uBAAuB,CACpC,IAAI,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,kCAAkC,CAAC,MAAM,CAAC,GACnE,OAAO,CAAC,IAAI,CAAC;IAID,WAAW,CAAC,QAAQ,EAAE,QAAQ,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAyB3D,QAAQ,IAAI,QAAQ,GAAG,IAAI;IAIrB,GAAG,CAAC,OAAO,GAAE,UAAe,GAAG,OAAO,CAAC,UAAU,CAAC;IAiDlD,eAAe,CAC5B,OAAO,CAAC,EAAE,UAAU,GAAG,SAAS,GAC/B,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IAWvB,WAAW,CACxB,OAAO,EAAE,QAAQ,CAAC,iBAAiB,CAAC,GACnC,OAAO,CAAC,MAAM,CAAC;IAuDH,gBAAgB,IAAI,OAAO,CAAC,UAAU,CAAC;IAIvC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC;IAI7B,qBAAqB,CAClC,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,CAAC,GAAG,IAAI,EAAE,MAAM,KAAK,OAAO,GAAG,CAAC,GAAG,IAAI,EAAE,MAAM,KAAK,OAAO,EAExE,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,2BAA2B,CAAC;IAQxB,mCAAmC,CAChD,EAAE,EAAE,MAAM,GACT,OAAO,CAAC,IAAI,CAAC;IAID,cAAc,CAAC,IAAI,SAAS,OAAO,EAAE,EAAE,GAAG,EACvD,IAAI,EAAE,MAAM,EACZ,YAAY,EACR,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,GACnC;QAAC,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,KAAK,SAAS,CAAC,GAAG,CAAC,CAAA;KAAC,GAC/C,OAAO,CAAC,IAAI,CAAC;IAOP,yBAAyB,IAAI,OAAO;IAI9B,eAAe,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAajD,OAAO,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAiBnD,uBAAuB,IAAI,KAAK;IAIhC,MAAM,IAAI,KAAK;IAIT,kBAAkB,CAC/B,OAAO,GAAE,kBAAuB,GAC/B,OAAO,CAAC,WAAW,CAAC;IAgDd,OAAO,IAAI,aAAa,EAAE;IAKpB,sBAAsB,CAAC,MAAM,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAQrE;;OAEG;IACH,iBAAiB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAM;IAEhC,mBAAmB,CAChC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAC9B,OAAO,CAAC,IAAI,CAAC;IAkBhB;;OAEG;IACH,YAAY,EAAE,WAAW,GAAG,IAAI,CAAQ;IAEzB,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IA4BlE,mBAAmB,IAAI,KAAK;IAI5B,sBAAsB,IAAI,KAAK;IAIzB,cAAc,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAiB/C,wBAAwB,CACrC,iBAAiB,EAAE,iBAAiB,GAAG,IAAI,GAC1C,OAAO,CAAC,IAAI,CAAC;IAoCD,SAAS,CAAC,GAAG,OAAO,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAoEnD,YAAY,CACzB,GAAG,OAAO,EAAE,oBAAoB,EAAE,GACjC,OAAO,CAAC,IAAI,CAAC;IA0BD,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAIxD,OAAO,IAAI,KAAK;IAIV,MAAM,CACnB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAIhB,SAAS,CACtB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IA8BtB,mBAAmB,IAAI,KAAK;CAGtC;AAiED,wBAAgB,qBAAqB,CACnC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAC/B,2BAA2B,UAAQ,GAClC,MAAM,CA+CR;AAoBD;;;GAGG;AACH,wBAAgB,8CAA8C,CAC5D,WAAW,EAAE,WAAW,EACxB,GAAG,aAAa,EAAE,KAAK,CAAC,MAAM,WAAW,CAAC,GACzC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAQzB;AAQD,wBAAgB,+BAA+B,CAC7C,QAAQ,EAAE,cAAc,GAAG,SAAS,GACnC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAMvB;AAED,wBAAgB,6BAA6B,CAC3C,MAAM,EAAE,MAAM,GAAG,SAAS,GACzB,MAAM,GAAG,SAAS,CAEpB;AAED,wBAAgB,6CAA6C,CAC3D,YAAY,EAAE,kBAAkB,GAAG,MAAM,GAAG,SAAS,GACpD,MAAM,GAAG,SAAS,CAUpB"}