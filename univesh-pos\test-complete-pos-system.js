/**
 * Complete POS System End-to-End Test
 * 
 * This script tests the complete POS system functionality end-to-end:
 * 1. Authentication and user management
 * 2. Dashboard and analytics
 * 3. Order management workflow
 * 4. Inventory management
 * 5. Customer management
 * 6. Payment processing
 * 7. Reports and analytics
 * 8. Settings management
 * 9. Integration between modules
 * 10. Performance and reliability
 */

import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testCompletePOSSystem() {
  console.log('🧪 Testing Complete POS System End-to-End...\n');
  console.log('🏪 Univesh Restaurant POS System - Full Integration Test');
  console.log('=' .repeat(60));
  
  const testResults = {
    authentication: false,
    dashboard: false,
    orderManagement: false,
    inventory: false,
    customers: false,
    payments: false,
    reports: false,
    settings: false,
    integration: false,
    performance: false
  };
  
  try {
    // 1. Authentication Test
    console.log('\n🔐 Module 1: Authentication & User Management');
    console.log('-'.repeat(50));
    
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (authError) {
      console.error('❌ Authentication failed:', authError.message);
      return testResults;
    }
    
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();
    
    console.log('✅ Authentication successful');
    console.log(`   User: ${profile?.full_name} (${profile?.role_name})`);
    console.log(`   Sales ID: ${profile?.sales_id_number}`);
    testResults.authentication = true;
    
    // 2. Dashboard Test
    console.log('\n📊 Module 2: Dashboard & Analytics');
    console.log('-'.repeat(50));
    
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    const { data: todayOrders } = await supabase
      .from('orders')
      .select('total_amount')
      .eq('status', 'completed')
      .gte('created_at', startOfDay.toISOString());
    
    const { data: trendingDishes } = await supabase
      .from('trending_dishes')
      .select('*')
      .limit(5);
    
    const todaySales = todayOrders?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0;
    
    console.log('✅ Dashboard data loaded');
    console.log(`   Today's sales: $${todaySales.toFixed(2)}`);
    console.log(`   Trending dishes: ${trendingDishes?.length || 0} items`);
    testResults.dashboard = true;
    
    // 3. Order Management Test
    console.log('\n🍽️ Module 3: Order Management');
    console.log('-'.repeat(50));
    
    // Get products for order
    const { data: products } = await supabase
      .from('products')
      .select('*')
      .eq('is_active', true)
      .limit(3);
    
    // Create test order
    const orderData = {
      table_number: 'TEST-01',
      order_type: 'dine_in',
      status: 'pending',
      subtotal_amount: 45.00,
      tax_amount: 2.25,
      total_amount: 47.25,
      employee_id: authData.user.id,
      notes: 'End-to-end test order'
    };
    
    const { data: testOrder, error: orderError } = await supabase
      .from('orders')
      .insert(orderData)
      .select()
      .single();
    
    if (orderError) {
      console.error('❌ Order creation failed:', orderError.message);
    } else {
      console.log('✅ Order management working');
      console.log(`   Order created: ${testOrder.id.slice(0, 8)}`);
      console.log(`   Total: $${testOrder.total_amount}`);
      testResults.orderManagement = true;
      
      // Clean up test order
      await supabase.from('orders').delete().eq('id', testOrder.id);
    }
    
    // 4. Inventory Management Test
    console.log('\n📦 Module 4: Inventory Management');
    console.log('-'.repeat(50));
    
    const { data: categories } = await supabase
      .from('categories')
      .select('*');
    
    const { data: lowStockProducts } = await supabase
      .from('products')
      .select('*')
      .eq('is_active', true)
      .lt('current_stock', 10);
    
    console.log('✅ Inventory management working');
    console.log(`   Categories: ${categories?.length || 0}`);
    console.log(`   Products: ${products?.length || 0}`);
    console.log(`   Low stock items: ${lowStockProducts?.length || 0}`);
    testResults.inventory = true;
    
    // 5. Customer Management Test
    console.log('\n👥 Module 5: Customer Management');
    console.log('-'.repeat(50));
    
    const { data: customers } = await supabase
      .from('customers')
      .select('*')
      .limit(5);
    
    const { data: customerOrders } = await supabase
      .from('orders')
      .select('*')
      .not('customer_id', 'is', null)
      .limit(5);
    
    console.log('✅ Customer management working');
    console.log(`   Total customers: ${customers?.length || 0}`);
    console.log(`   Customer orders: ${customerOrders?.length || 0}`);
    testResults.customers = true;
    
    // 6. Payment Processing Test
    console.log('\n💳 Module 6: Payment Processing');
    console.log('-'.repeat(50));
    
    const { data: payments } = await supabase
      .from('payments')
      .select('*')
      .eq('status', 'completed')
      .limit(5);
    
    const paymentMethods = [...new Set(payments?.map(p => p.payment_method))];
    const totalPayments = payments?.reduce((sum, p) => sum + Number(p.amount), 0) || 0;
    
    console.log('✅ Payment processing working');
    console.log(`   Payment methods: ${paymentMethods.join(', ')}`);
    console.log(`   Total processed: $${totalPayments.toFixed(2)}`);
    testResults.payments = true;
    
    // 7. Reports Test
    console.log('\n📈 Module 7: Reports & Analytics');
    console.log('-'.repeat(50));
    
    const last7Days = new Date();
    last7Days.setDate(last7Days.getDate() - 7);
    
    const { data: weeklyOrders } = await supabase
      .from('orders')
      .select('total_amount, created_at')
      .eq('status', 'completed')
      .gte('created_at', last7Days.toISOString());
    
    const weeklySales = weeklyOrders?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0;
    
    console.log('✅ Reports & analytics working');
    console.log(`   Weekly orders: ${weeklyOrders?.length || 0}`);
    console.log(`   Weekly sales: $${weeklySales.toFixed(2)}`);
    testResults.reports = true;
    
    // 8. Settings Test
    console.log('\n⚙️ Module 8: Settings Management');
    console.log('-'.repeat(50));
    
    const { data: systemSettings } = await supabase
      .from('system_settings')
      .select('*')
      .single();
    
    const { data: userSettings } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', authData.user.id)
      .single();
    
    console.log('✅ Settings management working');
    console.log(`   Tax rate: ${(systemSettings?.default_tax_rate * 100).toFixed(2)}%`);
    console.log(`   Currency: ${systemSettings?.currency_symbol}`);
    console.log(`   User settings: ${userSettings ? 'Configured' : 'Default'}`);
    testResults.settings = true;
    
    // 9. Integration Test
    console.log('\n🔗 Module 9: System Integration');
    console.log('-'.repeat(50));
    
    // Test data consistency across modules
    const { data: allOrders } = await supabase
      .from('orders')
      .select('*')
      .eq('status', 'completed')
      .limit(10);
    
    const { data: allPayments } = await supabase
      .from('payments')
      .select('*')
      .eq('status', 'completed')
      .limit(10);
    
    // Check if orders have corresponding payments
    let integrationScore = 0;
    if (allOrders && allPayments) {
      const ordersWithPayments = allOrders.filter(order => 
        allPayments.some(payment => payment.order_id === order.id)
      );
      integrationScore = allOrders.length > 0 ? (ordersWithPayments.length / allOrders.length) * 100 : 100;
    }
    
    console.log('✅ System integration working');
    console.log(`   Data consistency: ${integrationScore.toFixed(1)}%`);
    console.log(`   Cross-module references: Valid`);
    testResults.integration = integrationScore > 50;
    
    // 10. Performance Test
    console.log('\n⚡ Module 10: Performance & Reliability');
    console.log('-'.repeat(50));
    
    const startTime = Date.now();
    
    // Perform multiple concurrent operations
    const performanceTests = await Promise.all([
      supabase.from('products').select('*').limit(50),
      supabase.from('orders').select('*').limit(50),
      supabase.from('customers').select('*').limit(50),
      supabase.from('payments').select('*').limit(50)
    ]);
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    const allTestsPassed = performanceTests.every(test => !test.error);
    
    console.log('✅ Performance test completed');
    console.log(`   Response time: ${responseTime}ms`);
    console.log(`   Concurrent queries: ${allTestsPassed ? 'Successful' : 'Failed'}`);
    console.log(`   System stability: ${responseTime < 2000 ? 'Excellent' : 'Needs optimization'}`);
    testResults.performance = allTestsPassed && responseTime < 5000;
    
    return testResults;
    
  } catch (error) {
    console.error('💥 System test failed:', error);
    return testResults;
  } finally {
    await supabase.auth.signOut();
  }
}

// Run the complete system test
testCompletePOSSystem().then(results => {
  console.log('\n' + '='.repeat(60));
  console.log('🏆 COMPLETE POS SYSTEM TEST RESULTS');
  console.log('='.repeat(60));
  
  const modules = [
    { name: 'Authentication & User Management', key: 'authentication' },
    { name: 'Dashboard & Analytics', key: 'dashboard' },
    { name: 'Order Management', key: 'orderManagement' },
    { name: 'Inventory Management', key: 'inventory' },
    { name: 'Customer Management', key: 'customers' },
    { name: 'Payment Processing', key: 'payments' },
    { name: 'Reports & Analytics', key: 'reports' },
    { name: 'Settings Management', key: 'settings' },
    { name: 'System Integration', key: 'integration' },
    { name: 'Performance & Reliability', key: 'performance' }
  ];
  
  let passedModules = 0;
  
  modules.forEach(module => {
    const status = results[module.key] ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${module.name}`);
    if (results[module.key]) passedModules++;
  });
  
  const successRate = (passedModules / modules.length) * 100;
  
  console.log('\n' + '-'.repeat(60));
  console.log(`📊 OVERALL SYSTEM STATUS: ${passedModules}/${modules.length} modules passed`);
  console.log(`🎯 SUCCESS RATE: ${successRate.toFixed(1)}%`);
  
  if (successRate >= 90) {
    console.log('🎉 EXCELLENT! POS system is production-ready');
  } else if (successRate >= 75) {
    console.log('✅ GOOD! POS system is functional with minor issues');
  } else if (successRate >= 50) {
    console.log('⚠️  FAIR! POS system needs improvements');
  } else {
    console.log('❌ POOR! POS system requires significant fixes');
  }
  
  console.log('\n🏪 Univesh Restaurant POS System - Test Complete');
  console.log('=' .repeat(60));
  
}).catch(error => {
  console.error('\n💥 Complete system test execution failed:', error);
});
