# Univesh POS Deployment Guide

This guide provides step-by-step instructions for deploying the Univesh Restaurant POS system to production.

## 🚀 Production Deployment

### Prerequisites
- Supabase project (already configured)
- Domain name (optional)
- SSL certificate (handled by hosting provider)

### 1. Environment Configuration

Create a production `.env` file:
```env
VITE_SUPABASE_URL=https://kufsqfbiilphymiehwet.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU
VITE_APP_NAME=Univesh Restaurant POS
VITE_APP_VERSION=1.0.0
VITE_DEV_MODE=false
```

### 2. Build for Production

```bash
# Install dependencies
npm install

# Build the application
npm run build

# Preview the build (optional)
npm run preview
```

### 3. Deployment Options

#### Option A: Vercel (Recommended)

1. Install Vercel CLI:
```bash
npm install -g vercel
```

2. Deploy:
```bash
vercel --prod
```

3. Configure environment variables in Vercel dashboard

#### Option B: Netlify

1. Install Netlify CLI:
```bash
npm install -g netlify-cli
```

2. Deploy:
```bash
netlify deploy --prod --dir=dist
```

#### Option C: Manual Deployment

1. Upload the `dist/` folder to your web server
2. Configure your web server to serve the files
3. Set up redirects for SPA routing

### 4. Post-Deployment Setup

#### Create Admin User
1. Visit your deployed application
2. Sign up with admin credentials:
   - Sales ID: `ADMIN001`
   - Email: `<EMAIL>`
   - Password: Choose a strong password
3. The system will create a cashier role by default
4. Manually update the role in Supabase dashboard to admin

#### Database Configuration
1. Verify all tables are created
2. Check RLS policies are active
3. Insert sample data if needed
4. Configure backup schedules

### 5. Security Checklist

- [ ] Environment variables are secure
- [ ] HTTPS is enabled
- [ ] RLS policies are active
- [ ] Admin user is created
- [ ] Database backups are configured
- [ ] Error logging is set up

### 6. Performance Optimization

#### Frontend Optimizations
- Gzip compression enabled
- CDN configured for static assets
- Browser caching headers set
- Image optimization

#### Database Optimizations
- Connection pooling configured
- Query performance monitored
- Indexes optimized
- Regular maintenance scheduled

### 7. Monitoring and Maintenance

#### Health Checks
- Application uptime monitoring
- Database performance monitoring
- Error rate tracking
- User activity monitoring

#### Regular Maintenance
- Security updates
- Dependency updates
- Database maintenance
- Backup verification

### 8. Troubleshooting

#### Common Issues

**Build Errors:**
- Check Node.js version (18+)
- Clear node_modules and reinstall
- Verify environment variables

**Authentication Issues:**
- Verify Supabase URL and keys
- Check RLS policies
- Validate user roles

**Performance Issues:**
- Monitor database queries
- Check network latency
- Optimize bundle size

### 9. Scaling Considerations

#### Horizontal Scaling
- Load balancer configuration
- Multiple server instances
- Database read replicas

#### Vertical Scaling
- Server resource allocation
- Database performance tuning
- CDN optimization

### 10. Backup and Recovery

#### Database Backups
- Automated daily backups
- Point-in-time recovery
- Cross-region replication

#### Application Backups
- Source code versioning
- Configuration backups
- Asset backups

## 📞 Support

For deployment assistance:
- Check the main README.md
- Review Supabase documentation
- Contact the development team

---

**Deployment completed successfully! 🎉**
