/**
 * Loading State Manager
 * 
 * Manages application loading states to prevent persistent loading screens
 * and provide better user experience with timeout handling and recovery.
 */

interface LoadingState {
  isLoading: boolean
  component: string
  startTime: number
  timeout?: NodeJS.Timeout
}

class LoadingStateManager {
  private static instance: LoadingStateManager
  private loadingStates: Map<string, LoadingState> = new Map()
  private readonly DEFAULT_TIMEOUT = 10000 // 10 seconds
  private readonly MAX_TIMEOUT = 30000 // 30 seconds

  static getInstance(): LoadingStateManager {
    if (!LoadingStateManager.instance) {
      LoadingStateManager.instance = new LoadingStateManager()
    }
    return LoadingStateManager.instance
  }

  /**
   * Start loading for a component with automatic timeout
   */
  startLoading(
    componentName: string, 
    timeoutMs: number = this.DEFAULT_TIMEOUT,
    onTimeout?: () => void
  ): void {
    // Clear any existing timeout for this component
    this.clearLoading(componentName)

    const timeout = setTimeout(() => {
      console.warn(`⏰ Loading timeout for ${componentName} after ${timeoutMs}ms`)
      
      if (onTimeout) {
        onTimeout()
      } else {
        // Default timeout behavior - force clear loading
        this.forceStopLoading(componentName)
      }
    }, Math.min(timeoutMs, this.MAX_TIMEOUT))

    this.loadingStates.set(componentName, {
      isLoading: true,
      component: componentName,
      startTime: Date.now(),
      timeout
    })

    if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 Started loading for ${componentName}`)
    }
  }

  /**
   * Stop loading for a component
   */
  stopLoading(componentName: string): void {
    const state = this.loadingStates.get(componentName)
    
    if (state) {
      if (state.timeout) {
        clearTimeout(state.timeout)
      }
      
      const duration = Date.now() - state.startTime
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`✅ Stopped loading for ${componentName} after ${duration}ms`)
      }
      
      this.loadingStates.delete(componentName)
    }
  }

  /**
   * Force stop loading (used for timeout recovery)
   */
  forceStopLoading(componentName: string): void {
    const state = this.loadingStates.get(componentName)
    
    if (state) {
      if (state.timeout) {
        clearTimeout(state.timeout)
      }
      
      const duration = Date.now() - state.startTime
      
      console.warn(`⚠️ Force stopped loading for ${componentName} after ${duration}ms`)
      
      this.loadingStates.delete(componentName)
      
      // Trigger a custom event for components to listen to
      window.dispatchEvent(new CustomEvent('loadingTimeout', {
        detail: { componentName, duration }
      }))
    }
  }

  /**
   * Clear loading state without logging (for cleanup)
   */
  clearLoading(componentName: string): void {
    const state = this.loadingStates.get(componentName)
    
    if (state?.timeout) {
      clearTimeout(state.timeout)
    }
    
    this.loadingStates.delete(componentName)
  }

  /**
   * Check if a component is currently loading
   */
  isLoading(componentName: string): boolean {
    return this.loadingStates.has(componentName)
  }

  /**
   * Get loading duration for a component
   */
  getLoadingDuration(componentName: string): number {
    const state = this.loadingStates.get(componentName)
    return state ? Date.now() - state.startTime : 0
  }

  /**
   * Get all currently loading components
   */
  getLoadingComponents(): string[] {
    return Array.from(this.loadingStates.keys())
  }

  /**
   * Clear all loading states (for cleanup/reset)
   */
  clearAll(): void {
    this.loadingStates.forEach(state => {
      if (state.timeout) {
        clearTimeout(state.timeout)
      }
    })
    
    this.loadingStates.clear()
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🧹 Cleared all loading states')
    }
  }

  /**
   * Get statistics about loading states
   */
  getStats(): {
    activeCount: number
    components: string[]
    longestDuration: number
    averageDuration: number
  } {
    const components = Array.from(this.loadingStates.values())
    const durations = components.map(state => Date.now() - state.startTime)
    
    return {
      activeCount: components.length,
      components: components.map(state => state.component),
      longestDuration: durations.length > 0 ? Math.max(...durations) : 0,
      averageDuration: durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0
    }
  }
}

// React hook for using loading state manager
export const useLoadingState = (componentName: string) => {
  const manager = LoadingStateManager.getInstance()
  
  const startLoading = (timeoutMs?: number, onTimeout?: () => void) => {
    manager.startLoading(componentName, timeoutMs, onTimeout)
  }
  
  const stopLoading = () => {
    manager.stopLoading(componentName)
  }
  
  const isLoading = () => {
    return manager.isLoading(componentName)
  }
  
  const getDuration = () => {
    return manager.getLoadingDuration(componentName)
  }
  
  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      manager.clearLoading(componentName)
    }
  }, [componentName])
  
  return {
    startLoading,
    stopLoading,
    isLoading,
    getDuration
  }
}

// Utility functions
export const startComponentLoading = (componentName: string, timeoutMs?: number) => {
  LoadingStateManager.getInstance().startLoading(componentName, timeoutMs)
}

export const stopComponentLoading = (componentName: string) => {
  LoadingStateManager.getInstance().stopLoading(componentName)
}

export const isComponentLoading = (componentName: string): boolean => {
  return LoadingStateManager.getInstance().isLoading(componentName)
}

export const clearAllLoading = () => {
  LoadingStateManager.getInstance().clearAll()
}

export default LoadingStateManager

// Import React for the hook
import React from 'react'
