import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '../lib/supabase'

interface Profile {
  id: string
  role_id: string
  sales_id_number: string
  full_name: string
  email: string
  phone_number?: string
  date_of_birth?: string
  designation?: string
  role_name?: 'admin' | 'manager' | 'cashier'
}

interface AuthContextType {
  user: User | null
  profile: Profile | null
  session: Session | null
  loading: boolean
  signIn: (salesId: string, password: string) => Promise<{ error?: any }>
  signUp: (email: string, password: string, salesId: string, fullName: string) => Promise<{ error?: any }>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<Profile>) => Promise<{ error?: any }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      setSession(session)
      setUser(session?.user ?? null)

      if (session?.user) {
        await fetchProfile(session.user.id)
      }

      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)

        if (session?.user) {
          await fetchProfile(session.user.id)
        } else {
          setProfile(null)
        }

        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          *,
          roles!inner(role_name)
        `)
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching profile:', error)
        return
      }

      if (data) {
        setProfile({
          ...data,
          role_name: data.roles.role_name
        })
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
    }
  }

  const signIn = async (salesId: string, password: string) => {
    try {
      setLoading(true)

      // First, find the user by sales_id_number to get their email
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('email')
        .eq('sales_id_number', salesId)
        .single()

      if (profileError || !profileData) {
        return { error: { message: 'Invalid Sales ID Number' } }
      }

      // Sign in with email and password
      const { data, error } = await supabase.auth.signInWithPassword({
        email: profileData.email,
        password: password,
      })

      if (error) {
        return { error }
      }

      return { data }
    } catch (error) {
      return { error }
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string, salesId: string, fullName: string) => {
    try {
      setLoading(true)

      // Check if sales ID already exists
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('sales_id_number', salesId)
        .single()

      if (existingProfile) {
        return { error: { message: 'Sales ID Number already exists' } }
      }

      // Sign up the user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
      })

      if (authError) {
        return { error: authError }
      }

      if (authData.user) {
        // Get default cashier role
        const { data: roleData } = await supabase
          .from('roles')
          .select('id')
          .eq('role_name', 'cashier')
          .single()

        if (roleData) {
          // Create profile
          const { error: profileError } = await supabase
            .from('profiles')
            .insert({
              id: authData.user.id,
              role_id: roleData.id,
              sales_id_number: salesId,
              full_name: fullName,
              email: email,
              designation: 'Cashier'
            })

          if (profileError) {
            return { error: profileError }
          }
        }
      }

      return { data: authData }
    } catch (error) {
      return { error }
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    await supabase.auth.signOut()
    setUser(null)
    setProfile(null)
    setSession(null)
    setLoading(false)
  }

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) return { error: { message: 'No user logged in' } }

    try {
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)

      if (error) {
        return { error }
      }

      // Refresh profile data
      await fetchProfile(user.id)
      return {}
    } catch (error) {
      return { error }
    }
  }

  const value = {
    user,
    profile,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
