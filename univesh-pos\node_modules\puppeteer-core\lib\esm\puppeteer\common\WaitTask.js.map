{"version": 3, "file": "WaitTask.js", "sourceRoot": "", "sources": ["../../../../src/common/WaitTask.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAMH,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAC,WAAW,EAAC,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAC,iBAAiB,EAAC,MAAM,qBAAqB,CAAC;AAEtD,OAAO,EAAC,YAAY,EAAC,MAAM,aAAa,CAAC;AACzC,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AAarC;;GAEG;AACH,MAAM,OAAO,QAAQ;IACnB,MAAM,CAAQ;IACd,QAAQ,CAA8B;IACtC,KAAK,CAAuB;IAE5B,GAAG,CAAS;IACZ,KAAK,CAAY;IAEjB,QAAQ,CAAkB;IAC1B,aAAa,CAAgB;IAE7B,OAAO,GAAG,QAAQ,CAAC,MAAM,EAAgB,CAAC;IAE1C,OAAO,CAAuB;IAC9B,OAAO,CAAe;IACtB,OAAO,GAAsB,EAAE,CAAC;IAEhC,YACE,KAAY,EACZ,OAAwB,EACxB,EAAiD,EACjD,GAAG,IAAe;QAElB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,OAAO,EAAE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE;YAC3D,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;QAEH,QAAQ,OAAO,EAAE,EAAE,CAAC;YAClB,KAAK,QAAQ;gBACX,IAAI,CAAC,GAAG,GAAG,kBAAkB,EAAE,KAAK,CAAC;gBACrC,MAAM;YACR;gBACE,IAAI,CAAC,GAAG,GAAG,iBAAiB,CAAC,EAAE,CAAC,CAAC;gBACjC,MAAM;QACV,CAAC;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CACnC,mBAAmB,OAAO,CAAC,OAAO,aAAa,CAChD,CAAC;YACF,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC1C,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACtB,CAAC;QAED,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,KAAK;QACT,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QACxB,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC;YACH,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACtB,KAAK,KAAK;oBACR,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAC7C,CAAC,EAAC,SAAS,EAAE,cAAc,EAAC,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,EAAE;wBAC3C,MAAM,GAAG,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC;wBAC/B,OAAO,IAAI,SAAS,CAAC,GAAG,EAAE;4BACxB,OAAO,GAAG,CAAC,GAAG,IAAI,CAAe,CAAC;wBACpC,CAAC,CAAC,CAAC;oBACL,CAAC,EACD,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;wBACvB,OAAO,OAAO,CAAC,aAAa,CAAC;oBAC/B,CAAC,CAAC,EACF,IAAI,CAAC,GAAG,EACR,GAAG,IAAI,CAAC,KAAK,CACd,CAAC;oBACF,MAAM;gBACR,KAAK,UAAU;oBACb,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAC7C,CAAC,EAAC,cAAc,EAAE,cAAc,EAAC,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,EAAE;wBACtD,MAAM,GAAG,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC;wBAC/B,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE;4BAC7B,OAAO,GAAG,CAAC,GAAG,IAAI,CAAe,CAAC;wBACpC,CAAC,EAAE,IAAI,IAAI,QAAQ,CAAC,CAAC;oBACvB,CAAC,EACD,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;wBACvB,OAAO,OAAO,CAAC,aAAa,CAAC;oBAC/B,CAAC,CAAC,EACF,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,GAAG,EACR,GAAG,IAAI,CAAC,KAAK,CACd,CAAC;oBACF,MAAM;gBACR;oBACE,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAC7C,CAAC,EAAC,cAAc,EAAE,cAAc,EAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,EAAE;wBACpD,MAAM,GAAG,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC;wBAC/B,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE;4BAC7B,OAAO,GAAG,CAAC,GAAG,IAAI,CAAe,CAAC;wBACpC,CAAC,EAAE,EAAE,CAAC,CAAC;oBACT,CAAC,EACD,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;wBACvB,OAAO,OAAO,CAAC,aAAa,CAAC;oBAC/B,CAAC,CAAC,EACF,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,GAAG,EACR,GAAG,IAAI,CAAC,KAAK,CACd,CAAC;oBACF,MAAM;YACV,CAAC;YAED,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACnC,KAAK,MAAM,CAAC,KAAK,EAAE,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;gBACxD,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;YACzB,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAE7B,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC9B,OAAO;YACT,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACzC,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,KAAa;QAC3B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEhE,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE5B,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAC,MAAM,EAAC,EAAE;oBACzC,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBACtB,CAAC,CAAC,CAAC;gBACH,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACjB,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBAC7B,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;gBAC3B,CAAC;YACH,CAAC;YAAC,MAAM,CAAC;gBACP,oEAAoE;YACtE,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,KAAc;QACxB,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YACvB,oFAAoF;YACpF,0EAA0E;YAC1E,gCAAgC;YAChC,IACE,KAAK,CAAC,OAAO,CAAC,QAAQ,CACpB,sDAAsD,CACvD,EACD,CAAC;gBACD,OAAO,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACrD,CAAC;YAED,uDAAuD;YACvD,kDAAkD;YAClD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAC,EAAE,CAAC;gBAC9D,OAAO;YACT,CAAC;YAED,iEAAiE;YACjE,aAAa;YACb,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC,EAAE,CAAC;gBACpE,OAAO;YACT,CAAC;YAED,iEAAiE;YACjE,gEAAgE;YAChE,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,+BAA+B,CAAC,EAAE,CAAC;gBAC5D,OAAO;YACT,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,KAAK,CAAC,+BAA+B,EAAE;YAChD,KAAK,EAAE,KAAK;SACb,CAAC,CAAC;IACL,CAAC;IAED,cAAc,GAAG,GAAG,EAAE;QACpB,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC,CAAC;CACH;AAED;;GAEG;AACH,MAAM,OAAO,WAAW;IACtB,MAAM,GAAkB,IAAI,GAAG,EAAY,CAAC;IAE5C,GAAG,CAAC,IAAmB;QACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAED,MAAM,CAAC,IAAmB;QACxB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,YAAY,CAAC,KAAa;QACxB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAC/B,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,MAAM,OAAO,CAAC,GAAG,CACf,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF"}