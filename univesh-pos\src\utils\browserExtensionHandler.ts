/**
 * Browser Extension Error Handler
 *
 * Advanced error suppression system for browser extension conflicts in React applications.
 * Implements 2025 best practices for clean console output during development.
 *
 * Features:
 * - Multi-layer error interception (console, window, promise rejections)
 * - Selective suppression with pattern matching
 * - Performance optimized with minimal overhead
 * - Development-friendly debugging capabilities
 */

interface ExtensionError {
  message: string
  source?: string
  lineno?: number
  colno?: number
  error?: Error
  timestamp?: number
}

interface ErrorStats {
  suppressedCount: number
  lastSuppressed: number
  patterns: string[]
  isActive: boolean
}

class BrowserExtensionHandler {
  private static instance: BrowserExtensionHandler
  private suppressedPatterns: Set<string> = new Set()
  private originalConsoleError: typeof console.error
  private originalConsoleWarn: typeof console.warn
  private originalWindowErrorHandler: OnErrorEventHandler | null = null
  private suppressedCount: number = 0
  private isInitialized: boolean = false

  constructor() {
    this.originalConsoleError = console.error.bind(console)
    this.originalConsoleWarn = console.warn.bind(console)
    this.initializeErrorSuppression()
  }

  static getInstance(): BrowserExtensionHandler {
    if (!BrowserExtensionHandler.instance) {
      BrowserExtensionHandler.instance = new BrowserExtensionHandler()
    }
    return BrowserExtensionHandler.instance
  }

  private initializeErrorSuppression(): void {
    if (this.isInitialized) return

    // Comprehensive 2025 browser extension error patterns
    const extensionErrorPatterns = [
      // Chrome runtime errors
      'unchecked runtime.lasterror',
      'runtime.lasterror',
      'could not establish connection',
      'receiving end does not exist',
      'the message port closed before a response was received',
      'message port closed before a response was received',
      'extension context invalidated',
      'chrome.runtime.sendmessage',
      'chrome.runtime.connect',

      // Extension URLs
      'chrome-extension://',
      'moz-extension://',
      'safari-extension://',
      'edge-extension://',
      'webkit-extension://',

      // Common extension injection errors
      'content script',
      'extension script',
      'injected script',
      'user script',

      // Network/connection errors from extensions
      'net::err_blocked_by_client',
      'net::err_failed',
      'failed to fetch',

      // Specific extension error messages
      'non-error promise rejection captured',
      'script error',
      'network error when attempting to fetch resource'
    ]

    // Store patterns for optimized lookup
    extensionErrorPatterns.forEach(pattern => {
      this.suppressedPatterns.add(pattern.toLowerCase())
    })

    this.setupConsoleInterception()
    this.setupWindowErrorHandling()
    this.setupPromiseRejectionHandling()
    this.setupPerformanceOptimizations()

    this.isInitialized = true
  }

  private setupConsoleInterception(): void {
    // Enhanced console.error interception
    console.error = (...args: any[]) => {
      if (this.shouldSuppressConsoleMessage(args)) {
        this.suppressedCount++
        if (import.meta.env.DEV && this.suppressedCount % 10 === 1) {
          // Log summary every 10 suppressed errors to avoid spam
          this.originalConsoleError(
            '%c🛡️ Browser Extension Handler%c Suppressed %d extension errors',
            'color: #4CAF50; font-weight: bold',
            'color: #666',
            this.suppressedCount
          )
        }
        return
      }

      this.originalConsoleError(...args)
    }

    // Also intercept console.warn for extension warnings
    console.warn = (...args: any[]) => {
      if (this.shouldSuppressConsoleMessage(args)) {
        return // Silently suppress extension warnings
      }

      this.originalConsoleWarn(...args)
    }
  }

  private setupWindowErrorHandling(): void {
    this.originalWindowErrorHandler = window.onerror

    window.onerror = (message, source, lineno, colno, error) => {
      if (this.shouldSuppressWindowError(message, source, error)) {
        this.suppressedCount++
        return true // Prevent default error handling
      }

      // Call original handler for legitimate errors
      if (this.originalWindowErrorHandler) {
        return this.originalWindowErrorHandler(message, source, lineno, colno, error)
      }
      return false
    }

    // Modern error event listener for better coverage
    window.addEventListener('error', (event) => {
      if (this.shouldSuppressErrorEvent(event)) {
        event.stopImmediatePropagation()
        event.preventDefault()
        this.suppressedCount++
      }
    }, true) // Use capture phase for early interception
  }

  private setupPromiseRejectionHandling(): void {
    window.addEventListener('unhandledrejection', (event) => {
      if (this.shouldSuppressPromiseRejection(event)) {
        event.preventDefault()
        event.stopImmediatePropagation()
        this.suppressedCount++
      }
    }, true) // Use capture phase
  }

  private setupPerformanceOptimizations(): void {
    // Debounce pattern matching for better performance
    const patternArray = Array.from(this.suppressedPatterns)

    // Pre-compile common patterns for faster matching
    this.suppressedPatterns.clear()
    patternArray.forEach(pattern => {
      this.suppressedPatterns.add(pattern)
    })
  }

  private shouldSuppressConsoleMessage(args: any[]): boolean {
    const message = args.join(' ').toLowerCase()
    return this.matchesExtensionPattern(message)
  }

  private shouldSuppressWindowError(message: any, source: any, error: any): boolean {
    const messageStr = String(message).toLowerCase()
    const sourceStr = String(source || '').toLowerCase()
    const errorStr = String(error || '').toLowerCase()

    return this.matchesExtensionPattern(messageStr) ||
           this.matchesExtensionPattern(sourceStr) ||
           this.matchesExtensionPattern(errorStr) ||
           sourceStr.includes('extension')
  }

  private shouldSuppressErrorEvent(event: ErrorEvent): boolean {
    const message = String(event.message || '').toLowerCase()
    const filename = String(event.filename || '').toLowerCase()
    const errorStr = String(event.error || '').toLowerCase()

    return this.matchesExtensionPattern(message) ||
           this.matchesExtensionPattern(filename) ||
           this.matchesExtensionPattern(errorStr) ||
           filename.includes('extension')
  }

  private shouldSuppressPromiseRejection(event: PromiseRejectionEvent): boolean {
    const reason = String(event.reason || '').toLowerCase()
    return this.matchesExtensionPattern(reason)
  }

  private matchesExtensionPattern(text: string): boolean {
    if (!text) return false

    // Fast path: check for common patterns first
    if (text.includes('runtime.lasterror') ||
        text.includes('extension') ||
        text.includes('message port closed')) {
      return true
    }

    // Comprehensive pattern matching
    for (const pattern of this.suppressedPatterns) {
      if (text.includes(pattern)) {
        return true
      }
    }

    return false
  }

  /**
   * Add custom error pattern to suppress
   */
  addSuppressedPattern(pattern: string): void {
    this.suppressedPatterns.add(pattern.toLowerCase())
  }

  /**
   * Remove error pattern from suppression list
   */
  removeSuppressedPattern(pattern: string): void {
    this.suppressedPatterns.delete(pattern.toLowerCase())
  }

  /**
   * Check if an error should be suppressed (public API)
   */
  shouldSuppressError(error: string | Error): boolean {
    const errorStr = String(error).toLowerCase()
    return this.matchesExtensionPattern(errorStr)
  }

  /**
   * Restore original error handlers (for cleanup)
   */
  restore(): void {
    if (!this.isInitialized) return

    console.error = this.originalConsoleError
    console.warn = this.originalConsoleWarn
    window.onerror = this.originalWindowErrorHandler
    this.isInitialized = false
  }

  /**
   * Get comprehensive statistics about suppressed errors
   */
  getStats(): ErrorStats {
    return {
      suppressedCount: this.suppressedCount,
      lastSuppressed: Date.now(),
      patterns: Array.from(this.suppressedPatterns),
      isActive: this.isInitialized && console.error !== this.originalConsoleError
    }
  }

  /**
   * Reset suppression counter (useful for testing)
   */
  resetStats(): void {
    this.suppressedCount = 0
  }

  /**
   * Enable debug mode to see what's being suppressed
   */
  enableDebugMode(): void {
    if (!import.meta.env.DEV) return

    const originalShouldSuppress = this.shouldSuppressConsoleMessage.bind(this)
    this.shouldSuppressConsoleMessage = (args: any[]) => {
      const shouldSuppress = originalShouldSuppress(args)
      if (shouldSuppress) {
        this.originalConsoleError(
          '%c🔍 DEBUG%c Would suppress:',
          'color: #FF9800; font-weight: bold',
          'color: #666',
          args.join(' ')
        )
      }
      return shouldSuppress
    }
  }
}

/**
 * Initialize the browser extension handler with enhanced error suppression.
 * This function should be called early in the application lifecycle.
 *
 * @param enableDebug - Enable debug mode to see suppressed errors (dev only)
 * @returns The handler instance for further configuration
 */
export function initializeBrowserExtensionHandler(enableDebug: boolean = false): BrowserExtensionHandler {
  const handler = BrowserExtensionHandler.getInstance()

  if (enableDebug && import.meta.env.DEV) {
    handler.enableDebugMode()
  }

  // Log initialization in development
  if (import.meta.env.DEV) {
    console.log(
      '%c🛡️ Browser Extension Handler%c Initialized with enhanced 2025 error suppression',
      'color: #4CAF50; font-weight: bold',
      'color: #666'
    )
  }

  return handler
}

/**
 * Quick setup function for common use cases
 */
export function setupBrowserExtensionSuppression(): void {
  initializeBrowserExtensionHandler(false)
}

// Export the class and default instance
export default BrowserExtensionHandler
export { BrowserExtensionHandler }
