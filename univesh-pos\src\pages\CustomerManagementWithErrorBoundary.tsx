/**
 * Customer Management with Error Boundary Wrapper
 */

import React from 'react'
import { withErrorBoundary } from '../components/error/ErrorBoundary'
import CustomerManagement from './CustomerManagement'
import { Box, Typography, Button } from '@mui/material'
import { People, Refresh } from '@mui/icons-material'
import { colors } from '../theme'

// Custom fallback UI for Customer Management errors
const CustomerManagementErrorFallback: React.FC = () => {
  const handleRetry = () => {
    window.location.reload()
  }

  return (
    <Box sx={{ p: 3, textAlign: 'center' }}>
      <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
        Customer Management
      </Typography>
      
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 300,
          bgcolor: colors.grey[50],
          borderRadius: 2,
          p: 4
        }}
      >
        <People 
          sx={{ 
            fontSize: 64, 
            color: colors.grey[400], 
            mb: 2 
          }} 
        />
        
        <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
          Customer Management Temporarily Unavailable
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          We're having trouble loading the customer data. Please try refreshing the page.
        </Typography>

        <Button
          variant="contained"
          startIcon={<Refresh />}
          onClick={handleRetry}
        >
          Refresh Page
        </Button>
      </Box>
    </Box>
  )
}

// Create the wrapped component
const CustomerManagementWithErrorBoundary = withErrorBoundary(CustomerManagement, {
  fallback: <CustomerManagementErrorFallback />,
  componentName: 'CustomerManagement',
  onError: (error, errorInfo) => {
    console.error('Customer Management Error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    })
  }
})

export default CustomerManagementWithErrorBoundary
