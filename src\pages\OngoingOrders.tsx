import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Paper,
  Divider
} from '@mui/material'
import {
  Restaurant,
  AccessTime,
  CheckCircle,
  Cancel,
  Add,
  Edit,
  Visibility,
  Kitchen,
  LocalShipping,
  TableRestaurant
} from '@mui/icons-material'
import { colors } from '../theme'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'

interface Order {
  id: string
  table_number: string | null
  order_type: 'dine_in' | 'delivery' | 'take_away'
  status: 'draft' | 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled'
  subtotal_amount: number
  tax_amount: number
  total_amount: number
  employee_id: string
  created_at: string
  updated_at: string
  profiles?: {
    full_name: string
  }
  order_items?: OrderItem[]
}

interface OrderItem {
  id: string
  product_id: string
  quantity: number
  unit_price_at_order: number
  item_subtotal: number
  products: {
    name: string
    description: string | null
  }
}

const OngoingOrders: React.FC = () => {
  const { user } = useAuth()
  const [orders, setOrders] = useState<Order[]>([])
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  useEffect(() => {
    fetchOrders()
    
    // Set up real-time subscription
    const subscription = supabase
      .channel('ongoing-orders')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'orders' },
        () => {
          fetchOrders()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const fetchOrders = async () => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          profiles!orders_employee_id_fkey(full_name),
          order_items(
            id,
            product_id,
            quantity,
            unit_price_at_order,
            item_subtotal,
            products(name, description)
          )
        `)
        .in('status', ['pending', 'preparing', 'ready'])
        .order('created_at', { ascending: false })

      if (error) throw error

      setOrders(data || [])
    } catch (error) {
      console.error('Error fetching orders:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch orders')
    } finally {
      setLoading(false)
    }
  }

  const updateOrderStatus = async (orderId: string, newStatus: 'preparing' | 'ready' | 'cancelled') => {
    try {
      setError(null)

      const { error } = await supabase
        .from('orders')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)

      if (error) throw error

      setSuccess(`Order status updated to ${newStatus}`)
      setTimeout(() => setSuccess(null), 3000)
      
      fetchOrders()
    } catch (error) {
      console.error('Error updating order status:', error)
      setError(error instanceof Error ? error.message : 'Failed to update order status')
    }
  }

  const openDetailsDialog = (order: Order) => {
    setSelectedOrder(order)
    setDetailsDialogOpen(true)
  }

  const closeDetailsDialog = () => {
    setDetailsDialogOpen(false)
    setSelectedOrder(null)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning'
      case 'preparing': return 'info'
      case 'ready': return 'success'
      case 'completed': return 'success'
      case 'cancelled': return 'error'
      default: return 'default'
    }
  }

  const getOrderTypeIcon = (orderType: string) => {
    switch (orderType) {
      case 'dine_in': return <TableRestaurant />
      case 'delivery': return <LocalShipping />
      case 'take_away': return <Restaurant />
      default: return <Restaurant />
    }
  }

  const getTimeElapsed = (createdAt: string) => {
    const now = new Date()
    const created = new Date(createdAt)
    const diffInMinutes = Math.floor((now.getTime() - created.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else {
      const hours = Math.floor(diffInMinutes / 60)
      const minutes = diffInMinutes % 60
      return `${hours}h ${minutes}m ago`
    }
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress size={60} />
      </Box>
    )
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
        Ongoing Orders
      </Typography>

      {/* Success/Error Messages */}
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Orders Grid */}
      {orders.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <Restaurant sx={{ fontSize: 64, color: colors.grey[400], mb: 2 }} />
            <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
              No ongoing orders
            </Typography>
            <Typography variant="body2" color="text.secondary">
              All orders have been completed or no new orders are pending.
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {orders.map((order) => (
            <Grid item xs={12} md={6} lg={4} key={order.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flex: 1 }}>
                  {/* Order Header */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      #{order.id.slice(0, 8)}
                    </Typography>
                    <Chip
                      label={order.status.toUpperCase()}
                      size="small"
                      color={getStatusColor(order.status) as any}
                    />
                  </Box>

                  {/* Order Info */}
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      {getOrderTypeIcon(order.order_type)}
                      <Typography variant="body2">
                        {order.order_type.replace('_', ' ').toUpperCase()}
                        {order.table_number && ` - Table ${order.table_number}`}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <AccessTime fontSize="small" />
                      <Typography variant="body2" color="text.secondary">
                        {getTimeElapsed(order.created_at)}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      By: {order.profiles?.full_name || 'Unknown'}
                    </Typography>
                  </Box>

                  {/* Order Items */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Items ({order.order_items?.length || 0}):
                    </Typography>
                    <List dense>
                      {order.order_items?.slice(0, 3).map((item) => (
                        <ListItem key={item.id} sx={{ px: 0, py: 0.5 }}>
                          <ListItemText
                            primary={
                              <Typography variant="body2">
                                {item.quantity}× {item.products.name}
                              </Typography>
                            }
                          />
                        </ListItem>
                      ))}
                      {(order.order_items?.length || 0) > 3 && (
                        <ListItem sx={{ px: 0, py: 0.5 }}>
                          <ListItemText
                            primary={
                              <Typography variant="body2" color="text.secondary">
                                +{(order.order_items?.length || 0) - 3} more items
                              </Typography>
                            }
                          />
                        </ListItem>
                      )}
                    </List>
                  </Box>

                  {/* Total */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: colors.primary.main }}>
                      Total: ${order.total_amount.toFixed(2)}
                    </Typography>
                  </Box>

                  {/* Action Buttons */}
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<Visibility />}
                      onClick={() => openDetailsDialog(order)}
                    >
                      Details
                    </Button>
                    
                    {order.status === 'pending' && (
                      <Button
                        size="small"
                        variant="contained"
                        color="info"
                        startIcon={<Kitchen />}
                        onClick={() => updateOrderStatus(order.id, 'preparing')}
                      >
                        Start Preparing
                      </Button>
                    )}
                    
                    {order.status === 'preparing' && (
                      <Button
                        size="small"
                        variant="contained"
                        color="success"
                        startIcon={<CheckCircle />}
                        onClick={() => updateOrderStatus(order.id, 'ready')}
                      >
                        Mark Ready
                      </Button>
                    )}
                    
                    <Button
                      size="small"
                      variant="outlined"
                      color="error"
                      startIcon={<Cancel />}
                      onClick={() => updateOrderStatus(order.id, 'cancelled')}
                    >
                      Cancel
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  )
}

export default OngoingOrders
