import React, { useState } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  Badge
} from '@mui/material'
import {
  Dashboard,
  Restaurant,
  Assignment,
  Receipt,
  Inventory,
  People,
  Assessment,
  Settings,
  AccountCircle,
  Logout,
  Notifications
} from '@mui/icons-material'
import { useAuth } from '../../contexts/AuthContext'
import { colors } from '../../theme'

const drawerWidth = 280

interface NavigationItem {
  text: string
  icon: React.ReactElement
  path: string
  roles?: string[]
}

const navigationItems: NavigationItem[] = [
  { text: 'Dashboard', icon: <Dashboard />, path: '/dashboard' },
  { text: 'Menu & Orders', icon: <Restaurant />, path: '/menu' },
  { text: 'Ongoing Orders', icon: <Assignment />, path: '/orders' },
  { text: 'Bills', icon: <Receipt />, path: '/bills' },
  { text: 'Manage Inventory', icon: <Inventory />, path: '/inventory', roles: ['admin', 'manager'] },
  { text: 'Customers', icon: <People />, path: '/customers', roles: ['admin', 'manager'] },
  { text: 'Reports', icon: <Assessment />, path: '/reports', roles: ['admin', 'manager'] },
  { text: 'Settings', icon: <Settings />, path: '/settings' }
]

const DashboardLayout: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { profile, signOut } = useAuth()
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleProfileMenuClose = () => {
    setAnchorEl(null)
  }

  const handleSignOut = async () => {
    handleProfileMenuClose()
    await signOut()
  }

  const handleNavigate = (path: string) => {
    navigate(path)
  }

  const isMenuOpen = Boolean(anchorEl)

  // Filter navigation items based on user role
  const filteredNavigationItems = navigationItems.filter(item => {
    if (!item.roles) return true
    return item.roles.includes(profile?.role?.role_name || 'cashier')
  })

  return (
    <Box sx={{ display: 'flex' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          width: `calc(100% - ${drawerWidth}px)`,
          ml: `${drawerWidth}px`,
          backgroundColor: colors.background.paper,
          color: colors.text.primary,
          boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.1)'
        }}
      >
        <Toolbar>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, fontWeight: 600 }}>
            {navigationItems.find(item => item.path === location.pathname)?.text || 'Univesh POS'}
          </Typography>

          {/* Notifications */}
          <IconButton
            size="large"
            color="inherit"
            sx={{ mr: 2 }}
          >
            <Badge badgeContent={3} color="error">
              <Notifications />
            </Badge>
          </IconButton>

          {/* Profile Menu */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" sx={{ display: { xs: 'none', sm: 'block' } }}>
              {profile?.full_name}
            </Typography>
            <IconButton
              size="large"
              edge="end"
              aria-label="account of current user"
              aria-controls="primary-search-account-menu"
              aria-haspopup="true"
              onClick={handleProfileMenuOpen}
              color="inherit"
            >
              <Avatar sx={{ width: 32, height: 32, bgcolor: colors.primary.main }}>
                {profile?.full_name?.charAt(0) || <AccountCircle />}
              </Avatar>
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Profile Menu */}
      <Menu
        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right'
        }}
        keepMounted
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
        open={isMenuOpen}
        onClose={handleProfileMenuClose}
      >
        <MenuItem onClick={() => { handleProfileMenuClose(); navigate('/settings') }}>
          <ListItemIcon>
            <Settings fontSize="small" />
          </ListItemIcon>
          Settings
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleSignOut}>
          <ListItemIcon>
            <Logout fontSize="small" />
          </ListItemIcon>
          Sign Out
        </MenuItem>
      </Menu>

      {/* Sidebar */}
      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
            backgroundColor: colors.background.paper,
            borderRight: `1px solid ${colors.grey[300]}`
          }
        }}
        variant="permanent"
        anchor="left"
      >
        {/* Logo */}
        <Box
          sx={{
            p: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderBottom: `1px solid ${colors.grey[300]}`
          }}
        >
          <Typography
            variant="h5"
            sx={{
              fontWeight: 700,
              color: colors.primary.main
            }}
          >
            Univesh POS
          </Typography>
        </Box>

        {/* User Info */}
        <Box sx={{ p: 2, borderBottom: `1px solid ${colors.grey[300]}` }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: colors.primary.main }}>
              {profile?.full_name?.charAt(0)}
            </Avatar>
            <Box>
              <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                {profile?.full_name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {profile?.role?.role_name?.toUpperCase()} • {profile?.sales_id_number}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Navigation */}
        <List sx={{ flexGrow: 1, pt: 1 }}>
          {filteredNavigationItems.map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                onClick={() => handleNavigate(item.path)}
                selected={location.pathname === item.path}
                sx={{
                  mx: 1,
                  borderRadius: 2,
                  '&.Mui-selected': {
                    backgroundColor: colors.primary.main,
                    color: colors.primary.contrastText,
                    '&:hover': {
                      backgroundColor: colors.primary.dark
                    },
                    '& .MuiListItemIcon-root': {
                      color: colors.primary.contrastText
                    }
                  }
                }}
              >
                <ListItemIcon
                  sx={{
                    color: location.pathname === item.path ? colors.primary.contrastText : colors.text.secondary
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontWeight: location.pathname === item.path ? 600 : 400
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Drawer>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          bgcolor: colors.grey[50],
          p: 3,
          mt: 8, // Account for AppBar height
          minHeight: 'calc(100vh - 64px)'
        }}
      >
        <Outlet />
      </Box>
    </Box>
  )
}

export default DashboardLayout
