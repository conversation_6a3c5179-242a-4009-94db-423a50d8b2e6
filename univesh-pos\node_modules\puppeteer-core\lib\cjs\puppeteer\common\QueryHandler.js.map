{"version": 3, "file": "QueryHandler.js", "sourceRoot": "", "sources": ["../../../../src/common/QueryHandler.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,0EAA+D;AAI/D,uDAAiD;AACjD,qDAA2E;AAE3E,2DAA4D;AAC5D,6CAAqC;AA6BrC;;GAEG;AACH,MAAa,YAAY;IACvB,oEAAoE;IACpE,MAAM,CAAC,gBAAgB,CAAoB;IAC3C,MAAM,CAAC,aAAa,CAAiB;IAErC,MAAM,KAAK,cAAc;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,aAAa,GAAG,IAAA,iCAAmB,EAC9C,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE;YACtC,MAAM,gBAAgB,GACpB,WAAW,CAAC,kBAAkB,CAAC,CAAC;YAClC,MAAM,OAAO,GAAG,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAChE,IAAI,KAAK,EAAE,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBACnC,OAAO,MAAM,CAAC;YAChB,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,EACD;YACE,gBAAgB,EAAE,IAAA,+BAAiB,EAAC,IAAI,CAAC,gBAAgB,CAAC;SAC3D,CACF,CAAC,CAAC;IACL,CAAC;IAED,MAAM,KAAK,iBAAiB;QAC1B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAA,iCAAmB,EACjD,KAAK,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa;YAC5C,MAAM,aAAa,GAAkB,WAAW,CAAC,eAAe,CAAC,CAAC;YAClE,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAClE,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,MAAM,CAAC;YACf,CAAC;QACH,CAAC,EACD;YACE,aAAa,EAAE,IAAA,+BAAiB,EAAC,IAAI,CAAC,aAAa,CAAC;SACrD,CACF,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CACpB,OAA4B,EAC5B,QAAgB;;;YAEhB,MAAM,MAAM,kCAAG,MAAM,OAAO,CAAC,cAAc,CACzC,IAAI,CAAC,iBAAiB,EACtB,QAAQ,EACR,oBAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;gBACvB,OAAO,OAAO,CAAC,aAAa,CAAC;YAC/B,CAAC,CAAC,CACH,QAAA,CAAC;YACF,KAAK,CAAC,CAAC,IAAA,2CAAuB,EAAC,MAAM,CAAC,CAAC;;;;;;;;;KACxC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,QAAQ,CACnB,OAA4B,EAC5B,QAAgB;;;YAEhB,MAAM,MAAM,kCAAG,MAAM,OAAO,CAAC,cAAc,CACzC,IAAI,CAAC,cAAc,EACnB,QAAQ,EACR,oBAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;gBACvB,OAAO,OAAO,CAAC,aAAa,CAAC;YAC/B,CAAC,CAAC,CACH,QAAA,CAAC;YACF,IAAI,CAAC,CAAC,yCAAgB,IAAI,MAAM,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;;;;;;;;;KACtB;IAED;;;;;;OAMG;IACH,MAAM,CAAC,KAAK,CAAC,OAAO,CAClB,cAA2C,EAC3C,QAAgB,EAChB,OAEC;;;YAED,IAAI,KAAa,CAAC;YAClB,MAAM,OAAO,kCAAG,MAAM,CAAC,KAAK,IAAI,EAAE;gBAChC,IAAI,CAAC,CAAC,yCAAgB,IAAI,cAAc,CAAC,EAAE,CAAC;oBAC1C,KAAK,GAAG,cAAc,CAAC;oBACvB,OAAO;gBACT,CAAC;gBACD,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;gBAC7B,OAAO,MAAM,KAAK,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;YACjE,CAAC,CAAC,EAAE,QAAA,CAAC;YAEL,MAAM,EAAC,OAAO,GAAG,KAAK,EAAE,MAAM,GAAG,KAAK,EAAE,OAAO,EAAE,MAAM,EAAC,GAAG,OAAO,CAAC;YACnE,MAAM,OAAO,GAAG,OAAO,IAAI,MAAM,CAAC,CAAC,gCAAoB,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;YAEzE,IAAI,CAAC;;;oBACH,MAAM,EAAE,cAAc,EAAE,CAAC;oBAEzB,MAAM,MAAM,kCAAG,MAAM,KAAK,CAAC,aAAa,EAAE,CAAC,eAAe,CACxD,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;wBACtD,MAAM,aAAa,GAAG,aAAa,CAAC,cAAc,CAChD,KAAK,CACW,CAAC;wBACnB,MAAM,IAAI,GAAG,MAAM,aAAa,CAC9B,IAAI,IAAI,QAAQ,EAChB,QAAQ,EACR,aAAa,CACd,CAAC;wBACF,OAAO,aAAa,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;oBACtD,CAAC,EACD;wBACE,OAAO;wBACP,IAAI,EAAE,OAAO;wBACb,OAAO;wBACP,MAAM;qBACP,EACD,oBAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;wBACvB,OAAO,OAAO,CAAC,aAAa,CAAC;oBAC/B,CAAC,CAAC,EACF,IAAA,+BAAiB,EAAC,IAAI,CAAC,cAAc,CAAC,EACtC,QAAQ,EACR,OAAO,EACP,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAC5C,QAAA,CAAC;oBAEF,IAAI,MAAM,EAAE,OAAO,EAAE,CAAC;wBACpB,MAAM,MAAM,CAAC,MAAM,CAAC;oBACtB,CAAC;oBAED,IAAI,CAAC,CAAC,yCAAgB,IAAI,MAAM,CAAC,EAAE,CAAC;wBAClC,OAAO,IAAI,CAAC;oBACd,CAAC;oBACD,OAAO,MAAM,KAAK,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;;;;;;;;;aACvD;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,IAAA,0BAAW,EAAC,KAAK,CAAC,EAAE,CAAC;oBACxB,MAAM,KAAK,CAAC;gBACd,CAAC;gBACD,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAChC,MAAM,KAAK,CAAC;gBACd,CAAC;gBACD,KAAK,CAAC,OAAO,GAAG,0BAA0B,QAAQ,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC;gBAChF,MAAM,KAAK,CAAC;YACd,CAAC;;;;;;;;;KACF;CACF;AAxKD,oCAwKC"}