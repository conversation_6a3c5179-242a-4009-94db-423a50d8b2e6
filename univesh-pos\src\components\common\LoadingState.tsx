import React from 'react'
import {
  Box,
  CircularProgress,
  Typography,
  Skeleton,
  Card,
  CardContent,
  Grid
} from '@mui/material'
import { colors } from '../../theme'

interface LoadingStateProps {
  variant?: 'spinner' | 'skeleton' | 'card' | 'table' | 'dashboard'
  message?: string
  size?: 'small' | 'medium' | 'large'
  fullScreen?: boolean
  rows?: number
  columns?: number
}

const LoadingState: React.FC<LoadingStateProps> = ({
  variant = 'spinner',
  message = 'Loading...',
  size = 'medium',
  fullScreen = false,
  rows = 5,
  columns = 4
}) => {
  const getSpinnerSize = () => {
    switch (size) {
      case 'small': return 30
      case 'medium': return 50
      case 'large': return 70
      default: return 50
    }
  }

  const containerSx = fullScreen ? {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    zIndex: 9999
  } : {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: size === 'large' ? 400 : size === 'medium' ? 200 : 100,
    width: '100%'
  }

  if (variant === 'spinner') {
    return (
      <Box sx={containerSx}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress 
            size={getSpinnerSize()} 
            sx={{ color: colors.primary.main, mb: 2 }}
          />
          <Typography 
            variant={size === 'large' ? 'h6' : 'body1'} 
            color="text.secondary"
          >
            {message}
          </Typography>
        </Box>
      </Box>
    )
  }

  if (variant === 'skeleton') {
    return (
      <Box sx={{ width: '100%', p: 2 }}>
        {Array.from({ length: rows }).map((_, index) => (
          <Skeleton
            key={index}
            variant="rectangular"
            height={size === 'large' ? 60 : size === 'medium' ? 40 : 30}
            sx={{ mb: 1, borderRadius: 1 }}
          />
        ))}
      </Box>
    )
  }

  if (variant === 'card') {
    return (
      <Grid container spacing={3}>
        {Array.from({ length: columns }).map((_, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Skeleton variant="rectangular" height={120} sx={{ mb: 2, borderRadius: 1 }} />
                <Skeleton variant="text" height={30} sx={{ mb: 1 }} />
                <Skeleton variant="text" height={20} width="60%" />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    )
  }

  if (variant === 'table') {
    return (
      <Box sx={{ width: '100%' }}>
        {/* Table Header */}
        <Box sx={{ display: 'flex', gap: 2, mb: 2, p: 2 }}>
          {Array.from({ length: columns }).map((_, index) => (
            <Skeleton
              key={index}
              variant="text"
              height={30}
              sx={{ flex: 1 }}
            />
          ))}
        </Box>
        
        {/* Table Rows */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <Box key={rowIndex} sx={{ display: 'flex', gap: 2, mb: 1, p: 2 }}>
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton
                key={colIndex}
                variant="text"
                height={25}
                sx={{ flex: 1 }}
              />
            ))}
          </Box>
        ))}
      </Box>
    )
  }

  if (variant === 'dashboard') {
    return (
      <Box sx={{ width: '100%' }}>
        {/* Metrics Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {Array.from({ length: 4 }).map((_, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ flex: 1 }}>
                      <Skeleton variant="text" height={40} width="80%" sx={{ mb: 1 }} />
                      <Skeleton variant="text" height={20} width="60%" />
                    </Box>
                    <Skeleton variant="circular" width={40} height={40} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Charts/Tables */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Skeleton variant="text" height={30} width="40%" sx={{ mb: 2 }} />
                <Skeleton variant="rectangular" height={300} sx={{ borderRadius: 1 }} />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Skeleton variant="text" height={30} width="40%" sx={{ mb: 2 }} />
                <Box>
                  {Array.from({ length: 5 }).map((_, index) => (
                    <Box key={index} sx={{ display: 'flex', gap: 2, mb: 1 }}>
                      <Skeleton variant="text" height={25} sx={{ flex: 1 }} />
                      <Skeleton variant="text" height={25} sx={{ flex: 1 }} />
                      <Skeleton variant="text" height={25} sx={{ flex: 1 }} />
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    )
  }

  return null
}

export default LoadingState
