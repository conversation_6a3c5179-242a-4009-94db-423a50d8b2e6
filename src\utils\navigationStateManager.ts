/**
 * Navigation State Manager
 *
 * Handles navigation state persistence and recovery to prevent
 * loading screen persistence issues after visiting problematic pages.
 *
 * Features:
 * - Page state tracking
 * - Loading state timeout management
 * - Navigation recovery mechanisms
 * - Error state handling
 */

import React from 'react'

interface PageState {
  path: string
  loading: boolean
  error: string | null
  lastVisited: number
  loadStartTime: number
  timeoutId?: NodeJS.Timeout
}

interface NavigationConfig {
  defaultTimeout: number
  maxTimeout: number
  retryAttempts: number
  debugMode: boolean
}

class NavigationStateManager {
  private static instance: NavigationStateManager
  private pageStates: Map<string, PageState> = new Map()
  private config: NavigationConfig
  private listeners: Set<(state: PageState) => void> = new Set()

  private constructor() {
    this.config = {
      defaultTimeout: 10000, // 10 seconds
      maxTimeout: 30000,     // 30 seconds
      retryAttempts: 3,
      debugMode: process.env.NODE_ENV === 'development'
    }

    // Cleanup old states periodically
    setInterval(() => this.cleanupOldStates(), 60000) // Every minute
  }

  static getInstance(): NavigationStateManager {
    if (!NavigationStateManager.instance) {
      NavigationStateManager.instance = new NavigationStateManager()
    }
    return NavigationStateManager.instance
  }

  /**
   * Start loading state for a page
   */
  startLoading(path: string, timeoutMs?: number): void {
    const timeout = Math.min(timeoutMs || this.config.defaultTimeout, this.config.maxTimeout)
    const now = Date.now()

    // Clear existing timeout if any
    const existingState = this.pageStates.get(path)
    if (existingState?.timeoutId) {
      clearTimeout(existingState.timeoutId)
    }

    // Set timeout to automatically resolve loading state
    const timeoutId = setTimeout(() => {
      this.handleLoadingTimeout(path)
    }, timeout)

    const state: PageState = {
      path,
      loading: true,
      error: null,
      lastVisited: now,
      loadStartTime: now,
      timeoutId
    }

    this.pageStates.set(path, state)
    this.notifyListeners(state)

    if (this.config.debugMode) {
      console.log(`🔄 [NavigationStateManager] Started loading for ${path} (timeout: ${timeout}ms)`)
    }
  }

  /**
   * Complete loading state for a page
   */
  completeLoading(path: string): void {
    const state = this.pageStates.get(path)
    if (!state) return

    // Clear timeout
    if (state.timeoutId) {
      clearTimeout(state.timeoutId)
    }

    const updatedState: PageState = {
      ...state,
      loading: false,
      error: null,
      timeoutId: undefined
    }

    this.pageStates.set(path, updatedState)
    this.notifyListeners(updatedState)

    if (this.config.debugMode) {
      const loadTime = Date.now() - state.loadStartTime
      console.log(`✅ [NavigationStateManager] Completed loading for ${path} (${loadTime}ms)`)
    }
  }

  /**
   * Set error state for a page
   */
  setError(path: string, error: string): void {
    const state = this.pageStates.get(path)
    if (!state) return

    // Clear timeout
    if (state.timeoutId) {
      clearTimeout(state.timeoutId)
    }

    const updatedState: PageState = {
      ...state,
      loading: false,
      error,
      timeoutId: undefined
    }

    this.pageStates.set(path, updatedState)
    this.notifyListeners(updatedState)

    if (this.config.debugMode) {
      console.error(`❌ [NavigationStateManager] Error for ${path}: ${error}`)
    }
  }

  /**
   * Get current state for a page
   */
  getState(path: string): PageState | null {
    return this.pageStates.get(path) || null
  }

  /**
   * Check if a page is currently loading
   */
  isLoading(path: string): boolean {
    const state = this.pageStates.get(path)
    return state?.loading || false
  }

  /**
   * Clear state for a page
   */
  clearState(path: string): void {
    const state = this.pageStates.get(path)
    if (state?.timeoutId) {
      clearTimeout(state.timeoutId)
    }
    this.pageStates.delete(path)

    if (this.config.debugMode) {
      console.log(`🧹 [NavigationStateManager] Cleared state for ${path}`)
    }
  }

  /**
   * Clear all states
   */
  clearAllStates(): void {
    this.pageStates.forEach((state) => {
      if (state.timeoutId) {
        clearTimeout(state.timeoutId)
      }
    })
    this.pageStates.clear()

    if (this.config.debugMode) {
      console.log(`🧹 [NavigationStateManager] Cleared all states`)
    }
  }

  /**
   * Add listener for state changes
   */
  addListener(listener: (state: PageState) => void): void {
    this.listeners.add(listener)
  }

  /**
   * Remove listener
   */
  removeListener(listener: (state: PageState) => void): void {
    this.listeners.delete(listener)
  }

  /**
   * Handle loading timeout
   */
  private handleLoadingTimeout(path: string): void {
    const state = this.pageStates.get(path)
    if (!state || !state.loading) return

    const loadTime = Date.now() - state.loadStartTime

    if (this.config.debugMode) {
      console.warn(`⏰ [NavigationStateManager] Loading timeout for ${path} after ${loadTime}ms`)
    }

    // Force complete loading with timeout error
    const updatedState: PageState = {
      ...state,
      loading: false,
      error: `Loading timeout after ${loadTime}ms`,
      timeoutId: undefined
    }

    this.pageStates.set(path, updatedState)
    this.notifyListeners(updatedState)
  }

  /**
   * Cleanup old states (older than 5 minutes)
   */
  private cleanupOldStates(): void {
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
    const toDelete: string[] = []

    this.pageStates.forEach((state, path) => {
      if (state.lastVisited < fiveMinutesAgo && !state.loading) {
        toDelete.push(path)
      }
    })

    toDelete.forEach(path => this.clearState(path))

    if (this.config.debugMode && toDelete.length > 0) {
      console.log(`🧹 [NavigationStateManager] Cleaned up ${toDelete.length} old states`)
    }
  }

  /**
   * Notify all listeners of state change
   */
  private notifyListeners(state: PageState): void {
    this.listeners.forEach(listener => {
      try {
        listener(state)
      } catch (error) {
        console.error('[NavigationStateManager] Error in listener:', error)
      }
    })
  }

  /**
   * Get statistics for debugging
   */
  getStats(): {
    totalStates: number
    loadingStates: number
    errorStates: number
    oldestState: number | null
  } {
    const states = Array.from(this.pageStates.values())
    const now = Date.now()

    return {
      totalStates: states.length,
      loadingStates: states.filter(s => s.loading).length,
      errorStates: states.filter(s => s.error).length,
      oldestState: states.length > 0 ? Math.min(...states.map(s => now - s.lastVisited)) : null
    }
  }
}

export default NavigationStateManager

// Hook for using navigation state in React components
export const useNavigationState = (path: string) => {
  const [state, setState] = React.useState<PageState | null>(null)
  const manager = NavigationStateManager.getInstance()

  React.useEffect(() => {
    // Get initial state
    setState(manager.getState(path))

    // Listen for changes
    const listener = (updatedState: PageState) => {
      if (updatedState.path === path) {
        setState(updatedState)
      }
    }

    manager.addListener(listener)

    return () => {
      manager.removeListener(listener)
    }
  }, [path, manager])

  const startLoading = React.useCallback((timeoutMs?: number) => {
    manager.startLoading(path, timeoutMs)
  }, [path, manager])

  const completeLoading = React.useCallback(() => {
    manager.completeLoading(path)
  }, [path, manager])

  const setError = React.useCallback((error: string) => {
    manager.setError(path, error)
  }, [path, manager])

  const clearState = React.useCallback(() => {
    manager.clearState(path)
  }, [path, manager])

  return {
    state,
    isLoading: state?.loading || false,
    error: state?.error || null,
    startLoading,
    completeLoading,
    setError,
    clearState
  }
}
