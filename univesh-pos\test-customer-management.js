/**
 * Customer Management Test
 * 
 * This script tests the complete customer management functionality:
 * 1. Customer CRUD operations
 * 2. Customer search and filtering
 * 3. Customer order history
 * 4. Customer analytics
 * 5. Customer data validation
 */

import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testCustomerManagement() {
  console.log('🧪 Testing Customer Management System...\n');
  
  try {
    // Authenticate
    console.log('🔐 Step 1: Authenticate...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (authError) {
      console.error('❌ Authentication failed:', authError);
      return false;
    }
    
    console.log('✅ Authenticated successfully');
    
    // Test customer creation
    console.log('\n👥 Step 2: Test customer creation...');
    
    const testCustomers = [
      {
        full_name: 'Alice Johnson',
        email: '<EMAIL>',
        phone_number: '******-0201',
        billing_address: '123 Test Street, Test City, TC 12345',
        shipping_address: '123 Test Street, Test City, TC 12345'
      },
      {
        full_name: 'Bob Smith',
        email: '<EMAIL>',
        phone_number: '******-0202',
        billing_address: '456 Demo Avenue, Demo City, DC 67890',
        shipping_address: '456 Demo Avenue, Demo City, DC 67890'
      },
      {
        full_name: 'Carol Williams',
        email: '<EMAIL>',
        phone_number: '******-0203',
        billing_address: '789 Sample Road, Sample City, SC 11111',
        shipping_address: '789 Sample Road, Sample City, SC 11111'
      }
    ];
    
    const createdCustomers = [];
    
    for (const customerData of testCustomers) {
      const { data: newCustomer, error: customerError } = await supabase
        .from('customers')
        .insert(customerData)
        .select()
        .single();
      
      if (customerError) {
        console.error('❌ Error creating customer:', customerError);
        continue;
      }
      
      createdCustomers.push(newCustomer);
      console.log(`✅ Customer created: ${newCustomer.full_name} (${newCustomer.email})`);
    }
    
    // Test customer updates
    console.log('\n📝 Step 3: Test customer updates...');
    
    if (createdCustomers.length > 0) {
      const customerToUpdate = createdCustomers[0];
      const updatedData = {
        full_name: 'Alice Johnson-Updated',
        phone_number: '******-0299',
        billing_address: '123 Updated Street, Updated City, UC 54321',
        updated_at: new Date().toISOString()
      };
      
      const { error: updateError } = await supabase
        .from('customers')
        .update(updatedData)
        .eq('id', customerToUpdate.id);
      
      if (updateError) {
        console.error('❌ Error updating customer:', updateError);
      } else {
        console.log(`✅ Customer updated: ${customerToUpdate.full_name} -> ${updatedData.full_name}`);
      }
    }
    
    // Test customer queries
    console.log('\n📊 Step 4: Test customer queries...');
    
    // Get all customers
    const { data: allCustomers, error: customersError } = await supabase
      .from('customers')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (customersError) {
      console.error('❌ Error fetching customers:', customersError);
    } else {
      console.log(`✅ Customers query successful: ${allCustomers?.length || 0} customers found`);
      
      const testCustomersFound = allCustomers?.filter(c => 
        createdCustomers.some(cc => cc.id === c.id)
      ) || [];
      
      console.log(`   Test customers found: ${testCustomersFound.length}`);
      testCustomersFound.forEach(customer => {
        console.log(`   - ${customer.full_name}: ${customer.email} (${customer.phone_number})`);
      });
    }
    
    // Test customer search functionality
    console.log('\n🔍 Step 5: Test customer search...');
    
    // Search by name
    const { data: nameSearchResults, error: nameSearchError } = await supabase
      .from('customers')
      .select('*')
      .ilike('full_name', '%Alice%');
    
    if (nameSearchError) {
      console.error('❌ Error searching by name:', nameSearchError);
    } else {
      console.log(`✅ Name search successful: ${nameSearchResults?.length || 0} customers found`);
    }
    
    // Search by email
    const { data: emailSearchResults, error: emailSearchError } = await supabase
      .from('customers')
      .select('*')
      .ilike('email', '%test.com%');
    
    if (emailSearchError) {
      console.error('❌ Error searching by email:', emailSearchError);
    } else {
      console.log(`✅ Email search successful: ${emailSearchResults?.length || 0} customers found`);
    }
    
    // Test customer order history
    console.log('\n📈 Step 6: Test customer order history...');
    
    // Get admin profile for creating test orders
    const { data: adminProfile } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', '<EMAIL>')
      .single();
    
    const adminId = adminProfile.id;
    
    // Create test orders for customers
    if (createdCustomers.length > 0) {
      const testCustomer = createdCustomers[0];
      
      // Create a test order for this customer
      const orderData = {
        table_number: 'T99',
        order_type: 'dine_in',
        status: 'completed',
        subtotal_amount: 25.00,
        tax_amount: 1.25,
        discount_amount: 0,
        complementary_amount: 0,
        total_amount: 26.25,
        employee_id: adminId,
        customer_id: testCustomer.id,
        notes: 'Test order for customer history'
      };
      
      const { data: testOrder, error: orderError } = await supabase
        .from('orders')
        .insert(orderData)
        .select()
        .single();
      
      if (orderError) {
        console.error('❌ Error creating test order:', orderError);
      } else {
        console.log(`✅ Test order created for customer: ${testCustomer.full_name}`);
        
        // Query customer order history
        const { data: customerOrders, error: historyError } = await supabase
          .from('orders')
          .select(`
            *,
            order_items(
              quantity,
              unit_price_at_order,
              item_subtotal,
              products(name)
            )
          `)
          .eq('customer_id', testCustomer.id)
          .order('created_at', { ascending: false });
        
        if (historyError) {
          console.error('❌ Error fetching customer order history:', historyError);
        } else {
          console.log(`✅ Customer order history: ${customerOrders?.length || 0} orders found`);
          customerOrders?.forEach(order => {
            console.log(`   - Order ${order.id.slice(0, 8)}: $${order.total_amount} (${order.status})`);
          });
        }
        
        // Clean up test order
        await supabase.from('orders').delete().eq('id', testOrder.id);
      }
    }
    
    // Test customer analytics
    console.log('\n📊 Step 7: Test customer analytics...');
    
    // Customer registration trends
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const { data: recentCustomers, error: recentError } = await supabase
      .from('customers')
      .select('*')
      .gte('created_at', thirtyDaysAgo.toISOString());
    
    if (recentError) {
      console.error('❌ Error fetching recent customers:', recentError);
    } else {
      console.log(`✅ Customer analytics:`);
      console.log(`   New customers (30 days): ${recentCustomers?.length || 0}`);
    }
    
    // Customer contact method distribution
    const { data: contactStats, error: contactError } = await supabase
      .from('customers')
      .select('email, phone_number');
    
    if (contactError) {
      console.error('❌ Error fetching contact stats:', contactError);
    } else {
      const hasEmail = contactStats?.filter(c => c.email).length || 0;
      const hasPhone = contactStats?.filter(c => c.phone_number).length || 0;
      const hasBoth = contactStats?.filter(c => c.email && c.phone_number).length || 0;
      
      console.log(`   Contact method distribution:`);
      console.log(`     Has email: ${hasEmail}`);
      console.log(`     Has phone: ${hasPhone}`);
      console.log(`     Has both: ${hasBoth}`);
    }
    
    // Test customer data validation
    console.log('\n✅ Step 8: Test customer data validation...');
    
    // Test duplicate email validation
    const duplicateEmailData = {
      full_name: 'Duplicate Email Test',
      email: createdCustomers[0]?.email,
      phone_number: '******-9999'
    };
    
    const { error: duplicateEmailError } = await supabase
      .from('customers')
      .insert(duplicateEmailData);
    
    if (duplicateEmailError) {
      console.log('✅ Email uniqueness constraint working (duplicate email rejected)');
    } else {
      console.log('⚠️  Email uniqueness constraint not working');
    }
    
    // Test duplicate phone validation
    const duplicatePhoneData = {
      full_name: 'Duplicate Phone Test',
      email: '<EMAIL>',
      phone_number: createdCustomers[0]?.phone_number
    };
    
    const { error: duplicatePhoneError } = await supabase
      .from('customers')
      .insert(duplicatePhoneData);
    
    if (duplicatePhoneError) {
      console.log('✅ Phone uniqueness constraint working (duplicate phone rejected)');
    } else {
      console.log('⚠️  Phone uniqueness constraint not working');
    }
    
    // Test customer deletion
    console.log('\n🗑️ Step 9: Test customer deletion...');
    
    if (createdCustomers.length > 1) {
      const customerToDelete = createdCustomers[createdCustomers.length - 1];
      
      const { error: deleteError } = await supabase
        .from('customers')
        .delete()
        .eq('id', customerToDelete.id);
      
      if (deleteError) {
        console.error('❌ Error deleting customer:', deleteError);
      } else {
        console.log(`✅ Customer deleted: ${customerToDelete.full_name}`);
      }
    }
    
    // Cleanup remaining test data
    console.log('\n🧹 Step 10: Cleanup test data...');
    
    for (const customer of createdCustomers.slice(0, -1)) { // Skip the deleted one
      await supabase.from('customers').delete().eq('id', customer.id);
    }
    
    console.log('✅ Test data cleaned up');
    
    return true;
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    return false;
  } finally {
    await supabase.auth.signOut();
    console.log('\n👋 Signed out');
  }
}

// Run the test
testCustomerManagement().then(success => {
  if (success) {
    console.log('\n🎉 Customer Management Test PASSED!');
    console.log('\n✅ Summary:');
    console.log('   • Customer CRUD operations working');
    console.log('   • Customer search functionality working');
    console.log('   • Customer order history working');
    console.log('   • Customer analytics working');
    console.log('   • Data validation constraints working');
    console.log('   • Customer deletion working');
    console.log('   • Complete customer management functional');
  } else {
    console.log('\n❌ Customer Management Test FAILED!');
  }
}).catch(error => {
  console.error('\n💥 Test execution failed:', error);
});
