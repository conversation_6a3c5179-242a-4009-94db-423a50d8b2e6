# 🚀 PRODUCTION DEPLOYMENT GUIDE
## Univesh Restaurant POS System

### **OVERVIEW**
This guide provides comprehensive instructions for deploying the Univesh Restaurant POS system to production environments with enterprise-level security, performance, and reliability.

---

## **PRE-DEPLOYMENT CHECKLIST**

### **1. Environment Preparation**
- [ ] Production Supabase project created and configured
- [ ] Domain name registered and DNS configured
- [ ] SSL certificate obtained (Let's Encrypt or commercial)
- [ ] CDN configured (Cloudflare, AWS CloudFront, etc.)
- [ ] Monitoring services set up (Sentry, DataDog, etc.)

### **2. Security Verification**
- [ ] All environment variables secured
- [ ] RLS policies implemented and tested
- [ ] API keys rotated for production
- [ ] Security headers configured
- [ ] HTTPS enforced
- [ ] Content Security Policy implemented

### **3. Performance Optimization**
- [ ] Bundle size optimized (< 1MB initial load)
- [ ] Images optimized and compressed
- [ ] Lazy loading implemented
- [ ] Caching strategies configured
- [ ] Database queries optimized

### **4. Testing Completion**
- [ ] All unit tests passing
- [ ] Integration tests completed
- [ ] End-to-end tests successful
- [ ] Performance tests passed
- [ ] Security audit completed
- [ ] Accessibility compliance verified

---

## **DEPLOYMENT METHODS**

### **METHOD 1: Vercel Deployment (Recommended)**

#### **Step 1: Prepare Repository**
```bash
# Ensure all changes are committed
git add .
git commit -m "Production ready deployment"
git push origin main
```

#### **Step 2: Configure Vercel**
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy to production
vercel --prod
```

#### **Step 3: Environment Variables**
In Vercel dashboard, add these environment variables:
```
NODE_ENV=production
VITE_SUPABASE_URL=your-production-supabase-url
VITE_SUPABASE_ANON_KEY=your-production-anon-key
VITE_APP_ENV=production
VITE_ENABLE_DEVTOOLS=false
```

#### **Step 4: Domain Configuration**
- Add custom domain in Vercel dashboard
- Configure DNS records
- Verify SSL certificate

### **METHOD 2: Netlify Deployment**

#### **Step 1: Build Configuration**
Create `netlify.toml`:
```toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_ENV = "production"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
```

#### **Step 2: Deploy**
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login and deploy
netlify login
netlify deploy --prod --dir=dist
```

### **METHOD 3: AWS S3 + CloudFront**

#### **Step 1: Build Application**
```bash
npm run build
```

#### **Step 2: S3 Configuration**
```bash
# Upload to S3
aws s3 sync dist/ s3://your-bucket-name --delete

# Configure bucket for static hosting
aws s3 website s3://your-bucket-name --index-document index.html --error-document index.html
```

#### **Step 3: CloudFront Setup**
- Create CloudFront distribution
- Configure origin to S3 bucket
- Set up custom error pages
- Configure caching rules

### **METHOD 4: Docker Deployment**

#### **Step 1: Create Dockerfile**
```dockerfile
# Multi-stage build for production
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### **Step 2: Nginx Configuration**
Create `nginx.conf`:
```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    server {
        listen 80;
        server_name localhost;
        
        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
        
        # Security headers
        add_header X-Frame-Options "DENY" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
    }
}
```

#### **Step 3: Build and Deploy**
```bash
# Build Docker image
docker build -t univesh-pos .

# Run container
docker run -p 80:80 univesh-pos
```

---

## **POST-DEPLOYMENT CONFIGURATION**

### **1. Database Setup**

#### **Supabase Production Configuration**
```sql
-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
-- ... (apply to all tables)

-- Create production admin user
INSERT INTO auth.users (email, encrypted_password, email_confirmed_at)
VALUES ('<EMAIL>', crypt('SecurePassword123!', gen_salt('bf')), now());
```

#### **Initial Data Setup**
```sql
-- Insert roles
INSERT INTO public.roles (role_name) VALUES 
('admin'), ('manager'), ('cashier');

-- Insert system settings
INSERT INTO public.system_settings (
    default_tax_rate, 
    currency_symbol, 
    auto_print_receipts
) VALUES (0.05, '$', true);

-- Create admin profile
INSERT INTO public.profiles (
    id, 
    sales_id, 
    full_name, 
    email, 
    role_id
) VALUES (
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    'ADMIN001',
    'System Administrator',
    '<EMAIL>',
    (SELECT id FROM public.roles WHERE role_name = 'admin')
);
```

### **2. Monitoring Setup**

#### **Error Tracking (Sentry)**
```javascript
// Add to main.tsx
import * as Sentry from "@sentry/react";

Sentry.init({
  dsn: "YOUR_SENTRY_DSN",
  environment: "production",
  tracesSampleRate: 0.1,
});
```

#### **Analytics (Google Analytics)**
```javascript
// Add to index.html
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### **3. Performance Monitoring**

#### **Core Web Vitals Tracking**
```javascript
// Implement in monitoring.ts
import {getCLS, getFID, getFCP, getLCP, getTTFB} from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```

### **4. Backup Strategy**

#### **Database Backups**
- Enable automated backups in Supabase
- Set up daily backup schedule
- Test restore procedures
- Store backups in multiple locations

#### **Application Backups**
- Version control with Git
- Tagged releases
- Docker image versioning
- Configuration backups

---

## **SECURITY HARDENING**

### **1. Environment Security**
```bash
# Secure environment variables
export VITE_SUPABASE_URL="https://your-project.supabase.co"
export VITE_SUPABASE_ANON_KEY="your-anon-key"

# Never commit these to version control
echo ".env.production" >> .gitignore
```

### **2. Network Security**
- Configure firewall rules
- Enable DDoS protection
- Set up rate limiting
- Configure CORS properly

### **3. Application Security**
- Implement CSP headers
- Enable HSTS
- Configure secure cookies
- Validate all inputs

---

## **PERFORMANCE OPTIMIZATION**

### **1. Caching Strategy**
```javascript
// Service Worker for caching
const CACHE_NAME = 'univesh-pos-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});
```

### **2. CDN Configuration**
- Configure static asset caching
- Enable compression (Gzip/Brotli)
- Set appropriate cache headers
- Use image optimization

### **3. Database Optimization**
- Index frequently queried columns
- Optimize query performance
- Enable connection pooling
- Monitor query execution times

---

## **MONITORING & MAINTENANCE**

### **1. Health Checks**
```javascript
// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version
  });
});
```

### **2. Log Management**
- Centralized logging
- Log rotation
- Error alerting
- Performance monitoring

### **3. Update Strategy**
- Staged deployments
- Blue-green deployment
- Rollback procedures
- Version management

---

## **TROUBLESHOOTING**

### **Common Issues**

#### **Build Failures**
```bash
# Clear cache and rebuild
npm run clean
npm ci
npm run build
```

#### **Environment Variable Issues**
```bash
# Verify environment variables
echo $VITE_SUPABASE_URL
echo $VITE_SUPABASE_ANON_KEY
```

#### **Database Connection Issues**
- Check Supabase project status
- Verify API keys
- Check network connectivity
- Review RLS policies

### **Emergency Procedures**

#### **Rollback Deployment**
```bash
# Vercel rollback
vercel rollback

# Git rollback
git revert HEAD
git push origin main
```

#### **Database Recovery**
```sql
-- Restore from backup
pg_restore -d your_database backup_file.sql
```

---

## **SUPPORT & MAINTENANCE**

### **Contact Information**
- **Technical Support**: <EMAIL>
- **Emergency Contact**: +1-XXX-XXX-XXXX
- **Documentation**: https://docs.univesh.com

### **Maintenance Schedule**
- **Daily**: Automated backups, health checks
- **Weekly**: Performance review, security updates
- **Monthly**: Full system audit, dependency updates
- **Quarterly**: Disaster recovery testing

---

## **COMPLIANCE & DOCUMENTATION**

### **Required Documentation**
- [ ] Security audit report
- [ ] Performance test results
- [ ] Backup and recovery procedures
- [ ] Incident response plan
- [ ] User training materials

### **Compliance Requirements**
- [ ] PCI DSS (if processing payments)
- [ ] GDPR (if handling EU data)
- [ ] SOC 2 (if required by customers)
- [ ] Local regulations compliance

---

**🎉 Congratulations! Your Univesh Restaurant POS system is now production-ready!**

For additional support or questions, please refer to the documentation or contact the development team.
