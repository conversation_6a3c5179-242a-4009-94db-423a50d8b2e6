<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Extension Error Test</title>
</head>
<body>
    <h1>Browser Extension Error Test</h1>
    <p>Open the console to see if browser extension errors are suppressed.</p>
    
    <button onclick="testExtensionErrors()">Test Extension Errors</button>
    <button onclick="testNormalErrors()">Test Normal Errors</button>
    
    <script>
        // Simulate browser extension errors
        function testExtensionErrors() {
            console.log('Testing browser extension errors...');
            
            // These should be suppressed
            console.error('Unchecked runtime.lastError: The message port closed before a response was received.');
            console.error('Unchecked runtime.lastError: Could not establish connection. Receiving end does not exist.');
            console.error('Extension context invalidated.');
            
            // Test promise rejection
            Promise.reject(new Error('runtime.lastError: Extension error')).catch(() => {});
        }
        
        function testNormalErrors() {
            console.log('Testing normal errors...');
            
            // These should NOT be suppressed
            console.error('This is a normal application error');
            console.error('Network request failed');
        }
        
        // Import and initialize the browser extension handler
        if (typeof window !== 'undefined') {
            import('./src/utils/browserExtensionHandler.js').then(({ BrowserExtensionHandler }) => {
                console.log('Browser extension handler loaded');
                const handler = BrowserExtensionHandler.getInstance();
                console.log('Handler stats:', handler.getStats());
            }).catch(err => {
                console.log('Could not load browser extension handler:', err);
            });
        }
    </script>
</body>
</html>
