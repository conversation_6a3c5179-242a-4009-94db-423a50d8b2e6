// Authentication Test Script
// This script tests the authentication functionality and error handling

import { supabase } from '../lib/supabase'
import { Au<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NetworkChe<PERSON>, SessionManager } from '../utils/authErrorHandler'

interface TestResult {
  test: string
  status: 'PASS' | 'FAIL' | 'SKIP'
  message: string
  duration: number
}

class AuthenticationTester {
  private results: TestResult[] = []

  private async runTest(testName: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now()
    try {
      await testFn()
      this.results.push({
        test: testName,
        status: 'PASS',
        message: 'Test completed successfully',
        duration: Date.now() - startTime
      })
    } catch (error) {
      this.results.push({
        test: testName,
        status: 'FAIL',
        message: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      })
    }
  }

  async testSupabaseConnection(): Promise<void> {
    await this.runTest('Supabase Connection', async () => {
      const { data, error } = await supabase.from('roles').select('count').limit(1)
      if (error) {
        throw new Error(`Supabase connection failed: ${error.message}`)
      }
      console.log('✅ Supabase connection successful')
    })
  }

  async testAuthErrorHandler(): Promise<void> {
    await this.runTest('Auth Error Handler', async () => {
      // Test network error handling
      const networkError = new TypeError('Failed to fetch')
      const errorInfo = AuthErrorHandler.handleAuthError(networkError)
      
      if (errorInfo.code !== 'NETWORK_ERROR') {
        throw new Error('Network error not properly handled')
      }

      // Test invalid credentials error
      const authError = { message: 'Invalid login credentials' }
      const authErrorInfo = AuthErrorHandler.handleAuthError(authError)
      
      if (authErrorInfo.code !== 'INVALID_CREDENTIALS') {
        throw new Error('Auth error not properly handled')
      }

      console.log('✅ Auth error handler working correctly')
    })
  }

  async testNetworkChecker(): Promise<void> {
    await this.runTest('Network Checker', async () => {
      const isOnline = NetworkChecker.getStatus()
      console.log(`Network status: ${isOnline ? 'Online' : 'Offline'}`)
      
      // Test connectivity check
      const connectivityResult = await NetworkChecker.checkConnectivity()
      console.log(`Connectivity check: ${connectivityResult ? 'Connected' : 'Disconnected'}`)
      
      console.log('✅ Network checker functioning')
    })
  }

  async testSessionRefresh(): Promise<void> {
    await this.runTest('Session Refresh', async () => {
      try {
        // Get current session
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.log('⚠️ No active session to refresh')
          return
        }

        if (!session) {
          console.log('⚠️ No session found')
          return
        }

        // Test session refresh
        const refreshResult = await SessionManager.refreshSession(supabase)
        console.log('✅ Session refresh mechanism working')
      } catch (error) {
        console.log('⚠️ Session refresh test skipped (no active session)')
      }
    })
  }

  async testTokenRefreshEndpoint(): Promise<void> {
    await this.runTest('Token Refresh Endpoint', async () => {
      try {
        // Test the specific endpoint that was failing
        const response = await fetch('https://kufsqfbiilphymiehwet.supabase.co/auth/v1/token?grant_type=refresh_token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY
          },
          body: JSON.stringify({
            refresh_token: 'test_token'
          })
        })

        // We expect this to fail with 400 (bad request) rather than network error
        if (response.status === 400) {
          console.log('✅ Token refresh endpoint is accessible (400 expected for invalid token)')
        } else {
          throw new Error(`Unexpected response status: ${response.status}`)
        }
      } catch (error) {
        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
          throw new Error('Network connectivity issue with token refresh endpoint')
        }
        // Other errors are expected (like 400 for invalid token)
        console.log('✅ Token refresh endpoint accessible')
      }
    })
  }

  async testAuthConfiguration(): Promise<void> {
    await this.runTest('Auth Configuration', async () => {
      // Test environment variables
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
      const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY

      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Missing Supabase environment variables')
      }

      if (!supabaseUrl.includes('kufsqfbiilphymiehwet.supabase.co')) {
        throw new Error('Incorrect Supabase URL configuration')
      }

      console.log('✅ Auth configuration is correct')
    })
  }

  async testRetryMechanism(): Promise<void> {
    await this.runTest('Retry Mechanism', async () => {
      let attemptCount = 0
      
      const testOperation = async () => {
        attemptCount++
        if (attemptCount < 3) {
          throw new Error('Simulated failure')
        }
        return 'success'
      }

      const result = await AuthErrorHandler.retryOperation(
        testOperation,
        'TEST_RETRY',
        3
      )

      if (result !== 'success' || attemptCount !== 3) {
        throw new Error('Retry mechanism not working correctly')
      }

      console.log('✅ Retry mechanism working correctly')
    })
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Authentication Tests...\n')

    await this.testAuthConfiguration()
    await this.testSupabaseConnection()
    await this.testAuthErrorHandler()
    await this.testNetworkChecker()
    await this.testTokenRefreshEndpoint()
    await this.testRetryMechanism()
    await this.testSessionRefresh()

    this.printResults()
  }

  private printResults(): void {
    console.log('\n📊 Test Results Summary:')
    console.log('=' .repeat(60))

    const passed = this.results.filter(r => r.status === 'PASS').length
    const failed = this.results.filter(r => r.status === 'FAIL').length
    const skipped = this.results.filter(r => r.status === 'SKIP').length

    this.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⏭️'
      console.log(`${icon} ${result.test}: ${result.message} (${result.duration}ms)`)
    })

    console.log('=' .repeat(60))
    console.log(`Total: ${this.results.length} | Passed: ${passed} | Failed: ${failed} | Skipped: ${skipped}`)
    
    if (failed === 0) {
      console.log('🎉 All tests passed! Authentication system is working correctly.')
    } else {
      console.log('⚠️ Some tests failed. Please review the issues above.')
    }
  }
}

// Export for use in browser console or testing
export const runAuthTests = async () => {
  const tester = new AuthenticationTester()
  await tester.runAllTests()
}

// Auto-run tests if in development mode
if (import.meta.env.DEV) {
  console.log('🔧 Authentication test utilities loaded. Run `runAuthTests()` in console to test.')
}

export default AuthenticationTester
