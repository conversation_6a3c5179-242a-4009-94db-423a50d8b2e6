// Simple test script to verify Supabase authentication
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testAuth() {
  console.log('🚀 Testing Complete Authentication Flow...')
  console.log('=' .repeat(50))

  try {
    // Test 1: Basic connection
    console.log('\n1️⃣ Testing Supabase connection...')
    const { data, error } = await supabase.from('roles').select('count').limit(1)
    if (error) {
      console.error('❌ Connection test failed:', error)
      return
    }
    console.log('✅ Connection successful')

    // Test 2: Profile fetch for existing user
    console.log('\n2️⃣ Testing profile fetch...')
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', 'a1b2c3d4-e5f6-7890-abcd-ef1234567890')
      .single()

    if (profileError) {
      console.error('❌ Profile fetch failed:', profileError)
      return
    } else {
      console.log('✅ Profile fetch successful')
      console.log('   - User ID:', profileData.id)
      console.log('   - Sales ID:', profileData.sales_id_number)
      console.log('   - Name:', profileData.full_name)
      console.log('   - Email:', profileData.email)
    }

    // Test 3: Authentication
    console.log('\n3️⃣ Testing authentication...')
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    })

    if (signInError) {
      console.error('❌ Sign in failed:', signInError)
      return
    } else {
      console.log('✅ Sign in successful')
      console.log('   - User ID:', signInData.user?.id)
      console.log('   - Email:', signInData.user?.email)
      console.log('   - Session expires:', new Date(signInData.session?.expires_at * 1000))
    }

    // Test 4: Authenticated role fetch
    console.log('\n4️⃣ Testing authenticated role fetch...')
    const { data: roleData, error: roleError } = await supabase
      .from('roles')
      .select('id, role_name')
      .eq('id', profileData.role_id)
      .single()

    if (roleError) {
      console.error('❌ Authenticated role fetch failed:', roleError)
    } else {
      console.log('✅ Authenticated role fetch successful')
      console.log('   - Role ID:', roleData.id)
      console.log('   - Role Name:', roleData.role_name)
    }

    // Test 5: Sales ID lookup (simulating login flow)
    console.log('\n5️⃣ Testing Sales ID lookup (login flow simulation)...')
    const { data: salesLookup, error: salesError } = await supabase
      .from('profiles')
      .select('email')
      .eq('sales_id_number', 'ADMIN001')
      .single()

    if (salesError) {
      console.error('❌ Sales ID lookup failed:', salesError)
    } else {
      console.log('✅ Sales ID lookup successful')
      console.log('   - Sales ID: ADMIN001 → Email:', salesLookup.email)
    }

    // Test 6: Sign out
    console.log('\n6️⃣ Testing sign out...')
    const { error: signOutError } = await supabase.auth.signOut()

    if (signOutError) {
      console.error('❌ Sign out failed:', signOutError)
    } else {
      console.log('✅ Sign out successful')
    }

    console.log('\n' + '=' .repeat(50))
    console.log('🎉 All authentication tests passed!')
    console.log('\n📋 Test Summary:')
    console.log('   ✅ Database connection')
    console.log('   ✅ Profile data retrieval')
    console.log('   ✅ User authentication')
    console.log('   ✅ Role-based access')
    console.log('   ✅ Sales ID lookup')
    console.log('   ✅ Session management')
    console.log('\n🔑 Login Credentials:')
    console.log('   Sales ID: ADMIN001')
    console.log('   Password: admin123')
    console.log('   Email: <EMAIL>')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testAuth()
