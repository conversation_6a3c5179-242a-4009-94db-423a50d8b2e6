// Simple test script to verify Supabase authentication
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testAuth() {
  console.log('Testing Supabase connection...')
  
  try {
    // Test basic connection
    const { data, error } = await supabase.from('roles').select('count').limit(1)
    if (error) {
      console.error('Connection test failed:', error)
      return
    }
    console.log('✅ Connection successful')

    // Test profile fetch for existing user
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', 'a1b2c3d4-e5f6-7890-abcd-ef1234567890')
      .single()

    if (profileError) {
      console.error('❌ Profile fetch failed:', profileError)
    } else {
      console.log('✅ Profile fetch successful:', profileData)
      
      // Test role fetch
      const { data: roleData, error: roleError } = await supabase
        .from('roles')
        .select('id, role_name')
        .eq('id', profileData.role_id)
        .single()

      if (roleError) {
        console.error('❌ Role fetch failed:', roleError)
      } else {
        console.log('✅ Role fetch successful:', roleData)
      }
    }

    // Test sign in with admin user
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    })

    if (signInError) {
      console.error('❌ Sign in failed:', signInError)
    } else {
      console.log('✅ Sign in successful:', signInData.user?.id)

      // Now test role fetch with authenticated user
      const { data: roleData2, error: roleError2 } = await supabase
        .from('roles')
        .select('id, role_name')
        .eq('id', profileData.role_id)
        .single()

      if (roleError2) {
        console.error('❌ Authenticated role fetch failed:', roleError2)
      } else {
        console.log('✅ Authenticated role fetch successful:', roleData2)
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testAuth()
