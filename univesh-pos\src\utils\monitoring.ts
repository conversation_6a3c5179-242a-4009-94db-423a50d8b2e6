// Production Monitoring and Analytics Setup
// Comprehensive monitoring for performance, errors, and user analytics

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  metadata?: Record<string, any>
}

interface ErrorReport {
  message: string
  stack?: string
  url: string
  userAgent: string
  timestamp: number
  userId?: string
  sessionId: string
  metadata?: Record<string, any>
}

interface UserEvent {
  event: string
  properties: Record<string, any>
  timestamp: number
  userId?: string
  sessionId: string
}

class ProductionMonitoring {
  private sessionId: string
  private userId?: string
  private isProduction: boolean
  private metricsBuffer: PerformanceMetric[] = []
  private errorBuffer: ErrorReport[] = []
  private eventBuffer: UserEvent[] = []

  constructor() {
    this.sessionId = this.generateSessionId()
    this.isProduction = process.env.NODE_ENV === 'production'
    
    if (this.isProduction) {
      this.initializeMonitoring()
    }
  }

  // Initialize all monitoring systems
  private initializeMonitoring(): void {
    this.setupErrorTracking()
    this.setupPerformanceMonitoring()
    this.setupUserAnalytics()
    this.setupNetworkMonitoring()
    this.startMetricsCollection()
  }

  // Error tracking setup
  private setupErrorTracking(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.reportError({
        message: event.message,
        stack: event.error?.stack,
        url: event.filename || window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.sessionId,
        metadata: {
          line: event.lineno,
          column: event.colno,
          type: 'javascript'
        }
      })
    })

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError({
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.sessionId,
        metadata: {
          type: 'promise_rejection',
          reason: event.reason
        }
      })
    })

    // React error boundary integration
    this.setupReactErrorBoundary()
  }

  // Performance monitoring setup
  private setupPerformanceMonitoring(): void {
    // Core Web Vitals
    this.measureCoreWebVitals()
    
    // Navigation timing
    this.measureNavigationTiming()
    
    // Resource timing
    this.measureResourceTiming()
    
    // Custom performance marks
    this.setupCustomMarks()
  }

  // User analytics setup
  private setupUserAnalytics(): void {
    // Page view tracking
    this.trackPageView()
    
    // User interaction tracking
    this.setupInteractionTracking()
    
    // Business metrics tracking
    this.setupBusinessMetrics()
  }

  // Network monitoring
  private setupNetworkMonitoring(): void {
    // API call monitoring
    this.monitorFetchRequests()
    
    // Connection quality monitoring
    this.monitorConnectionQuality()
  }

  // Core Web Vitals measurement
  private measureCoreWebVitals(): void {
    // Largest Contentful Paint (LCP)
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      
      this.recordMetric({
        name: 'LCP',
        value: lastEntry.startTime,
        timestamp: Date.now(),
        metadata: { element: lastEntry.element?.tagName }
      })
    }).observe({ entryTypes: ['largest-contentful-paint'] })

    // First Input Delay (FID)
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        this.recordMetric({
          name: 'FID',
          value: entry.processingStart - entry.startTime,
          timestamp: Date.now(),
          metadata: { eventType: entry.name }
        })
      })
    }).observe({ entryTypes: ['first-input'] })

    // Cumulative Layout Shift (CLS)
    let clsValue = 0
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      })
      
      this.recordMetric({
        name: 'CLS',
        value: clsValue,
        timestamp: Date.now()
      })
    }).observe({ entryTypes: ['layout-shift'] })
  }

  // Navigation timing measurement
  private measureNavigationTiming(): void {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      this.recordMetric({
        name: 'TTFB',
        value: navigation.responseStart - navigation.requestStart,
        timestamp: Date.now()
      })
      
      this.recordMetric({
        name: 'DOMContentLoaded',
        value: navigation.domContentLoadedEventEnd - navigation.navigationStart,
        timestamp: Date.now()
      })
      
      this.recordMetric({
        name: 'LoadComplete',
        value: navigation.loadEventEnd - navigation.navigationStart,
        timestamp: Date.now()
      })
    })
  }

  // Resource timing measurement
  private measureResourceTiming(): void {
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        if (entry.duration > 1000) { // Only track slow resources
          this.recordMetric({
            name: 'SlowResource',
            value: entry.duration,
            timestamp: Date.now(),
            metadata: {
              name: entry.name,
              type: (entry as any).initiatorType
            }
          })
        }
      })
    }).observe({ entryTypes: ['resource'] })
  }

  // Custom performance marks
  private setupCustomMarks(): void {
    // Mark important application events
    this.markEvent('app_start')
    
    // Listen for route changes
    window.addEventListener('popstate', () => {
      this.markEvent('route_change')
    })
  }

  // Page view tracking
  private trackPageView(): void {
    this.trackEvent('page_view', {
      url: window.location.href,
      title: document.title,
      referrer: document.referrer
    })
  }

  // Interaction tracking
  private setupInteractionTracking(): void {
    // Click tracking
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        this.trackEvent('button_click', {
          text: target.textContent?.trim(),
          id: target.id,
          className: target.className
        })
      }
    })

    // Form submission tracking
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement
      this.trackEvent('form_submit', {
        id: form.id,
        action: form.action,
        method: form.method
      })
    })
  }

  // Business metrics tracking
  private setupBusinessMetrics(): void {
    // Track POS-specific events
    this.setupPOSMetrics()
  }

  // POS-specific metrics
  private setupPOSMetrics(): void {
    // These would be called from the application components
    // Examples:
    // this.trackBusinessEvent('order_created', { amount, items })
    // this.trackBusinessEvent('payment_processed', { method, amount })
    // this.trackBusinessEvent('product_added', { productId, category })
  }

  // Monitor fetch requests
  private monitorFetchRequests(): void {
    const originalFetch = window.fetch
    
    window.fetch = async (...args) => {
      const startTime = performance.now()
      const url = args[0] as string
      
      try {
        const response = await originalFetch(...args)
        const duration = performance.now() - startTime
        
        this.recordMetric({
          name: 'API_Call',
          value: duration,
          timestamp: Date.now(),
          metadata: {
            url,
            status: response.status,
            success: response.ok
          }
        })
        
        return response
      } catch (error) {
        const duration = performance.now() - startTime
        
        this.recordMetric({
          name: 'API_Error',
          value: duration,
          timestamp: Date.now(),
          metadata: {
            url,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        })
        
        throw error
      }
    }
  }

  // Monitor connection quality
  private monitorConnectionQuality(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      
      this.recordMetric({
        name: 'ConnectionSpeed',
        value: connection.downlink,
        timestamp: Date.now(),
        metadata: {
          effectiveType: connection.effectiveType,
          rtt: connection.rtt
        }
      })
      
      connection.addEventListener('change', () => {
        this.recordMetric({
          name: 'ConnectionChange',
          value: connection.downlink,
          timestamp: Date.now(),
          metadata: {
            effectiveType: connection.effectiveType,
            rtt: connection.rtt
          }
        })
      })
    }
  }

  // React error boundary integration
  private setupReactErrorBoundary(): void {
    // This would be integrated with the ErrorBoundary component
    window.addEventListener('react-error', ((event: CustomEvent) => {
      this.reportError({
        message: event.detail.error.message,
        stack: event.detail.error.stack,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.sessionId,
        metadata: {
          type: 'react_error',
          componentStack: event.detail.errorInfo?.componentStack
        }
      })
    }) as EventListener)
  }

  // Start metrics collection and periodic sending
  private startMetricsCollection(): void {
    // Send metrics every 30 seconds
    setInterval(() => {
      this.flushMetrics()
    }, 30000)

    // Send on page unload
    window.addEventListener('beforeunload', () => {
      this.flushMetrics()
    })
  }

  // Public methods for application use
  public setUserId(userId: string): void {
    this.userId = userId
  }

  public markEvent(eventName: string): void {
    performance.mark(eventName)
    this.recordMetric({
      name: `Mark_${eventName}`,
      value: performance.now(),
      timestamp: Date.now()
    })
  }

  public recordMetric(metric: PerformanceMetric): void {
    this.metricsBuffer.push(metric)
  }

  public reportError(error: ErrorReport): void {
    this.errorBuffer.push(error)
    
    // Send critical errors immediately
    if (this.isCriticalError(error)) {
      this.sendErrors([error])
    }
  }

  public trackEvent(event: string, properties: Record<string, any> = {}): void {
    this.eventBuffer.push({
      event,
      properties,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId
    })
  }

  public trackBusinessEvent(event: string, properties: Record<string, any> = {}): void {
    this.trackEvent(`business_${event}`, properties)
  }

  // Flush all buffered data
  private flushMetrics(): void {
    if (this.metricsBuffer.length > 0) {
      this.sendMetrics([...this.metricsBuffer])
      this.metricsBuffer = []
    }

    if (this.errorBuffer.length > 0) {
      this.sendErrors([...this.errorBuffer])
      this.errorBuffer = []
    }

    if (this.eventBuffer.length > 0) {
      this.sendEvents([...this.eventBuffer])
      this.eventBuffer = []
    }
  }

  // Send data to monitoring services
  private sendMetrics(metrics: PerformanceMetric[]): void {
    if (!this.isProduction) return
    
    // Send to your monitoring service
    // Example: Datadog, New Relic, custom endpoint
    console.log('Sending metrics:', metrics)
  }

  private sendErrors(errors: ErrorReport[]): void {
    if (!this.isProduction) return
    
    // Send to error tracking service
    // Example: Sentry, Bugsnag, custom endpoint
    console.log('Sending errors:', errors)
  }

  private sendEvents(events: UserEvent[]): void {
    if (!this.isProduction) return
    
    // Send to analytics service
    // Example: Google Analytics, Mixpanel, custom endpoint
    console.log('Sending events:', events)
  }

  // Utility methods
  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  private isCriticalError(error: ErrorReport): boolean {
    return error.message.includes('ChunkLoadError') ||
           error.message.includes('Network Error') ||
           error.message.includes('Authentication')
  }
}

// Export singleton instance
export const monitoring = new ProductionMonitoring()

// Export for manual usage
export default ProductionMonitoring
