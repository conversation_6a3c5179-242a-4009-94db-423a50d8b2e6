# Security Headers for Production Deployment
# This file is used by Netlify, Vercel, and other static hosting providers
# For other hosting providers, configure these headers in your web server

/*
  # Security Headers (2025 Best Practices)
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  
  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co wss://*.supabase.co; frame-ancestors 'none'; base-uri 'self'; form-action 'self'; object-src 'none'; media-src 'self'
  
  # Performance and Caching
  Cache-Control: public, max-age=31536000, immutable
  
  # HTTPS Enforcement
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

# HTML files - shorter cache for dynamic content
/*.html
  Cache-Control: public, max-age=0, must-revalidate
  
# API routes (if any)
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
  
# Static assets - longer cache
/assets/*
  Cache-Control: public, max-age=31536000, immutable
