{"name": "univesh-pos", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "security:check": "echo 'Checking security headers...' && curl -I http://localhost:5173/ | grep -E '(X-Frame-Options|Content-Security-Policy|X-Content-Type-Options)'", "security:test": "npm run dev & sleep 5 && npm run security:check && pkill -f vite"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/x-charts": "^8.5.2", "@mui/x-data-grid": "^8.5.2", "@supabase/supabase-js": "^2.50.0", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "playwright": "^1.53.0", "puppeteer": "^24.10.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}