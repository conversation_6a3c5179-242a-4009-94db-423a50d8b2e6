/**
 * Manage Inventory with Error Boundary Wrapper
 */

import React from 'react'
import { withErrorBoundary } from '../components/error/ErrorBoundary'
import ManageInventory from './ManageInventory'
import { Box, Typography, Button } from '@mui/material'
import { Inventory, Refresh } from '@mui/icons-material'
import { colors } from '../theme'

// Custom fallback UI for Inventory errors
const InventoryErrorFallback: React.FC = () => {
  const handleRetry = () => {
    window.location.reload()
  }

  return (
    <Box sx={{ p: 3, textAlign: 'center' }}>
      <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
        Manage Inventory
      </Typography>
      
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 300,
          bgcolor: colors.grey[50],
          borderRadius: 2,
          p: 4
        }}
      >
        <Inventory 
          sx={{ 
            fontSize: 64, 
            color: colors.grey[400], 
            mb: 2 
          }} 
        />
        
        <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
          Inventory Management Temporarily Unavailable
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          We're having trouble loading the inventory data. Please try refreshing the page.
        </Typography>

        <Button
          variant="contained"
          startIcon={<Refresh />}
          onClick={handleRetry}
        >
          Refresh Page
        </Button>
      </Box>
    </Box>
  )
}

// Create the wrapped component
const ManageInventoryWithErrorBoundary = withErrorBoundary(ManageInventory, {
  fallback: <InventoryErrorFallback />,
  componentName: 'ManageInventory',
  onError: (error, errorInfo) => {
    console.error('Inventory Error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    })
  }
})

export default ManageInventoryWithErrorBoundary
