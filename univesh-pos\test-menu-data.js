/**
 * Test Menu Data Loading
 * 
 * This script tests if the menu data (categories and products) loads properly from Supabase
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testMenuData() {
  console.log('🧪 Testing Menu Data Loading...\n');
  
  try {
    // First, authenticate
    console.log('🔐 Step 1: Authenticate...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (authError) {
      console.error('❌ Authentication failed:', authError);
      return false;
    }
    
    console.log('✅ Authenticated successfully');
    
    // Test categories loading
    console.log('\n📂 Step 2: Test categories loading...');
    
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('categories')
      .select('*')
      .order('name');
    
    if (categoriesError) {
      console.error('❌ Error fetching categories:', categoriesError);
    } else {
      console.log(`✅ Categories loaded: ${categoriesData?.length || 0} categories`);
      if (categoriesData && categoriesData.length > 0) {
        categoriesData.forEach((category, index) => {
          console.log(`   ${index + 1}. ${category.name} (${category.description || 'No description'})`);
        });
      }
    }
    
    // Test products loading
    console.log('\n🍽️ Step 3: Test products loading...');
    
    const { data: productsData, error: productsError } = await supabase
      .from('products')
      .select(`
        *,
        categories(name)
      `)
      .eq('is_active', true)
      .order('name');
    
    if (productsError) {
      console.error('❌ Error fetching products:', productsError);
    } else {
      console.log(`✅ Products loaded: ${productsData?.length || 0} products`);
      if (productsData && productsData.length > 0) {
        productsData.slice(0, 10).forEach((product, index) => {
          console.log(`   ${index + 1}. ${product.name} - $${product.price} (Stock: ${product.current_stock})`);
        });
        if (productsData.length > 10) {
          console.log(`   ... and ${productsData.length - 10} more products`);
        }
      }
    }
    
    // Test RLS policies
    console.log('\n🔒 Step 4: Test RLS policies...');
    
    // Test without authentication
    await supabase.auth.signOut();
    
    const { data: unauthProducts, error: unauthError } = await supabase
      .from('products')
      .select('*')
      .limit(1);
    
    if (unauthError) {
      console.log('✅ RLS working: Unauthenticated access blocked');
      console.log(`   Error: ${unauthError.message}`);
    } else {
      console.log('⚠️  RLS issue: Unauthenticated access allowed');
    }
    
    // Re-authenticate for final tests
    await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    // Test specific product queries
    console.log('\n🔍 Step 5: Test specific queries...');
    
    // Test products by category
    if (categoriesData && categoriesData.length > 0) {
      const firstCategory = categoriesData[0];
      const { data: categoryProducts, error: categoryError } = await supabase
        .from('products')
        .select('*')
        .eq('category_id', firstCategory.id)
        .eq('is_active', true);
      
      if (categoryError) {
        console.error(`❌ Error fetching products for category ${firstCategory.name}:`, categoryError);
      } else {
        console.log(`✅ Products in category "${firstCategory.name}": ${categoryProducts?.length || 0}`);
      }
    }
    
    // Test product stock levels
    const { data: lowStockProducts, error: stockError } = await supabase
      .from('products')
      .select('name, current_stock')
      .eq('is_active', true)
      .lt('current_stock', 10);
    
    if (stockError) {
      console.error('❌ Error checking stock levels:', stockError);
    } else {
      console.log(`📦 Low stock products (< 10): ${lowStockProducts?.length || 0}`);
      if (lowStockProducts && lowStockProducts.length > 0) {
        lowStockProducts.forEach(product => {
          console.log(`   - ${product.name}: ${product.current_stock} remaining`);
        });
      }
    }
    
    return true;
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    return false;
  } finally {
    // Sign out
    await supabase.auth.signOut();
    console.log('\n👋 Signed out');
  }
}

// Run the test
testMenuData().then(success => {
  if (success) {
    console.log('\n🎉 Menu Data Test COMPLETED!');
    console.log('✅ All menu data queries working properly');
  } else {
    console.log('\n❌ Menu Data Test FAILED!');
  }
}).catch(error => {
  console.error('\n💥 Test execution failed:', error);
});
