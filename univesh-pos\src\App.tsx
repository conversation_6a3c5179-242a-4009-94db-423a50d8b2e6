import React, { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate, Outlet } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import { CssBaseline } from '@mui/material'
import { AuthProvider } from './contexts/AuthContext'
import ProtectedRoute from './components/auth/ProtectedRoute'
import LoginPage from './pages/LoginPage'
import DashboardLayout from './components/layout/DashboardLayout'
import theme from './theme'
import { initializeBrowserExtensionHandler } from './utils/browserExtensionHandler'

// Import page components
import Dashboard from './pages/Dashboard'
import MenuScreenWithErrorBoundary from './pages/MenuScreenWithErrorBoundary'
import OngoingOrders from './pages/OngoingOrders'
import Bills from './pages/Bills'
import ManageInventory from './pages/ManageInventory'
import CustomerManagement from './pages/CustomerManagement'
import Reports from './pages/Reports'
import Settings from './pages/Settings'

// Layout wrapper component
const LayoutWrapper: React.FC = () => {
  return (
    <DashboardLayout>
      <Outlet />
    </DashboardLayout>
  )
}

function App() {
  // Initialize browser extension error handling
  useEffect(() => {
    initializeBrowserExtensionHandler(process.env.NODE_ENV === 'development')
  }, [])

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Router>
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<LoginPage />} />

            {/* Protected Routes */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <LayoutWrapper />
                </ProtectedRoute>
              }
            >
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="menu" element={<MenuScreenWithErrorBoundary />} />
              <Route path="orders" element={<OngoingOrders />} />
              <Route path="bills" element={<Bills />} />
              <Route
                path="inventory"
                element={
                  <ProtectedRoute allowedRoles={['admin', 'manager']}>
                    <ManageInventory />
                  </ProtectedRoute>
                }
              />
              <Route
                path="customers"
                element={
                  <ProtectedRoute allowedRoles={['admin', 'manager']}>
                    <CustomerManagement />
                  </ProtectedRoute>
                }
              />
              <Route
                path="reports"
                element={
                  <ProtectedRoute allowedRoles={['admin', 'manager']}>
                    <Reports />
                  </ProtectedRoute>
                }
              />
              <Route path="settings" element={<Settings />} />
            </Route>

            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  )
}

export default App
