import React, { Suspense, lazy } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import { CssBaseline } from '@mui/material'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import theme from './theme'
import LoadingScreen from './components/common/LoadingScreen'
import ErrorBoundary from './components/common/ErrorBoundary'
import AuthErrorBoundary from './components/common/AuthErrorBoundary'
import LoadingState from './components/common/LoadingState'
import TestComponent from './components/common/TestComponent'
import AuthMonitor from './components/AuthMonitor'
import { NetworkStatus } from './components/NetworkStatus'

// Lazy load components for better performance
const LoginPage = lazy(() => import('./pages/LoginPage'))
const DashboardLayout = lazy(() => import('./components/layout/DashboardLayout'))
const Dashboard = lazy(() => import('./pages/Dashboard'))
const MenuScreen = lazy(() => import('./pages/MenuScreen'))
const OngoingOrders = lazy(() => import('./pages/OngoingOrders'))
const Bills = lazy(() => import('./pages/Bills'))
const ManageInventory = lazy(() => import('./pages/ManageInventory'))
const CustomerManagement = lazy(() => import('./pages/CustomerManagement'))
const Reports = lazy(() => import('./pages/Reports'))
const Settings = lazy(() => import('./pages/Settings'))

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth()

  if (loading) {
    return <LoadingScreen />
  }

  if (!user) {
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}

// Public Route Component (redirects to dashboard if already logged in)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth()

  if (loading) {
    return <LoadingScreen />
  }

  if (user) {
    return <Navigate to="/dashboard" replace />
  }

  return <>{children}</>
}

function AppContent() {
  return (
    <Router>
      <AuthMonitor showDebugInfo={import.meta.env.DEV} />
      <NetworkStatus showPersistentIndicator={import.meta.env.DEV} />
      <Routes>
        {/* Public Routes */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <Suspense fallback={<LoadingState variant="spinner" size="large" fullScreen />}>
                <LoginPage />
              </Suspense>
            </PublicRoute>
          }
        />

        {/* Protected Routes */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Suspense fallback={<LoadingState variant="spinner" size="large" fullScreen />}>
                <DashboardLayout />
              </Suspense>
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={
            <Suspense fallback={<LoadingState variant="dashboard" />}>
              <Dashboard />
            </Suspense>
          } />
          <Route path="menu" element={
            <Suspense fallback={<LoadingState variant="card" />}>
              <MenuScreen />
            </Suspense>
          } />
          <Route path="orders" element={
            <Suspense fallback={<LoadingState variant="card" />}>
              <OngoingOrders />
            </Suspense>
          } />
          <Route path="bills" element={
            <Suspense fallback={<LoadingState variant="table" />}>
              <Bills />
            </Suspense>
          } />
          <Route path="inventory" element={
            <Suspense fallback={<LoadingState variant="table" />}>
              <ManageInventory />
            </Suspense>
          } />
          <Route path="customers" element={
            <Suspense fallback={<LoadingState variant="table" />}>
              <CustomerManagement />
            </Suspense>
          } />
          <Route path="reports" element={
            <Suspense fallback={<LoadingState variant="dashboard" />}>
              <Reports />
            </Suspense>
          } />
          <Route path="settings" element={
            <Suspense fallback={<LoadingState variant="skeleton" />}>
              <Settings />
            </Suspense>
          } />
        </Route>

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </Router>
  )
}

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AuthErrorBoundary>
          <AuthProvider>
            <AppContent />
          </AuthProvider>
        </AuthErrorBoundary>
      </ThemeProvider>
    </ErrorBoundary>
  )
}

export default App
