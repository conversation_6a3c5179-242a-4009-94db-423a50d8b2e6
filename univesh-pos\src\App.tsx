import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import { CssBaseline, Box, Typography } from '@mui/material'
import theme from './theme'

// Simple pages for the POS system based on blueprint
const LoginPage: React.FC = () => (
  <Box sx={{
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100vh',
    backgroundColor: '#f5f5f5'
  }}>
    <Box sx={{
      backgroundColor: 'white',
      p: 4,
      borderRadius: 2,
      boxShadow: 2,
      maxWidth: 400,
      width: '100%'
    }}>
      <Typography variant="h4" align="center" gutterBottom sx={{ color: '#1049B8', fontWeight: 600 }}>
        Univesh POS
      </Typography>
      <Typography variant="body1" align="center" color="text.secondary">
        Restaurant Point of Sale System
      </Typography>
      <Typography variant="body2" align="center" sx={{ mt: 2 }}>
        Please sign in with your Sales ID Number
      </Typography>
    </Box>
  </Box>
)

const Dashboard: React.FC = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom sx={{ color: '#1049B8', fontWeight: 600 }}>
      Dashboard
    </Typography>
    <Typography variant="body1" color="text.secondary">
      Daily Sales Overview, Total Income, Total Orders, New Customers, Trending Dishes, Best Employees
    </Typography>
  </Box>
)

const MenuScreen: React.FC = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom sx={{ color: '#1049B8', fontWeight: 600 }}>
      Menu & Orders
    </Typography>
    <Typography variant="body1" color="text.secondary">
      Menu items, cart, order customization, and table selection
    </Typography>
  </Box>
)

const OngoingOrders: React.FC = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom sx={{ color: '#1049B8', fontWeight: 600 }}>
      Ongoing Orders
    </Typography>
    <Typography variant="body1" color="text.secondary">
      Real-time tracking of active and pending orders
    </Typography>
  </Box>
)

const Bills: React.FC = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom sx={{ color: '#1049B8', fontWeight: 600 }}>
      Bills & Payment
    </Typography>
    <Typography variant="body1" color="text.secondary">
      Checkout, payment processing (Cash/Card/Credit), and bill management
    </Typography>
  </Box>
)

const ManageInventory: React.FC = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom sx={{ color: '#1049B8', fontWeight: 600 }}>
      Manage Inventory
    </Typography>
    <Typography variant="body1" color="text.secondary">
      Product management, categories, variations, and stock tracking
    </Typography>
  </Box>
)

const CustomerManagement: React.FC = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom sx={{ color: '#1049B8', fontWeight: 600 }}>
      Customer Management
    </Typography>
    <Typography variant="body1" color="text.secondary">
      Customer database, order history, and contact information
    </Typography>
  </Box>
)

const Reports: React.FC = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom sx={{ color: '#1049B8', fontWeight: 600 }}>
      Reports
    </Typography>
    <Typography variant="body1" color="text.secondary">
      Sales reports, order reports, and payment method analysis
    </Typography>
  </Box>
)

const Settings: React.FC = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom sx={{ color: '#1049B8', fontWeight: 600 }}>
      Settings
    </Typography>
    <Typography variant="body1" color="text.secondary">
      User profile, checkout settings, and system configuration
    </Typography>
  </Box>
)

// Simple layout component
const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Box sx={{ display: 'flex', minHeight: '100vh' }}>
    {/* Sidebar */}
    <Box sx={{
      width: 240,
      backgroundColor: '#1049B8',
      color: 'white',
      p: 2
    }}>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
        Univesh POS
      </Typography>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Typography variant="body2" sx={{ p: 1, cursor: 'pointer', '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' } }}>
          Dashboard
        </Typography>
        <Typography variant="body2" sx={{ p: 1, cursor: 'pointer', '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' } }}>
          Menu & Orders
        </Typography>
        <Typography variant="body2" sx={{ p: 1, cursor: 'pointer', '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' } }}>
          Ongoing Orders
        </Typography>
        <Typography variant="body2" sx={{ p: 1, cursor: 'pointer', '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' } }}>
          Bills
        </Typography>
        <Typography variant="body2" sx={{ p: 1, cursor: 'pointer', '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' } }}>
          Manage Inventory
        </Typography>
        <Typography variant="body2" sx={{ p: 1, cursor: 'pointer', '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' } }}>
          Customers
        </Typography>
        <Typography variant="body2" sx={{ p: 1, cursor: 'pointer', '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' } }}>
          Reports
        </Typography>
        <Typography variant="body2" sx={{ p: 1, cursor: 'pointer', '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' } }}>
          Settings
        </Typography>
      </Box>
    </Box>

    {/* Main content */}
    <Box sx={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      {children}
    </Box>
  </Box>
)

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Routes>
          {/* Login Route */}
          <Route path="/login" element={<LoginPage />} />

          {/* Main App Routes */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Layout><Dashboard /></Layout>} />
          <Route path="/menu" element={<Layout><MenuScreen /></Layout>} />
          <Route path="/orders" element={<Layout><OngoingOrders /></Layout>} />
          <Route path="/bills" element={<Layout><Bills /></Layout>} />
          <Route path="/inventory" element={<Layout><ManageInventory /></Layout>} />
          <Route path="/customers" element={<Layout><CustomerManagement /></Layout>} />
          <Route path="/reports" element={<Layout><Reports /></Layout>} />
          <Route path="/settings" element={<Layout><Settings /></Layout>} />

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Router>
    </ThemeProvider>
  )
}

export default App
