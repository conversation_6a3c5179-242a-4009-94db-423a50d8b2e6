/**
 * Dashboard Loading Debug Test
 * 
 * This script debugs the dashboard loading issue by:
 * 1. Checking console errors
 * 2. Monitoring network requests
 * 3. Testing authentication state
 * 4. Verifying data fetching
 */

import puppeteer from 'puppeteer';

async function debugDashboardLoading() {
  console.log('🔍 Debugging Dashboard Loading Issue...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Listen for console messages
    const consoleMessages = [];
    const errors = [];
    const networkRequests = [];
    
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      console.log(`📝 Console: ${text}`);
      
      if (msg.type() === 'error') {
        errors.push(text);
        console.log(`❌ Error: ${text}`);
      }
    });
    
    // Monitor network requests
    page.on('request', request => {
      networkRequests.push({
        url: request.url(),
        method: request.method(),
        timestamp: Date.now()
      });
      console.log(`🌐 Request: ${request.method()} ${request.url()}`);
    });
    
    page.on('response', response => {
      console.log(`📡 Response: ${response.status()} ${response.url()}`);
    });
    
    console.log('🌐 Step 1: Navigate to login page...');
    await page.goto('http://localhost:5174/', { waitUntil: 'networkidle0' });
    
    // Wait for login form
    await page.waitForSelector('input[type="text"]', { timeout: 10000 });
    console.log('✅ Login page loaded');
    
    console.log('🔐 Step 2: Perform login...');
    await page.type('input[type="text"]', 'ADMIN001');
    await page.type('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    console.log('⏳ Step 3: Wait for navigation to dashboard...');
    try {
      await page.waitForNavigation({ timeout: 15000 });
      console.log('✅ Navigation completed');
      
      const currentUrl = page.url();
      console.log(`📍 Current URL: ${currentUrl}`);
      
      if (!currentUrl.includes('/dashboard')) {
        console.log('❌ Not on dashboard page');
        return false;
      }
      
    } catch (error) {
      console.log('❌ Navigation timeout:', error.message);
      return false;
    }
    
    console.log('🔍 Step 4: Check dashboard loading state...');
    
    // Wait a bit to see if loading completes
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Check if still loading
    const loadingElement = await page.$('text=Loading Univesh POS');
    if (loadingElement) {
      console.log('❌ Dashboard still showing loading screen');
      
      // Check for specific loading indicators
      const loadingStates = await page.evaluate(() => {
        const elements = document.querySelectorAll('*');
        const loadingTexts = [];
        elements.forEach(el => {
          if (el.textContent && el.textContent.includes('Loading')) {
            loadingTexts.push(el.textContent.trim());
          }
        });
        return loadingTexts;
      });
      
      console.log('🔍 Loading states found:', loadingStates);
    } else {
      console.log('✅ Dashboard loaded successfully');
    }
    
    console.log('🔍 Step 5: Check authentication state...');
    
    // Check if auth context has user data
    const authState = await page.evaluate(() => {
      // Try to access React DevTools or check for auth indicators
      const authIndicators = document.querySelectorAll('[data-testid*="auth"], [class*="auth"]');
      return {
        hasAuthIndicators: authIndicators.length > 0,
        bodyClasses: document.body.className,
        hasUserMenu: !!document.querySelector('[data-testid="user-menu"]'),
        hasLogoutButton: !!document.querySelector('button:contains("Logout")')
      };
    });
    
    console.log('🔍 Auth state:', authState);
    
    console.log('🔍 Step 6: Check for specific errors...');
    
    // Look for specific error patterns
    const authErrors = consoleMessages.filter(msg => 
      msg.includes('auth') && (msg.includes('error') || msg.includes('failed'))
    );
    
    const networkErrors = consoleMessages.filter(msg => 
      msg.includes('network') || msg.includes('fetch') || msg.includes('supabase')
    );
    
    const loadingErrors = consoleMessages.filter(msg => 
      msg.includes('loading') && msg.includes('false')
    );
    
    console.log('\n📊 Error Analysis:');
    console.log(`   Auth errors: ${authErrors.length}`);
    console.log(`   Network errors: ${networkErrors.length}`);
    console.log(`   Loading errors: ${loadingErrors.length}`);
    console.log(`   Total console messages: ${consoleMessages.length}`);
    
    if (authErrors.length > 0) {
      console.log('\n❌ Auth Errors:');
      authErrors.forEach(error => console.log(`   - ${error}`));
    }
    
    if (networkErrors.length > 0) {
      console.log('\n❌ Network Errors:');
      networkErrors.forEach(error => console.log(`   - ${error}`));
    }
    
    console.log('🔍 Step 7: Check network requests...');
    
    const supabaseRequests = networkRequests.filter(req => 
      req.url.includes('supabase.co')
    );
    
    console.log(`📡 Supabase requests: ${supabaseRequests.length}`);
    supabaseRequests.forEach(req => {
      console.log(`   ${req.method} ${req.url}`);
    });
    
    // Wait longer to see if anything changes
    console.log('⏳ Step 8: Wait longer to see if loading completes...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    const finalLoadingCheck = await page.$('text=Loading Univesh POS');
    if (finalLoadingCheck) {
      console.log('❌ Dashboard still loading after 15 seconds total');
      return false;
    } else {
      console.log('✅ Dashboard finally loaded');
      return true;
    }
    
  } catch (error) {
    console.error('💥 Debug test failed:', error);
    return false;
  } finally {
    // Keep browser open for manual inspection
    console.log('\n🔍 Browser left open for manual inspection...');
    console.log('Press Ctrl+C to close when done debugging');
    
    // Wait indefinitely
    await new Promise(() => {});
  }
}

// Run the debug test
debugDashboardLoading().catch(error => {
  console.error('\n💥 Debug execution failed:', error);
  process.exit(1);
});
