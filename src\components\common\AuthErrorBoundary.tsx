import React, { Component, ErrorInfo, ReactNode } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Alert,
  CircularProgress
} from '@mui/material'
import {
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  Home as HomeIcon
} from '@mui/icons-material'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  isRetrying: boolean
  retryCount: number
}

class AuthErrorBoundary extends Component<Props, State> {
  private maxRetries = 3
  private retryTimeout: NodeJS.Timeout | null = null

  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
    isRetrying: false,
    retryCount: 0
  }

  public static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('🚨 [AuthErrorBoundary] Authentication error caught:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Auto-retry for certain types of errors
    if (this.shouldAutoRetry(error) && this.state.retryCount < this.maxRetries) {
      this.autoRetry()
    }
  }

  private shouldAutoRetry = (error: Error): boolean => {
    const retryableErrors = [
      'Network Error',
      'Failed to fetch',
      'Connection timeout',
      'NETWORK_ERROR',
      'AUTH_NETWORK_ERROR'
    ]
    
    return retryableErrors.some(errorType => 
      error.message.includes(errorType) || error.name.includes(errorType)
    )
  }

  private autoRetry = () => {
    if (this.state.retryCount >= this.maxRetries) {
      return
    }

    console.log(`🔄 [AuthErrorBoundary] Auto-retrying (${this.state.retryCount + 1}/${this.maxRetries})`)
    
    this.setState({ isRetrying: true })
    
    this.retryTimeout = setTimeout(() => {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        isRetrying: false,
        retryCount: prevState.retryCount + 1
      }))
    }, 2000 * (this.state.retryCount + 1)) // Exponential backoff
  }

  private handleManualRetry = () => {
    console.log('🔄 [AuthErrorBoundary] Manual retry triggered')
    
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      isRetrying: false,
      retryCount: 0
    })
  }

  private handleGoHome = () => {
    // Clear any stored auth state and redirect to home
    localStorage.clear()
    sessionStorage.clear()
    window.location.href = '/'
  }

  private handleReload = () => {
    window.location.reload()
  }

  public componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout)
    }
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      if (this.state.isRetrying) {
        return (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: '100vh',
              backgroundColor: '#f5f5f5',
              gap: 3,
              p: 3
            }}
          >
            <CircularProgress size={60} sx={{ color: '#1976d2' }} />
            <Typography variant="h6" color="text.secondary">
              Retrying authentication...
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Attempt {this.state.retryCount + 1} of {this.maxRetries}
            </Typography>
          </Box>
        )
      }

      const isNetworkError = this.state.error?.message.includes('Network') || 
                            this.state.error?.message.includes('fetch') ||
                            this.state.error?.message.includes('timeout')

      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '100vh',
            backgroundColor: '#f5f5f5',
            p: 3
          }}
        >
          <Card sx={{ maxWidth: 600, width: '100%', textAlign: 'center' }}>
            <CardContent sx={{ p: 4 }}>
              <WarningIcon 
                sx={{ 
                  fontSize: 80, 
                  color: isNetworkError ? '#ed6c02' : '#d32f2f',
                  mb: 3 
                }} 
              />
              
              <Typography variant="h4" sx={{ fontWeight: 600, mb: 2 }}>
                {isNetworkError ? 'Connection Problem' : 'Authentication Error'}
              </Typography>
              
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                {isNetworkError 
                  ? 'Unable to connect to the server. Please check your internet connection and try again.'
                  : 'An error occurred during authentication. This might be a temporary issue.'
                }
              </Typography>

              {this.state.error && (
                <Alert severity={isNetworkError ? 'warning' : 'error'} sx={{ mb: 3, textAlign: 'left' }}>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {this.state.error.message}
                  </Typography>
                </Alert>
              )}

              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  startIcon={<RefreshIcon />}
                  onClick={this.handleManualRetry}
                  size="large"
                >
                  Try Again
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={this.handleReload}
                  size="large"
                >
                  Reload Page
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<HomeIcon />}
                  onClick={this.handleGoHome}
                  size="large"
                >
                  Go Home
                </Button>
              </Box>

              {this.state.retryCount > 0 && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
                  Retry attempts: {this.state.retryCount}/{this.maxRetries}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Box>
      )
    }

    return this.props.children
  }
}

export default AuthErrorBoundary
