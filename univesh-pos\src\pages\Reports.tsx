import React, { useState, useEffect, useCallback } from 'react'
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Tabs,
  Tab
} from '@mui/material'
import {
  Assessment,
  TrendingUp,
  AttachMoney,
  ShoppingCart,
  People,
  Payment,
  DateRange,
  Download
} from '@mui/icons-material'
import { colors } from '../theme'
import { supabase } from '../lib/supabase'

interface SalesReport {
  date: string
  total_sales: number
  order_count: number
  avg_order_value: number
}

interface PaymentMethodReport {
  payment_method: string
  total_amount: number
  transaction_count: number
  percentage: number
}

interface ProductReport {
  product_name: string
  category_name: string
  quantity_sold: number
  total_revenue: number
}

interface EmployeeReport {
  employee_name: string
  role_name: string
  orders_processed: number
  total_sales: number
}

const Reports: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Date filters
  const [dateRange, setDateRange] = useState('7') // days
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  // Report data
  const [salesReport, setSalesReport] = useState<SalesReport[]>([])
  const [paymentMethodReport, setPaymentMethodReport] = useState<PaymentMethodReport[]>([])
  const [productReport, setProductReport] = useState<ProductReport[]>([])
  const [employeeReport, setEmployeeReport] = useState<EmployeeReport[]>([])

  // Summary metrics
  const [summaryMetrics, setSummaryMetrics] = useState({
    totalSales: 0,
    totalOrders: 0,
    avgOrderValue: 0,
    totalCustomers: 0
  })

  useEffect(() => {
    // Set default date range
    const today = new Date()
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

    setEndDate(today.toISOString().split('T')[0])
    setStartDate(weekAgo.toISOString().split('T')[0])
  }, [])

  useEffect(() => {
    if (startDate && endDate) {
      fetchReports()
    }
  }, [startDate, endDate, fetchReports])

  const fetchReports = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const startDateTime = new Date(startDate + 'T00:00:00').toISOString()
      const endDateTime = new Date(endDate + 'T23:59:59').toISOString()

      // Fetch sales summary
      await fetchSalesSummary(startDateTime, endDateTime)

      // Fetch payment method report
      await fetchPaymentMethodReport(startDateTime, endDateTime)

      // Fetch product report
      await fetchProductReport(startDateTime, endDateTime)

      // Fetch employee report
      await fetchEmployeeReport(startDateTime, endDateTime)

    } catch (error) {
      console.error('Error fetching reports:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch reports')
    } finally {
      setLoading(false)
    }
  }, [startDate, endDate])

  const fetchSalesSummary = async (startDateTime: string, endDateTime: string) => {
    // Get total sales and orders
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('total_amount, created_at')
      .eq('status', 'completed')
      .gte('created_at', startDateTime)
      .lte('created_at', endDateTime)

    if (ordersError) throw ordersError

    const totalSales = orders?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0
    const totalOrders = orders?.length || 0
    const avgOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0

    // Get unique customers count
    const { data: customers, error: customersError } = await supabase
      .from('orders')
      .select('customer_id')
      .eq('status', 'completed')
      .gte('created_at', startDateTime)
      .lte('created_at', endDateTime)
      .not('customer_id', 'is', null)

    if (customersError) throw customersError

    const uniqueCustomers = new Set(customers?.map(c => c.customer_id)).size

    setSummaryMetrics({
      totalSales,
      totalOrders,
      avgOrderValue,
      totalCustomers: uniqueCustomers
    })

    // Generate daily sales report
    const dailySales: { [key: string]: { sales: number, orders: number } } = {}

    orders?.forEach(order => {
      const date = new Date(order.created_at).toISOString().split('T')[0]
      if (!dailySales[date]) {
        dailySales[date] = { sales: 0, orders: 0 }
      }
      dailySales[date].sales += Number(order.total_amount)
      dailySales[date].orders += 1
    })

    const salesReportData = Object.entries(dailySales).map(([date, data]) => ({
      date,
      total_sales: data.sales,
      order_count: data.orders,
      avg_order_value: data.orders > 0 ? data.sales / data.orders : 0
    })).sort((a, b) => a.date.localeCompare(b.date))

    setSalesReport(salesReportData)
  }

  const fetchPaymentMethodReport = async (startDateTime: string, endDateTime: string) => {
    const { data: payments, error } = await supabase
      .from('payments')
      .select('payment_method, amount')
      .eq('status', 'completed')
      .gte('created_at', startDateTime)
      .lte('created_at', endDateTime)

    if (error) throw error

    const paymentSummary: { [key: string]: { amount: number, count: number } } = {}
    let totalAmount = 0

    payments?.forEach(payment => {
      const method = payment.payment_method
      const amount = Number(payment.amount)

      if (!paymentSummary[method]) {
        paymentSummary[method] = { amount: 0, count: 0 }
      }

      paymentSummary[method].amount += amount
      paymentSummary[method].count += 1
      totalAmount += amount
    })

    const paymentReportData = Object.entries(paymentSummary).map(([method, data]) => ({
      payment_method: method,
      total_amount: data.amount,
      transaction_count: data.count,
      percentage: totalAmount > 0 ? (data.amount / totalAmount) * 100 : 0
    })).sort((a, b) => b.total_amount - a.total_amount)

    setPaymentMethodReport(paymentReportData)
  }

  const fetchProductReport = async (startDateTime: string, endDateTime: string) => {
    const { data: orderItems, error } = await supabase
      .from('order_items')
      .select(`
        quantity,
        unit_price_at_order,
        products!inner(name, categories(name)),
        orders!inner(status, created_at)
      `)
      .eq('orders.status', 'completed')
      .gte('orders.created_at', startDateTime)
      .lte('orders.created_at', endDateTime)

    if (error) throw error

    const productSummary: { [key: string]: { quantity: number, revenue: number, category: string } } = {}

    orderItems?.forEach(item => {
      const productName = item.products.name
      const categoryName = item.products.categories?.name || 'No Category'
      const quantity = item.quantity
      const revenue = quantity * Number(item.unit_price_at_order)

      if (!productSummary[productName]) {
        productSummary[productName] = { quantity: 0, revenue: 0, category: categoryName }
      }

      productSummary[productName].quantity += quantity
      productSummary[productName].revenue += revenue
    })

    const productReportData = Object.entries(productSummary).map(([name, data]) => ({
      product_name: name,
      category_name: data.category,
      quantity_sold: data.quantity,
      total_revenue: data.revenue
    })).sort((a, b) => b.total_revenue - a.total_revenue)

    setProductReport(productReportData)
  }

  const fetchEmployeeReport = async (startDateTime: string, endDateTime: string) => {
    const { data: orders, error } = await supabase
      .from('orders')
      .select(`
        total_amount,
        employee_id,
        profiles!inner(full_name, roles(role_name))
      `)
      .eq('status', 'completed')
      .gte('created_at', startDateTime)
      .lte('created_at', endDateTime)

    if (error) throw error

    const employeeSummary: { [key: string]: { orders: number, sales: number, role: string } } = {}

    orders?.forEach(order => {
      const employeeName = order.profiles.full_name
      const roleName = order.profiles.roles?.role_name || 'Unknown'
      const amount = Number(order.total_amount)

      if (!employeeSummary[employeeName]) {
        employeeSummary[employeeName] = { orders: 0, sales: 0, role: roleName }
      }

      employeeSummary[employeeName].orders += 1
      employeeSummary[employeeName].sales += amount
    })

    const employeeReportData = Object.entries(employeeSummary).map(([name, data]) => ({
      employee_name: name,
      role_name: data.role,
      orders_processed: data.orders,
      total_sales: data.sales
    })).sort((a, b) => b.total_sales - a.total_sales)

    setEmployeeReport(employeeReportData)
  }

  const handleDateRangeChange = (range: string) => {
    setDateRange(range)
    const today = new Date()
    const daysAgo = new Date(today.getTime() - parseInt(range) * 24 * 60 * 60 * 1000)

    setEndDate(today.toISOString().split('T')[0])
    setStartDate(daysAgo.toISOString().split('T')[0])
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress size={60} />
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Reports & Analytics
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Download />}
          onClick={() => {/* TODO: Implement export functionality */}}
        >
          Export Reports
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Date Range Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Quick Range</InputLabel>
                <Select
                  value={dateRange}
                  label="Quick Range"
                  onChange={(e) => handleDateRangeChange(e.target.value)}
                >
                  <MenuItem value="7">Last 7 days</MenuItem>
                  <MenuItem value="30">Last 30 days</MenuItem>
                  <MenuItem value="90">Last 3 months</MenuItem>
                  <MenuItem value="365">Last year</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Start Date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="End Date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="contained"
                onClick={fetchReports}
                startIcon={<Assessment />}
              >
                Generate Reports
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Summary Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: colors.primary.main }}>
                    ${summaryMetrics.totalSales.toFixed(2)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Sales
                  </Typography>
                </Box>
                <AttachMoney sx={{ fontSize: 40, color: colors.primary.main }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: colors.success.main }}>
                    {summaryMetrics.totalOrders}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Orders
                  </Typography>
                </Box>
                <ShoppingCart sx={{ fontSize: 40, color: colors.success.main }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: colors.warning.main }}>
                    ${summaryMetrics.avgOrderValue.toFixed(2)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg Order Value
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 40, color: colors.warning.main }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: colors.info.main }}>
                    {summaryMetrics.totalCustomers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Unique Customers
                  </Typography>
                </Box>
                <People sx={{ fontSize: 40, color: colors.info.main }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detailed Reports */}
      <Card>
        <CardContent>
          <Tabs value={activeTab} onChange={(_, value) => setActiveTab(value)} sx={{ mb: 3 }}>
            <Tab label="Daily Sales" icon={<DateRange />} iconPosition="start" />
            <Tab label="Payment Methods" icon={<Payment />} iconPosition="start" />
            <Tab label="Product Performance" icon={<ShoppingCart />} iconPosition="start" />
            <Tab label="Employee Performance" icon={<People />} iconPosition="start" />
          </Tabs>

          {/* Daily Sales Report */}
          {activeTab === 0 && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell align="right">Total Sales</TableCell>
                    <TableCell align="right">Orders</TableCell>
                    <TableCell align="right">Avg Order Value</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {salesReport.map((row) => (
                    <TableRow key={row.date}>
                      <TableCell>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {new Date(row.date).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1" sx={{ fontWeight: 600, color: colors.success.main }}>
                          ${row.total_sales.toFixed(2)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Chip label={row.order_count} size="small" color="primary" />
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1">
                          ${row.avg_order_value.toFixed(2)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                  {salesReport.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={4} align="center">
                        <Typography variant="body2" color="text.secondary" sx={{ py: 4 }}>
                          No sales data available for the selected period
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Payment Methods Report */}
          {activeTab === 1 && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Payment Method</TableCell>
                    <TableCell align="right">Total Amount</TableCell>
                    <TableCell align="right">Transactions</TableCell>
                    <TableCell align="right">Percentage</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paymentMethodReport.map((row) => (
                    <TableRow key={row.payment_method}>
                      <TableCell>
                        <Chip
                          label={row.payment_method}
                          color={
                            row.payment_method === 'Cash' ? 'success' :
                            row.payment_method === 'Card' ? 'primary' : 'secondary'
                          }
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1" sx={{ fontWeight: 600, color: colors.success.main }}>
                          ${row.total_amount.toFixed(2)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1">
                          {row.transaction_count}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {row.percentage.toFixed(1)}%
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                  {paymentMethodReport.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={4} align="center">
                        <Typography variant="body2" color="text.secondary" sx={{ py: 4 }}>
                          No payment data available for the selected period
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Product Performance Report */}
          {activeTab === 2 && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Product</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell align="right">Quantity Sold</TableCell>
                    <TableCell align="right">Total Revenue</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {productReport.slice(0, 20).map((row) => (
                    <TableRow key={row.product_name}>
                      <TableCell>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {row.product_name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip label={row.category_name} size="small" variant="outlined" />
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1">
                          {row.quantity_sold}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1" sx={{ fontWeight: 600, color: colors.success.main }}>
                          ${row.total_revenue.toFixed(2)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                  {productReport.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={4} align="center">
                        <Typography variant="body2" color="text.secondary" sx={{ py: 4 }}>
                          No product sales data available for the selected period
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Employee Performance Report */}
          {activeTab === 3 && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Employee</TableCell>
                    <TableCell>Role</TableCell>
                    <TableCell align="right">Orders Processed</TableCell>
                    <TableCell align="right">Total Sales</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {employeeReport.map((row) => (
                    <TableRow key={row.employee_name}>
                      <TableCell>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {row.employee_name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={row.role_name.toUpperCase()}
                          size="small"
                          color={
                            row.role_name === 'admin' ? 'error' :
                            row.role_name === 'manager' ? 'warning' : 'primary'
                          }
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1">
                          {row.orders_processed}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1" sx={{ fontWeight: 600, color: colors.success.main }}>
                          ${row.total_sales.toFixed(2)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                  {employeeReport.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={4} align="center">
                        <Typography variant="body2" color="text.secondary" sx={{ py: 4 }}>
                          No employee performance data available for the selected period
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    </Box>
  )
}

export default Reports
