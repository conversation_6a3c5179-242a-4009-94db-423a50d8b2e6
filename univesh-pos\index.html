<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Univesh Restaurant POS</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Streamline your restaurant operations with Univesh POS - comprehensive point-of-sale solution for order management, inventory, customers, and analytics.">
    <meta name="keywords" content="restaurant POS, point of sale, order management, inventory, restaurant software">
    <meta name="author" content="Univesh">
    <meta name="robots" content="noindex, nofollow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Univesh Restaurant POS">
    <meta property="og:description" content="Comprehensive restaurant point-of-sale solution">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://univesh-pos.com">

    <!-- Performance Optimization -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://fonts.gstatic.com">
    <link rel="dns-prefetch" href="https://kufsqfbiilphymiehwet.supabase.co">

    <!-- Security headers are now set via HTTP response headers in vite.config.ts -->

    <!-- Fonts and Resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- Gilroy Font (Primary) -->
    <link href="https://fonts.googleapis.com/css2?family=Gilroy:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Inter Font (Fallback) -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
