import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Divider,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText
} from '@mui/material'
import {
  Receipt,
  Payment,
  CheckCircle,
  CreditCard,
  AccountBalanceWallet,
  MonetizationOn
} from '@mui/icons-material'
import { colors } from '../theme'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'

interface Order {
  id: string
  table_number: string | null
  order_type: 'dine_in' | 'delivery' | 'take_away'
  status: 'draft' | 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled'
  subtotal_amount: number
  tax_amount: number
  discount_amount: number
  complementary_amount: number
  total_amount: number
  employee_id: string
  customer_id: string | null
  notes: string | null
  created_at: string
  updated_at: string
  profiles?: {
    full_name: string
  }
  customers?: {
    full_name: string
  }
  order_items?: OrderItem[]
}

interface OrderItem {
  id: string
  product_id: string
  quantity: number
  unit_price_at_order: number
  item_subtotal: number
  products: {
    name: string
    description: string | null
  }
}

interface Payment {
  id: string
  order_id: string
  amount: number
  payment_method: 'Cash' | 'Card' | 'Credit'
  status: 'completed' | 'pending' | 'refunded' | 'voided'
  transaction_reference: string | null
  processed_by_employee_id: string
  created_at: string
}

const Bills: React.FC = () => {
  const { user } = useAuth()
  const [orders, setOrders] = useState<Order[]>([])
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<'Cash' | 'Card' | 'Credit'>('Cash')
  const [transactionReference, setTransactionReference] = useState('')
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  useEffect(() => {
    fetchOrders()
  }, [])

  const fetchOrders = async () => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          profiles!orders_employee_id_fkey(full_name),
          customers(full_name),
          order_items(
            id,
            product_id,
            quantity,
            unit_price_at_order,
            item_subtotal,
            products(name, description)
          )
        `)
        .in('status', ['pending', 'preparing', 'ready'])
        .order('created_at', { ascending: false })

      if (error) throw error

      setOrders(data || [])
    } catch (error) {
      console.error('Error fetching orders:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch orders')
    } finally {
      setLoading(false)
    }
  }

  const openPaymentDialog = (order: Order) => {
    setSelectedOrder(order)
    setPaymentDialogOpen(true)
    setPaymentMethod('Cash')
    setTransactionReference('')
  }

  const closePaymentDialog = () => {
    setPaymentDialogOpen(false)
    setSelectedOrder(null)
    setPaymentMethod('Cash')
    setTransactionReference('')
  }

  const processPayment = async () => {
    if (!selectedOrder || !user) return

    try {
      setProcessing(true)
      setError(null)

      // Create payment record and update order status in a transaction
      const { error: paymentError } = await supabase
        .from('payments')
        .insert({
          order_id: selectedOrder.id,
          amount: selectedOrder.total_amount,
          payment_method: paymentMethod,
          status: 'completed' as const,
          transaction_reference: transactionReference || null,
          processed_by_employee_id: user.id
        })
        .select()
        .single()

      if (paymentError) throw paymentError

      // Update order status to completed
      const { error: orderError } = await supabase
        .from('orders')
        .update({
          status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedOrder.id)

      if (orderError) throw orderError

      // Update inventory for each order item
      for (const item of selectedOrder.order_items || []) {
        const { error: inventoryError } = await supabase.rpc('decrement_product_stock', {
          product_id: item.product_id,
          quantity: item.quantity
        })

        if (inventoryError) {
          console.warn('Failed to update inventory for product:', item.product_id, inventoryError)
        }
      }

      setSuccess(`Payment processed successfully! Order #${selectedOrder.id.slice(0, 8)} completed.`)
      setTimeout(() => setSuccess(null), 5000)

      closePaymentDialog()
      fetchOrders() // Refresh the orders list

    } catch (error) {
      console.error('Error processing payment:', error)
      setError(error instanceof Error ? error.message : 'Failed to process payment')
    } finally {
      setProcessing(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning'
      case 'preparing': return 'info'
      case 'ready': return 'success'
      case 'completed': return 'success'
      case 'cancelled': return 'error'
      default: return 'default'
    }
  }



  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress size={60} />
      </Box>
    )
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
        Bills & Payments
      </Typography>

      {/* Success/Error Messages */}
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Orders Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Pending Orders
          </Typography>

          {orders.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Receipt sx={{ fontSize: 64, color: colors.grey[400], mb: 2 }} />
              <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                No pending orders
              </Typography>
              <Typography variant="body2" color="text.secondary">
                All orders have been processed or no orders are ready for payment.
              </Typography>
            </Box>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Order ID</TableCell>
                    <TableCell>Table/Type</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Employee</TableCell>
                    <TableCell align="right">Total</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {orders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                          #{order.id.slice(0, 8)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            {order.table_number || 'N/A'}
                          </Typography>
                          <Chip
                            label={order.order_type.replace('_', ' ').toUpperCase()}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={order.status.toUpperCase()}
                          size="small"
                          color={getStatusColor(order.status) as 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {order.profiles?.full_name || 'Unknown'}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          ${order.total_amount.toFixed(2)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(order.created_at).toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Button
                          variant="contained"
                          size="small"
                          startIcon={<Payment />}
                          onClick={() => openPaymentDialog(order)}
                          disabled={order.status !== 'ready'}
                        >
                          Pay
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Payment Dialog */}
      <Dialog open={paymentDialogOpen} onClose={closePaymentDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          Process Payment - Order #{selectedOrder?.id.slice(0, 8)}
        </DialogTitle>
        <DialogContent>
          {selectedOrder && (
            <Grid container spacing={3} sx={{ mt: 1 }}>
              {/* Order Details */}
              <Grid item xs={12} md={6}>
                <Typography variant="h6" sx={{ mb: 2 }}>Order Details</Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">Table Number</Typography>
                  <Typography variant="body1">{selectedOrder.table_number || 'N/A'}</Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">Order Type</Typography>
                  <Typography variant="body1">{selectedOrder.order_type.replace('_', ' ').toUpperCase()}</Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">Status</Typography>
                  <Chip
                    label={selectedOrder.status.toUpperCase()}
                    size="small"
                    color={getStatusColor(selectedOrder.status) as 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'}
                  />
                </Box>
              </Grid>

              {/* Order Items */}
              <Grid item xs={12} md={6}>
                <Typography variant="h6" sx={{ mb: 2 }}>Order Items</Typography>
                <List dense>
                  {selectedOrder.order_items?.map((item) => (
                    <ListItem key={item.id} sx={{ px: 0 }}>
                      <ListItemText
                        primary={item.products.name}
                        secondary={`Qty: ${item.quantity} × $${item.unit_price_at_order.toFixed(2)}`}
                      />
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        ${item.item_subtotal.toFixed(2)}
                      </Typography>
                    </ListItem>
                  ))}
                </List>
              </Grid>

              {/* Payment Summary */}
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body1">Subtotal:</Typography>
                    <Typography variant="body1">${selectedOrder.subtotal_amount.toFixed(2)}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body1">Tax:</Typography>
                    <Typography variant="body1">${selectedOrder.tax_amount.toFixed(2)}</Typography>
                  </Box>
                  {selectedOrder.discount_amount > 0 && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1">Discount:</Typography>
                      <Typography variant="body1" color="success.main">
                        -${selectedOrder.discount_amount.toFixed(2)}
                      </Typography>
                    </Box>
                  )}
                  {selectedOrder.complementary_amount > 0 && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1">Complementary:</Typography>
                      <Typography variant="body1" color="success.main">
                        -${selectedOrder.complementary_amount.toFixed(2)}
                      </Typography>
                    </Box>
                  )}
                  <Divider sx={{ my: 1 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>Total Payment:</Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: colors.primary.main }}>
                      ${selectedOrder.total_amount.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              {/* Payment Method Selection */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Payment Method</InputLabel>
                  <Select
                    value={paymentMethod}
                    label="Payment Method"
                    onChange={(e) => setPaymentMethod(e.target.value as 'Cash' | 'Card' | 'Credit')}
                  >
                    <MenuItem value="Cash">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <MonetizationOn />
                        Cash
                      </Box>
                    </MenuItem>
                    <MenuItem value="Card">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CreditCard />
                        Card
                      </Box>
                    </MenuItem>
                    <MenuItem value="Credit">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <AccountBalanceWallet />
                        Credit
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {/* Transaction Reference */}
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Transaction Reference (Optional)"
                  value={transactionReference}
                  onChange={(e) => setTransactionReference(e.target.value)}
                  placeholder="Receipt number, card terminal ID, etc."
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={closePaymentDialog} disabled={processing}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={processPayment}
            disabled={processing || !selectedOrder}
            startIcon={processing ? <CircularProgress size={20} /> : <CheckCircle />}
          >
            {processing ? 'Processing...' : 'Complete Payment'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default Bills
