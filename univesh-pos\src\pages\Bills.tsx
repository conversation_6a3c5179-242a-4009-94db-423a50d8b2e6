import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material'
import {
  Receipt,
  Payment,
  CreditCard,
  AccountBalanceWallet,
  MonetizationOn,
  ExpandMore,
  ExpandLess,
  Search,
  FilterList,
  Download,
  Refresh
} from '@mui/icons-material'
import { colors } from '../theme'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'

interface CompletedOrder {
  id: string
  table_number: string | null
  order_type: 'dine_in' | 'delivery' | 'take_away'
  status: 'completed'
  subtotal_amount: number
  tax_amount: number
  discount_amount: number
  complementary_amount: number
  total_amount: number
  employee_id: string
  customer_id: string | null
  notes: string | null
  created_at: string
  updated_at: string
  profiles?: {
    full_name: string
  }
  customers?: {
    full_name: string
  }
  order_items?: OrderItem[]
  payments?: Payment[]
}

interface OrderItem {
  id: string
  product_id: string
  quantity: number
  unit_price_at_order: number
  item_subtotal: number
  products: {
    name: string
    description: string | null
  }
}

interface Payment {
  id: string
  order_id: string
  amount: number
  payment_method: 'Cash' | 'Card' | 'Credit'
  status: 'completed' | 'pending' | 'refunded' | 'voided'
  transaction_reference: string | null
  processed_by_employee_id: string
  created_at: string
  updated_at: string
}

const Bills: React.FC = () => {
  const { user } = useAuth()
  const [completedOrders, setCompletedOrders] = useState<CompletedOrder[]>([])
  const [filteredOrders, setFilteredOrders] = useState<CompletedOrder[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterPaymentMethod, setFilterPaymentMethod] = useState<string>('all')
  const [filterDateRange, setFilterDateRange] = useState<string>('all')
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [ordersPerPage] = useState(20)

  useEffect(() => {
    fetchCompletedOrders()
  }, [])

  useEffect(() => {
    filterOrders()
  }, [completedOrders, searchTerm, filterPaymentMethod, filterDateRange])

  const fetchCompletedOrders = async () => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          customers(full_name),
          order_items(
            id,
            product_id,
            quantity,
            unit_price_at_order,
            item_subtotal,
            products(name, description)
          ),
          payments(
            id,
            amount,
            payment_method,
            status,
            transaction_reference,
            processed_by_employee_id,
            created_at,
            updated_at
          )
        `)
        .eq('status', 'completed')
        .order('created_at', { ascending: false })
        .limit(100) // Limit to last 100 completed orders

      if (error) throw error

      // Get employee names separately
      if (data && data.length > 0) {
        const employeeIds = [...new Set(data.map(order => order.employee_id))]
        const { data: employees } = await supabase
          .from('profiles')
          .select('id, full_name')
          .in('id', employeeIds)

        // Add employee names to orders
        data.forEach(order => {
          const employee = employees?.find(emp => emp.id === order.employee_id)
          order.profiles = { full_name: employee?.full_name || 'Unknown' }
        })
      }

      setCompletedOrders(data || [])
    } catch (error) {
      console.error('Error fetching completed orders:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch transaction history')
    } finally {
      setLoading(false)
    }
  }

  const filterOrders = () => {
    let filtered = [...completedOrders]

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.table_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.profiles?.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customers?.full_name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Payment method filter
    if (filterPaymentMethod !== 'all') {
      filtered = filtered.filter(order =>
        order.payments?.some(payment => payment.payment_method === filterPaymentMethod)
      )
    }

    // Date range filter
    if (filterDateRange !== 'all') {
      const now = new Date()
      let startDate = new Date()

      switch (filterDateRange) {
        case 'today':
          startDate.setHours(0, 0, 0, 0)
          break
        case 'week':
          startDate.setDate(now.getDate() - 7)
          break
        case 'month':
          startDate.setMonth(now.getMonth() - 1)
          break
      }

      filtered = filtered.filter(order =>
        new Date(order.created_at) >= startDate
      )
    }

    setFilteredOrders(filtered)
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'Cash': return <MonetizationOn sx={{ color: colors.success.main }} />
      case 'Card': return <CreditCard sx={{ color: colors.info.main }} />
      case 'Credit': return <AccountBalanceWallet sx={{ color: colors.warning.main }} />
      default: return <Payment />
    }
  }

  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case 'Cash': return 'success'
      case 'Card': return 'info'
      case 'Credit': return 'warning'
      default: return 'default'
    }
  }

  const toggleOrderExpansion = (orderId: string) => {
    setExpandedOrder(expandedOrder === orderId ? null : orderId)
  }

  const refreshData = () => {
    fetchCompletedOrders()
  }

  // Pagination
  const indexOfLastOrder = currentPage * ordersPerPage
  const indexOfFirstOrder = indexOfLastOrder - ordersPerPage
  const currentOrders = filteredOrders.slice(indexOfFirstOrder, indexOfLastOrder)
  const totalPages = Math.ceil(filteredOrders.length / ordersPerPage)



  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress size={60} />
      </Box>
    )
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
        Transaction History
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        View completed orders and payment records
      </Typography>

      {/* Error Messages */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Payment Method</InputLabel>
                <Select
                  value={filterPaymentMethod}
                  label="Payment Method"
                  onChange={(e) => setFilterPaymentMethod(e.target.value)}
                >
                  <MenuItem value="all">All Methods</MenuItem>
                  <MenuItem value="Cash">Cash</MenuItem>
                  <MenuItem value="Card">Card</MenuItem>
                  <MenuItem value="Credit">Credit</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Date Range</InputLabel>
                <Select
                  value={filterDateRange}
                  label="Date Range"
                  onChange={(e) => setFilterDateRange(e.target.value)}
                >
                  <MenuItem value="all">All Time</MenuItem>
                  <MenuItem value="today">Today</MenuItem>
                  <MenuItem value="week">Last 7 Days</MenuItem>
                  <MenuItem value="month">Last 30 Days</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Refresh />}
                onClick={refreshData}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Transaction History Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Completed Transactions ({filteredOrders.length})
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Download />}
              size="small"
            >
              Export
            </Button>
          </Box>

          {currentOrders.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Receipt sx={{ fontSize: 64, color: colors.grey[400], mb: 2 }} />
              <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                No completed transactions
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {filteredOrders.length === 0
                  ? 'No transactions match your current filters.'
                  : 'No transactions found for the current page.'
                }
              </Typography>
            </Box>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Order ID</TableCell>
                    <TableCell>Date/Time</TableCell>
                    <TableCell>Table/Type</TableCell>
                    <TableCell>Items</TableCell>
                    <TableCell align="right">Total</TableCell>
                    <TableCell>Payment Method</TableCell>
                    <TableCell>Employee</TableCell>
                    <TableCell align="center">Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {currentOrders.map((order) => (
                    <TableRow key={order.id} hover>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace', fontWeight: 600 }}>
                          #{order.id.slice(0, 8)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(order.created_at).toLocaleDateString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(order.created_at).toLocaleTimeString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            {order.table_number || 'N/A'}
                          </Typography>
                          <Chip
                            label={order.order_type.replace('_', ' ').toUpperCase()}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {order.order_items?.length || 0} items
                        </Typography>
                        {order.order_items && order.order_items.length > 0 && (
                          <Typography variant="caption" color="text.secondary">
                            {order.order_items[0].products.name}
                            {order.order_items.length > 1 && ` +${order.order_items.length - 1} more`}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          ${order.total_amount.toFixed(2)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {order.payments && order.payments.length > 0 ? (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getPaymentMethodIcon(order.payments[0].payment_method)}
                            <Box>
                              <Typography variant="body2">
                                {order.payments[0].payment_method}
                              </Typography>
                              {order.payments[0].transaction_reference && (
                                <Typography variant="caption" color="text.secondary">
                                  Ref: {order.payments[0].transaction_reference}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            No payment record
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {order.profiles?.full_name || 'Unknown'}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label="COMPLETED"
                          size="small"
                          color="success"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    </Box>
  )
}

export default Bills
