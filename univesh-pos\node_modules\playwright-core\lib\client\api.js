"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var api_exports = {};
__export(api_exports, {
  APIRequest: () => import_fetch.APIRequest,
  APIRequestContext: () => import_fetch.APIRequestContext,
  APIResponse: () => import_fetch.APIResponse,
  Accessibility: () => import_accessibility.Accessibility,
  Android: () => import_android.Android,
  AndroidDevice: () => import_android.AndroidDevice,
  AndroidInput: () => import_android.AndroidInput,
  AndroidSocket: () => import_android.AndroidSocket,
  AndroidWebView: () => import_android.AndroidWebView,
  Browser: () => import_browser.Browser,
  BrowserContext: () => import_browserContext.BrowserContext,
  BrowserType: () => import_browserType.BrowserType,
  CDPSession: () => import_cdpSession.CDPSession,
  Clock: () => import_clock.Clock,
  ConsoleMessage: () => import_consoleMessage.ConsoleMessage,
  Coverage: () => import_coverage.Coverage,
  Dialog: () => import_dialog.Dialog,
  Download: () => import_download.Download,
  Electron: () => import_electron.Electron,
  ElectronApplication: () => import_electron.ElectronApplication,
  ElementHandle: () => import_elementHandle.ElementHandle,
  FileChooser: () => import_fileChooser.FileChooser,
  Frame: () => import_frame.Frame,
  FrameLocator: () => import_locator.FrameLocator,
  JSHandle: () => import_jsHandle.JSHandle,
  Keyboard: () => import_input.Keyboard,
  Locator: () => import_locator.Locator,
  Mouse: () => import_input.Mouse,
  Page: () => import_page.Page,
  Playwright: () => import_playwright.Playwright,
  Request: () => import_network.Request,
  Response: () => import_network.Response,
  Route: () => import_network.Route,
  Selectors: () => import_selectors.Selectors,
  TimeoutError: () => import_errors.TimeoutError,
  Touchscreen: () => import_input.Touchscreen,
  Tracing: () => import_tracing.Tracing,
  Video: () => import_video.Video,
  WebError: () => import_webError.WebError,
  WebSocket: () => import_network.WebSocket,
  WebSocketRoute: () => import_network.WebSocketRoute,
  Worker: () => import_worker.Worker
});
module.exports = __toCommonJS(api_exports);
var import_accessibility = require("./accessibility");
var import_android = require("./android");
var import_browser = require("./browser");
var import_browserContext = require("./browserContext");
var import_browserType = require("./browserType");
var import_clock = require("./clock");
var import_consoleMessage = require("./consoleMessage");
var import_coverage = require("./coverage");
var import_dialog = require("./dialog");
var import_download = require("./download");
var import_electron = require("./electron");
var import_locator = require("./locator");
var import_elementHandle = require("./elementHandle");
var import_fileChooser = require("./fileChooser");
var import_errors = require("./errors");
var import_frame = require("./frame");
var import_input = require("./input");
var import_jsHandle = require("./jsHandle");
var import_network = require("./network");
var import_fetch = require("./fetch");
var import_page = require("./page");
var import_selectors = require("./selectors");
var import_tracing = require("./tracing");
var import_video = require("./video");
var import_worker = require("./worker");
var import_cdpSession = require("./cdpSession");
var import_playwright = require("./playwright");
var import_webError = require("./webError");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  APIRequest,
  APIRequestContext,
  APIResponse,
  Accessibility,
  Android,
  AndroidDevice,
  AndroidInput,
  AndroidSocket,
  AndroidWebView,
  Browser,
  BrowserContext,
  BrowserType,
  CDPSession,
  Clock,
  ConsoleMessage,
  Coverage,
  Dialog,
  Download,
  Electron,
  ElectronApplication,
  ElementHandle,
  FileChooser,
  Frame,
  FrameLocator,
  JSHandle,
  Keyboard,
  Locator,
  Mouse,
  Page,
  Playwright,
  Request,
  Response,
  Route,
  Selectors,
  TimeoutError,
  Touchscreen,
  Tracing,
  Video,
  WebError,
  WebSocket,
  WebSocketRoute,
  Worker
});
