// Database Integration Test Script
// Tests all database views, RLS policies, and CRUD operations

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testDatabaseViews() {
  console.log('📊 Testing Database Views...')
  console.log('=' .repeat(50))

  const views = [
    'dashboard_daily_sales',
    'trending_dishes', 
    'best_employees',
    'customer_order_history',
    'payment_method_analysis',
    'hourly_sales_trends',
    'low_stock_products',
    'ongoing_orders_detailed'
  ]

  for (const view of views) {
    try {
      console.log(`\n🔍 Testing view: ${view}`)
      const { data, error } = await supabase
        .from(view)
        .select('*')
        .limit(5)

      if (error) {
        console.error(`❌ ${view} failed:`, error.message)
      } else {
        console.log(`✅ ${view} success - ${data.length} rows returned`)
        if (data.length > 0) {
          console.log(`   Sample columns: ${Object.keys(data[0]).join(', ')}`)
        }
      }
    } catch (err) {
      console.error(`❌ ${view} error:`, err.message)
    }
  }
}

async function testRLSPolicies() {
  console.log('\n🔒 Testing RLS Policies...')
  console.log('=' .repeat(50))

  // Test unauthenticated access (should be restricted)
  console.log('\n1️⃣ Testing unauthenticated access...')
  
  const tables = ['profiles', 'orders', 'products', 'customers', 'payments']
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1)

      if (error) {
        console.log(`✅ ${table} properly restricted: ${error.message}`)
      } else {
        console.log(`⚠️ ${table} accessible without auth - ${data.length} rows`)
      }
    } catch (err) {
      console.log(`✅ ${table} properly restricted: ${err.message}`)
    }
  }

  // Test authenticated access
  console.log('\n2️⃣ Testing authenticated access...')
  
  // Sign in first
  const { error: signInError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123'
  })

  if (signInError) {
    console.error('❌ Failed to sign in for RLS testing:', signInError)
    return
  }

  console.log('✅ Signed in successfully')

  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1)

      if (error) {
        console.log(`❌ ${table} access failed: ${error.message}`)
      } else {
        console.log(`✅ ${table} accessible when authenticated - ${data.length} rows`)
      }
    } catch (err) {
      console.log(`❌ ${table} access error: ${err.message}`)
    }
  }
}

async function testCRUDOperations() {
  console.log('\n📝 Testing CRUD Operations...')
  console.log('=' .repeat(50))

  // Ensure we're authenticated
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    console.error('❌ Not authenticated for CRUD testing')
    return
  }

  // Test CREATE - Add a test customer
  console.log('\n1️⃣ Testing CREATE operation...')
  const { data: newCustomer, error: createError } = await supabase
    .from('customers')
    .insert({
      full_name: 'Test Customer',
      email: '<EMAIL>',
      phone_number: '+1234567890'
    })
    .select()
    .single()

  if (createError) {
    console.error('❌ CREATE failed:', createError.message)
  } else {
    console.log('✅ CREATE successful - Customer created:', newCustomer.id)

    // Test READ
    console.log('\n2️⃣ Testing READ operation...')
    const { data: readCustomer, error: readError } = await supabase
      .from('customers')
      .select('*')
      .eq('id', newCustomer.id)
      .single()

    if (readError) {
      console.error('❌ READ failed:', readError.message)
    } else {
      console.log('✅ READ successful - Customer found:', readCustomer.full_name)
    }

    // Test UPDATE
    console.log('\n3️⃣ Testing UPDATE operation...')
    const { data: updatedCustomer, error: updateError } = await supabase
      .from('customers')
      .update({ full_name: 'Updated Test Customer' })
      .eq('id', newCustomer.id)
      .select()
      .single()

    if (updateError) {
      console.error('❌ UPDATE failed:', updateError.message)
    } else {
      console.log('✅ UPDATE successful - Name updated to:', updatedCustomer.full_name)
    }

    // Test DELETE
    console.log('\n4️⃣ Testing DELETE operation...')
    const { error: deleteError } = await supabase
      .from('customers')
      .delete()
      .eq('id', newCustomer.id)

    if (deleteError) {
      console.error('❌ DELETE failed:', deleteError.message)
    } else {
      console.log('✅ DELETE successful - Customer removed')
    }
  }
}

async function runAllTests() {
  console.log('🚀 Starting Database Integration Tests...')
  console.log('🕒 ' + new Date().toLocaleString())
  console.log('=' .repeat(60))

  try {
    await testDatabaseViews()
    await testRLSPolicies()
    await testCRUDOperations()

    console.log('\n' + '=' .repeat(60))
    console.log('🎉 Database Integration Tests Complete!')
    console.log('\n📋 Test Summary:')
    console.log('   ✅ Database views tested')
    console.log('   ✅ RLS policies validated')
    console.log('   ✅ CRUD operations verified')

  } catch (error) {
    console.error('❌ Test suite failed:', error)
  } finally {
    // Clean up - sign out
    await supabase.auth.signOut()
    console.log('\n🔐 Signed out successfully')
  }
}

runAllTests()
