import React from 'react'
import { Box, CircularProgress, Typography } from '@mui/material'

const LoadingScreen: React.FC = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        gap: 3
      }}
    >
      <CircularProgress
        size={60}
        thickness={4}
        sx={{
          color: '#1976d2'
        }}
      />
      <Typography
        variant="h6"
        sx={{
          color: '#333',
          fontWeight: 500
        }}
      >
        Loading Univesh POS...
      </Typography>
    </Box>
  )
}

export default LoadingScreen
