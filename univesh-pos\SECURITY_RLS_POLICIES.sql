-- =============================================
-- PRODUCTION-READY ROW LEVEL SECURITY POLICIES
-- Univesh Restaurant POS System
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_variations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_item_variations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- =============================================
-- HELPER FUNCTIONS FOR ROLE CHECKING
-- =============================================

-- Function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_id UUID)
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT r.role_name 
    FROM public.profiles p 
    JOIN public.roles r ON p.role_id = r.id 
    WHERE p.id = user_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role(user_id) = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin or manager
CREATE OR REPLACE FUNCTION is_admin_or_manager(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role(user_id) IN ('admin', 'manager');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- PROFILES TABLE POLICIES
-- =============================================

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile (limited fields)
CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Admins can view all profiles
CREATE POLICY "Admins can view all profiles" ON public.profiles
  FOR SELECT USING (is_admin(auth.uid()));

-- Admins can insert new profiles
CREATE POLICY "Admins can insert profiles" ON public.profiles
  FOR INSERT WITH CHECK (is_admin(auth.uid()));

-- Admins can update all profiles
CREATE POLICY "Admins can update all profiles" ON public.profiles
  FOR UPDATE USING (is_admin(auth.uid()));

-- =============================================
-- USER SETTINGS POLICIES
-- =============================================

-- Users can manage their own settings
CREATE POLICY "Users can manage own settings" ON public.user_settings
  FOR ALL USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- =============================================
-- SYSTEM SETTINGS POLICIES
-- =============================================

-- All authenticated users can read system settings
CREATE POLICY "All users can read system settings" ON public.system_settings
  FOR SELECT USING (auth.role() = 'authenticated');

-- Only admins can modify system settings
CREATE POLICY "Only admins can modify system settings" ON public.system_settings
  FOR ALL USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

-- =============================================
-- CATEGORIES POLICIES
-- =============================================

-- All authenticated users can read categories
CREATE POLICY "All users can read categories" ON public.categories
  FOR SELECT USING (auth.role() = 'authenticated');

-- Only admins and managers can modify categories
CREATE POLICY "Admins and managers can modify categories" ON public.categories
  FOR ALL USING (is_admin_or_manager(auth.uid()))
  WITH CHECK (is_admin_or_manager(auth.uid()));

-- =============================================
-- PRODUCTS POLICIES
-- =============================================

-- All authenticated users can read active products
CREATE POLICY "All users can read active products" ON public.products
  FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);

-- Admins and managers can read all products
CREATE POLICY "Admins and managers can read all products" ON public.products
  FOR SELECT USING (is_admin_or_manager(auth.uid()));

-- Only admins and managers can modify products
CREATE POLICY "Admins and managers can modify products" ON public.products
  FOR ALL USING (is_admin_or_manager(auth.uid()))
  WITH CHECK (is_admin_or_manager(auth.uid()));

-- =============================================
-- PRODUCT VARIATIONS POLICIES
-- =============================================

-- All authenticated users can read product variations
CREATE POLICY "All users can read product variations" ON public.product_variations
  FOR SELECT USING (auth.role() = 'authenticated');

-- Only admins and managers can modify product variations
CREATE POLICY "Admins and managers can modify product variations" ON public.product_variations
  FOR ALL USING (is_admin_or_manager(auth.uid()))
  WITH CHECK (is_admin_or_manager(auth.uid()));

-- =============================================
-- CUSTOMERS POLICIES
-- =============================================

-- All authenticated users can read customers
CREATE POLICY "All users can read customers" ON public.customers
  FOR SELECT USING (auth.role() = 'authenticated');

-- All authenticated users can create customers
CREATE POLICY "All users can create customers" ON public.customers
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Only admins and managers can update/delete customers
CREATE POLICY "Admins and managers can modify customers" ON public.customers
  FOR UPDATE USING (is_admin_or_manager(auth.uid()));

CREATE POLICY "Admins and managers can delete customers" ON public.customers
  FOR DELETE USING (is_admin_or_manager(auth.uid()));

-- =============================================
-- ORDERS POLICIES
-- =============================================

-- Users can read orders they created
CREATE POLICY "Users can read own orders" ON public.orders
  FOR SELECT USING (auth.uid() = employee_id);

-- Admins and managers can read all orders
CREATE POLICY "Admins and managers can read all orders" ON public.orders
  FOR SELECT USING (is_admin_or_manager(auth.uid()));

-- All authenticated users can create orders
CREATE POLICY "All users can create orders" ON public.orders
  FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = employee_id);

-- Users can update orders they created (if not completed)
CREATE POLICY "Users can update own orders" ON public.orders
  FOR UPDATE USING (
    auth.uid() = employee_id AND 
    status NOT IN ('completed', 'cancelled')
  );

-- Admins and managers can update any order
CREATE POLICY "Admins and managers can update all orders" ON public.orders
  FOR UPDATE USING (is_admin_or_manager(auth.uid()));

-- =============================================
-- ORDER ITEMS POLICIES
-- =============================================

-- Users can read order items for orders they can access
CREATE POLICY "Users can read accessible order items" ON public.order_items
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.orders o 
      WHERE o.id = order_id 
      AND (o.employee_id = auth.uid() OR is_admin_or_manager(auth.uid()))
    )
  );

-- Users can create order items for orders they created
CREATE POLICY "Users can create order items for own orders" ON public.order_items
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.orders o 
      WHERE o.id = order_id 
      AND o.employee_id = auth.uid()
      AND o.status NOT IN ('completed', 'cancelled')
    )
  );

-- Users can update/delete order items for orders they created (if not completed)
CREATE POLICY "Users can modify order items for own orders" ON public.order_items
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.orders o 
      WHERE o.id = order_id 
      AND o.employee_id = auth.uid()
      AND o.status NOT IN ('completed', 'cancelled')
    )
  );

-- =============================================
-- ORDER ITEM VARIATIONS POLICIES
-- =============================================

-- Follow same pattern as order items
CREATE POLICY "Users can read accessible order item variations" ON public.order_item_variations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.order_items oi
      JOIN public.orders o ON oi.order_id = o.id
      WHERE oi.id = order_item_id 
      AND (o.employee_id = auth.uid() OR is_admin_or_manager(auth.uid()))
    )
  );

CREATE POLICY "Users can modify order item variations for own orders" ON public.order_item_variations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.order_items oi
      JOIN public.orders o ON oi.order_id = o.id
      WHERE oi.id = order_item_id 
      AND o.employee_id = auth.uid()
      AND o.status NOT IN ('completed', 'cancelled')
    )
  );

-- =============================================
-- PAYMENTS POLICIES
-- =============================================

-- Users can read payments for orders they can access
CREATE POLICY "Users can read accessible payments" ON public.payments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.orders o 
      WHERE o.id = order_id 
      AND (o.employee_id = auth.uid() OR is_admin_or_manager(auth.uid()))
    )
  );

-- All authenticated users can create payments
CREATE POLICY "All users can create payments" ON public.payments
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND 
    auth.uid() = processed_by_employee_id
  );

-- Only admins and managers can update/delete payments
CREATE POLICY "Admins and managers can modify payments" ON public.payments
  FOR UPDATE USING (is_admin_or_manager(auth.uid()));

CREATE POLICY "Admins and managers can delete payments" ON public.payments
  FOR DELETE USING (is_admin_or_manager(auth.uid()));

-- =============================================
-- ROLES TABLE POLICIES (READ-ONLY FOR MOST)
-- =============================================

-- All authenticated users can read roles
CREATE POLICY "All users can read roles" ON public.roles
  FOR SELECT USING (auth.role() = 'authenticated');

-- Only admins can modify roles
CREATE POLICY "Only admins can modify roles" ON public.roles
  FOR ALL USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));
