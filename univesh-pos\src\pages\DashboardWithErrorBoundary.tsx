/**
 * Dashboard with Error Boundary Wrapper
 * 
 * This component wraps the Dashboard with a comprehensive error boundary
 * to handle React errors and provide graceful fallback UI.
 */

import React from 'react'
import ErrorBoundary from '../components/error/ErrorBoundary'
import Dashboard from './Dashboard'
import { Box, Typography, Button, Alert } from '@mui/material'
import { Dashboard as DashboardIcon, Refresh } from '@mui/icons-material'
import { colors } from '../theme'

// Custom fallback UI for Dashboard errors
const DashboardErrorFallback: React.FC = () => {
  const handleRetry = () => {
    window.location.reload()
  }

  const handleGoToMenu = () => {
    window.location.href = '/menu'
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
        Dashboard
      </Typography>
      
      <Alert 
        severity="error" 
        sx={{ mb: 3 }}
        action={
          <Button 
            color="inherit" 
            size="small" 
            onClick={handleRetry}
            startIcon={<Refresh />}
          >
            Retry
          </Button>
        }
      >
        Unable to load the dashboard. There was an error displaying the metrics and data.
      </Alert>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 300,
          textAlign: 'center',
          bgcolor: colors.grey[50],
          borderRadius: 2,
          p: 4
        }}
      >
        <DashboardIcon 
          sx={{ 
            fontSize: 64, 
            color: colors.grey[400], 
            mb: 2 
          }} 
        />
        
        <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
          Dashboard Temporarily Unavailable
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          We're having trouble loading the dashboard data. Please try refreshing the page or navigate to another section.
        </Typography>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            startIcon={<Refresh />}
            onClick={handleRetry}
          >
            Refresh Page
          </Button>
          
          <Button
            variant="outlined"
            onClick={handleGoToMenu}
          >
            Go to Menu
          </Button>
        </Box>
      </Box>
    </Box>
  )
}

// Error handler for Dashboard
const handleDashboardError = (error: Error, errorInfo: React.ErrorInfo) => {
  console.error('Dashboard Error:', {
    error: error.message,
    stack: error.stack,
    componentStack: errorInfo.componentStack,
    timestamp: new Date().toISOString()
  })

  // In production, send to error monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Example: errorMonitoringService.captureException(error, {
    //   tags: { component: 'Dashboard' },
    //   extra: errorInfo
    // })
  }
}

// Main component with error boundary
const DashboardWithErrorBoundary: React.FC = () => {
  return (
    <ErrorBoundary
      fallback={<DashboardErrorFallback />}
      onError={handleDashboardError}
      componentName="Dashboard"
      showDetails={process.env.NODE_ENV === 'development'}
    >
      <Dashboard />
    </ErrorBoundary>
  )
}

export default DashboardWithErrorBoundary
