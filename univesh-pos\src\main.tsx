import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'

// Initialize enhanced browser extension error handler (2025 best practices)
// This runs immediately to catch early extension errors
import('./utils/browserExtensionHandler').then(({ initializeBrowserExtensionHandler }) => {
  initializeBrowserExtensionHandler(false) // Set to true for debugging extension errors
}).catch(() => {
  // Fallback: continue without extension error suppression
  console.warn('Browser extension handler failed to initialize')
})

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
