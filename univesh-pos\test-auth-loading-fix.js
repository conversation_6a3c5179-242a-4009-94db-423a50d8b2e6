/**
 * Test Authentication Loading Fix
 * 
 * This script tests if the authentication loading issue has been resolved
 */

import puppeteer from 'puppeteer';

async function testAuthLoadingFix() {
  console.log('🧪 Testing Authentication Loading Fix...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    devtools: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Listen for console messages
    const consoleMessages = [];
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      
      // Log important auth messages
      if (text.includes('[AuthContext]') || text.includes('Loading') || text.includes('Profile')) {
        console.log(`📝 ${text}`);
      }
    });
    
    console.log('🌐 Step 1: Navigate to login page...');
    await page.goto('http://localhost:5174/', { waitUntil: 'networkidle0' });
    
    // Wait for login form
    await page.waitForSelector('input[type="text"]', { timeout: 10000 });
    console.log('✅ Login page loaded');
    
    console.log('🔐 Step 2: Perform login...');
    await page.type('input[type="text"]', 'ADMIN001');
    await page.type('input[type="password"]', 'admin123');
    
    console.log('🖱️ Clicking sign in button...');
    await page.click('button[type="submit"]');
    
    console.log('⏳ Step 3: Wait for dashboard to load...');
    
    // Wait for navigation with a reasonable timeout
    try {
      await page.waitForNavigation({ timeout: 10000 });
      console.log('✅ Navigation completed');
      
      const currentUrl = page.url();
      console.log(`📍 Current URL: ${currentUrl}`);
      
      if (!currentUrl.includes('/dashboard')) {
        console.log('❌ Not on dashboard page');
        return false;
      }
      
    } catch (error) {
      console.log('❌ Navigation timeout');
      return false;
    }
    
    console.log('🔍 Step 4: Check if dashboard loads properly...');
    
    // Wait for dashboard content to appear (not just loading screen)
    let dashboardLoaded = false;
    let attempts = 0;
    const maxAttempts = 10;
    
    while (!dashboardLoaded && attempts < maxAttempts) {
      attempts++;
      console.log(`   Attempt ${attempts}/${maxAttempts}: Checking dashboard state...`);
      
      // Check if still showing loading screen
      const loadingElement = await page.$('text=Loading Univesh POS');
      
      if (!loadingElement) {
        // Check if we can see dashboard content
        const dashboardContent = await page.evaluate(() => {
          // Look for dashboard-specific content
          const hasMetrics = document.querySelector('h4') && document.querySelector('h4').textContent.includes('Dashboard Overview');
          const hasCards = document.querySelectorAll('[class*="Card"], [class*="card"]').length > 0;
          const hasContent = document.body.textContent.length > 1000; // Reasonable content length
          
          return {
            hasMetrics,
            hasCards,
            hasContent,
            bodyText: document.body.textContent.substring(0, 200)
          };
        });
        
        console.log(`   Dashboard content check:`, dashboardContent);
        
        if (dashboardContent.hasMetrics || dashboardContent.hasCards) {
          dashboardLoaded = true;
          console.log('✅ Dashboard content detected!');
        } else if (dashboardContent.hasContent) {
          console.log('✅ Dashboard loaded (content detected)');
          dashboardLoaded = true;
        }
      } else {
        console.log('   Still showing loading screen...');
      }
      
      if (!dashboardLoaded) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    if (!dashboardLoaded) {
      console.log('❌ Dashboard failed to load after 10 seconds');
      
      // Get final page state for debugging
      const finalState = await page.evaluate(() => {
        return {
          title: document.title,
          bodyText: document.body.textContent.substring(0, 500),
          hasLoadingText: document.body.textContent.includes('Loading'),
          elementCount: document.querySelectorAll('*').length
        };
      });
      
      console.log('🔍 Final page state:', finalState);
      return false;
    }
    
    console.log('🔍 Step 5: Verify authentication state...');
    
    // Check for auth-related console messages
    const authMessages = consoleMessages.filter(msg => 
      msg.includes('[AuthContext]') && 
      (msg.includes('Profile fetched successfully') || msg.includes('Loading set to false'))
    );
    
    console.log(`📊 Auth messages found: ${authMessages.length}`);
    
    if (authMessages.length > 0) {
      console.log('✅ Authentication flow completed successfully');
      return true;
    } else {
      console.log('⚠️  Authentication flow unclear from console messages');
      return true; // Still consider success if dashboard loaded
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    return false;
  } finally {
    await browser.close();
  }
}

// Run the test
testAuthLoadingFix().then(success => {
  if (success) {
    console.log('\n🎉 Authentication Loading Fix Test PASSED!');
    console.log('✅ Dashboard loads properly after login');
    process.exit(0);
  } else {
    console.log('\n❌ Authentication Loading Fix Test FAILED!');
    console.log('❌ Dashboard still has loading issues');
    process.exit(1);
  }
}).catch(error => {
  console.error('\n💥 Test execution failed:', error);
  process.exit(1);
});
