import React from 'react'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>,
  <PERSON>
} from '@mui/material'
import {
  WifiOff,
  Refresh,
  Warning,
  CloudOff,
  SignalWifiOff
} from '@mui/icons-material'
import { colors } from '../../theme'

interface NetworkErrorProps {
  error: Error | string
  onRetry?: () => void
  variant?: 'inline' | 'fullscreen' | 'card'
  showDetails?: boolean
}

const NetworkErrorHandler: React.FC<NetworkErrorProps> = ({
  error,
  onRetry,
  variant = 'card',
  showDetails = false
}) => {
  const errorMessage = typeof error === 'string' ? error : error.message
  
  const getErrorType = () => {
    const message = errorMessage.toLowerCase()
    
    if (message.includes('network') || message.includes('fetch')) {
      return {
        type: 'network',
        icon: <WifiOff />,
        title: 'Network Connection Error',
        description: 'Unable to connect to the server. Please check your internet connection.',
        color: colors.error.main
      }
    }
    
    if (message.includes('timeout')) {
      return {
        type: 'timeout',
        icon: <CloudOff />,
        title: 'Request Timeout',
        description: 'The request took too long to complete. The server might be busy.',
        color: colors.warning.main
      }
    }
    
    if (message.includes('unauthorized') || message.includes('401')) {
      return {
        type: 'auth',
        icon: <Warning />,
        title: 'Authentication Error',
        description: 'Your session has expired. Please log in again.',
        color: colors.error.main
      }
    }
    
    if (message.includes('forbidden') || message.includes('403')) {
      return {
        type: 'permission',
        icon: <Warning />,
        title: 'Permission Denied',
        description: 'You do not have permission to perform this action.',
        color: colors.warning.main
      }
    }
    
    if (message.includes('server') || message.includes('500')) {
      return {
        type: 'server',
        icon: <CloudOff />,
        title: 'Server Error',
        description: 'The server encountered an error. Please try again later.',
        color: colors.error.main
      }
    }
    
    return {
      type: 'unknown',
      icon: <SignalWifiOff />,
      title: 'Connection Error',
      description: 'Something went wrong. Please try again.',
      color: colors.error.main
    }
  }

  const errorInfo = getErrorType()

  const getRetryText = () => {
    switch (errorInfo.type) {
      case 'network':
        return 'Check Connection & Retry'
      case 'timeout':
        return 'Try Again'
      case 'auth':
        return 'Refresh Session'
      case 'server':
        return 'Retry Request'
      default:
        return 'Retry'
    }
  }

  const content = (
    <>
      <Box sx={{ textAlign: 'center', mb: 3 }}>
        <Box
          sx={{
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 64,
            height: 64,
            borderRadius: '50%',
            backgroundColor: `${errorInfo.color}20`,
            color: errorInfo.color,
            mb: 2
          }}
        >
          {React.cloneElement(errorInfo.icon, { sx: { fontSize: 32 } })}
        </Box>
        
        <Typography variant="h6" sx={{ fontWeight: 600, mb: 1, color: errorInfo.color }}>
          {errorInfo.title}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {errorInfo.description}
        </Typography>

        <Chip
          label={errorInfo.type.toUpperCase()}
          size="small"
          sx={{
            backgroundColor: `${errorInfo.color}20`,
            color: errorInfo.color,
            fontWeight: 600
          }}
        />
      </Box>

      {showDetails && (
        <Alert severity="error" sx={{ mb: 3, textAlign: 'left' }}>
          <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
            Technical Details:
          </Typography>
          <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
            {errorMessage}
          </Typography>
        </Alert>
      )}

      {onRetry && (
        <Box sx={{ textAlign: 'center' }}>
          <Button
            variant="contained"
            startIcon={<Refresh />}
            onClick={onRetry}
            sx={{
              backgroundColor: errorInfo.color,
              '&:hover': {
                backgroundColor: errorInfo.color,
                opacity: 0.9
              }
            }}
          >
            {getRetryText()}
          </Button>
        </Box>
      )}
    </>
  )

  if (variant === 'inline') {
    return (
      <Alert 
        severity="error" 
        sx={{ mb: 2 }}
        action={
          onRetry && (
            <Button
              color="inherit"
              size="small"
              onClick={onRetry}
              startIcon={<Refresh />}
            >
              Retry
            </Button>
          )
        }
      >
        <Typography variant="body2">
          {errorInfo.description}
        </Typography>
      </Alert>
    )
  }

  if (variant === 'fullscreen') {
    return (
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          zIndex: 9999,
          p: 3
        }}
      >
        <Card sx={{ maxWidth: 400, width: '100%' }}>
          <CardContent sx={{ p: 4 }}>
            {content}
          </CardContent>
        </Card>
      </Box>
    )
  }

  return (
    <Card>
      <CardContent sx={{ p: 4 }}>
        {content}
      </CardContent>
    </Card>
  )
}

export default NetworkErrorHandler
