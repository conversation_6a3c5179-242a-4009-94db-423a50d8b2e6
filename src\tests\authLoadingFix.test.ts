/**
 * Test suite for authentication loading issue fixes
 * Tests the fixes implemented for the persistent loading screen issue
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { ThemeProvider } from '@mui/material/styles'
import { AuthProvider } from '../contexts/AuthContext'
import theme from '../theme'
import { supabase } from '../lib/supabase'

// Mock Supabase
vi.mock('../lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(),
      onAuthStateChange: vi.fn(() => ({
        data: { subscription: { unsubscribe: vi.fn() } }
      }))
    },
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn()
        })),
        limit: vi.fn()
      }))
    }))
  }
}))

// Mock network checker
vi.mock('../utils/authErrorHandler', () => ({
  NetworkChecker: {
    getStatus: vi.fn(() => true),
    addListener: vi.fn(),
    removeListener: vi.fn()
  },
  AuthErrorHandler: {
    retryOperation: vi.fn(),
    handleAuthError: vi.fn(() => ({
      shouldRetry: false,
      retryDelay: 1000
    }))
  },
  SessionManager: {
    clearRefreshState: vi.fn()
  }
}))

const TestComponent = () => {
  return (
    <ThemeProvider theme={theme}>
      <AuthProvider>
        <div data-testid="auth-content">Auth Content Loaded</div>
      </AuthProvider>
    </ThemeProvider>
  )
}

describe('Authentication Loading Fix', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Clear console to avoid noise
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should not hang on authentication initialization', async () => {
    // Mock successful session retrieval
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    mockGetSession.mockResolvedValue({
      data: { session: null },
      error: null
    })

    render(<TestComponent />)

    // Should not hang - content should be available within reasonable time
    await waitFor(
      () => {
        expect(screen.getByTestId('auth-content')).toBeInTheDocument()
      },
      { timeout: 5000 } // 5 second timeout
    )
  })

  it('should handle authentication timeout gracefully', async () => {
    // Mock hanging session retrieval
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    mockGetSession.mockImplementation(() => 
      new Promise(() => {}) // Never resolves
    )

    render(<TestComponent />)

    // Should timeout and still render content
    await waitFor(
      () => {
        expect(screen.getByTestId('auth-content')).toBeInTheDocument()
      },
      { timeout: 20000 } // 20 second timeout (longer than our 15s auth timeout)
    )
  })

  it('should handle network errors without infinite loading', async () => {
    // Mock network error
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    mockGetSession.mockRejectedValue(new Error('Network Error'))

    render(<TestComponent />)

    // Should handle error and not hang
    await waitFor(
      () => {
        expect(screen.getByTestId('auth-content')).toBeInTheDocument()
      },
      { timeout: 10000 }
    )
  })

  it('should handle Supabase connection test failure', async () => {
    // Mock Supabase connection test failure
    const mockFrom = vi.mocked(supabase.from)
    mockFrom.mockReturnValue({
      select: vi.fn(() => ({
        limit: vi.fn(() => Promise.reject(new Error('Connection failed')))
      }))
    } as any)

    const mockGetSession = vi.mocked(supabase.auth.getSession)
    mockGetSession.mockResolvedValue({
      data: { session: null },
      error: null
    })

    render(<TestComponent />)

    // Should handle connection failure gracefully
    await waitFor(
      () => {
        expect(screen.getByTestId('auth-content')).toBeInTheDocument()
      },
      { timeout: 15000 }
    )
  })

  it('should retry authentication initialization with exponential backoff', async () => {
    let callCount = 0
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    
    mockGetSession.mockImplementation(() => {
      callCount++
      if (callCount < 3) {
        return Promise.reject(new Error('Temporary error'))
      }
      return Promise.resolve({
        data: { session: null },
        error: null
      })
    })

    render(<TestComponent />)

    // Should eventually succeed after retries
    await waitFor(
      () => {
        expect(screen.getByTestId('auth-content')).toBeInTheDocument()
        expect(callCount).toBeGreaterThanOrEqual(3)
      },
      { timeout: 15000 }
    )
  })

  it('should clear timeout when authentication completes successfully', async () => {
    const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout')
    
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    mockGetSession.mockResolvedValue({
      data: { session: null },
      error: null
    })

    render(<TestComponent />)

    await waitFor(
      () => {
        expect(screen.getByTestId('auth-content')).toBeInTheDocument()
      },
      { timeout: 5000 }
    )

    // Timeout should be cleared when auth completes
    expect(clearTimeoutSpy).toHaveBeenCalled()
  })
})

describe('Debug Logging', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should log authentication flow steps', async () => {
    const consoleSpy = vi.spyOn(console, 'log')
    
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    mockGetSession.mockResolvedValue({
      data: { session: null },
      error: null
    })

    render(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByTestId('auth-content')).toBeInTheDocument()
    })

    // Should have logged authentication steps
    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('[AuthContext]')
    )
  })
})
