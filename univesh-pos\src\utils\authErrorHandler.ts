import { AuthError } from '@supabase/supabase-js'

export interface AuthErrorInfo {
  code: string
  message: string
  userMessage: string
  shouldRetry: boolean
  retryDelay?: number
}

export class AuthErrorHandler {
  private static retryAttempts = new Map<string, number>()
  private static readonly MAX_RETRY_ATTEMPTS = 3
  private static readonly RETRY_DELAYS = [1000, 2000, 5000] // Progressive delays

  static handleAuthError(error: any): AuthErrorInfo {
    // Handle network errors (Failed to fetch)
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed',
        userMessage: 'Unable to connect to the server. Please check your internet connection and try again.',
        shouldRetry: true,
        retryDelay: 2000
      }
    }

    // Handle Supabase auth errors
    if (error?.message) {
      const message = error.message.toLowerCase()
      
      if (message.includes('invalid_grant') || message.includes('refresh_token')) {
        return {
          code: 'TOKEN_REFRESH_FAILED',
          message: 'Token refresh failed',
          userMessage: 'Your session has expired. Please sign in again.',
          shouldRetry: false
        }
      }

      if (message.includes('invalid_credentials') || message.includes('invalid login')) {
        return {
          code: 'INVALID_CREDENTIALS',
          message: 'Invalid credentials',
          userMessage: 'Invalid Sales ID or password. Please check your credentials and try again.',
          shouldRetry: false
        }
      }

      if (message.includes('too_many_requests') || message.includes('rate limit')) {
        return {
          code: 'RATE_LIMITED',
          message: 'Rate limited',
          userMessage: 'Too many attempts. Please wait a moment before trying again.',
          shouldRetry: true,
          retryDelay: 10000
        }
      }

      if (message.includes('email_not_confirmed')) {
        return {
          code: 'EMAIL_NOT_CONFIRMED',
          message: 'Email not confirmed',
          userMessage: 'Please check your email and confirm your account before signing in.',
          shouldRetry: false
        }
      }

      if (message.includes('signup_disabled')) {
        return {
          code: 'SIGNUP_DISABLED',
          message: 'Signup disabled',
          userMessage: 'Account registration is currently disabled. Please contact your administrator.',
          shouldRetry: false
        }
      }
    }

    // Handle HTTP status codes
    if (error?.status) {
      switch (error.status) {
        case 400:
          return {
            code: 'BAD_REQUEST',
            message: 'Bad request',
            userMessage: 'Invalid request. Please check your input and try again.',
            shouldRetry: false
          }
        case 401:
          return {
            code: 'UNAUTHORIZED',
            message: 'Unauthorized',
            userMessage: 'Authentication failed. Please sign in again.',
            shouldRetry: false
          }
        case 403:
          return {
            code: 'FORBIDDEN',
            message: 'Forbidden',
            userMessage: 'You do not have permission to perform this action.',
            shouldRetry: false
          }
        case 429:
          return {
            code: 'RATE_LIMITED',
            message: 'Rate limited',
            userMessage: 'Too many requests. Please wait before trying again.',
            shouldRetry: true,
            retryDelay: 5000
          }
        case 500:
        case 502:
        case 503:
        case 504:
          return {
            code: 'SERVER_ERROR',
            message: 'Server error',
            userMessage: 'Server is temporarily unavailable. Please try again in a moment.',
            shouldRetry: true,
            retryDelay: 3000
          }
      }
    }

    // Default error handling
    return {
      code: 'UNKNOWN_ERROR',
      message: error?.message || 'Unknown error occurred',
      userMessage: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
      shouldRetry: true,
      retryDelay: 2000
    }
  }

  static shouldRetryOperation(errorCode: string): boolean {
    const attempts = this.retryAttempts.get(errorCode) || 0
    return attempts < this.MAX_RETRY_ATTEMPTS
  }

  static getRetryDelay(errorCode: string): number {
    const attempts = this.retryAttempts.get(errorCode) || 0
    return this.RETRY_DELAYS[Math.min(attempts, this.RETRY_DELAYS.length - 1)]
  }

  static incrementRetryAttempt(errorCode: string): void {
    const attempts = this.retryAttempts.get(errorCode) || 0
    this.retryAttempts.set(errorCode, attempts + 1)
  }

  static resetRetryAttempts(errorCode: string): void {
    this.retryAttempts.delete(errorCode)
  }

  static async retryOperation<T>(
    operation: () => Promise<T>,
    errorCode: string,
    maxAttempts: number = this.MAX_RETRY_ATTEMPTS
  ): Promise<T> {
    let lastError: any

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const result = await operation()
        this.resetRetryAttempts(errorCode)
        return result
      } catch (error) {
        lastError = error
        const errorInfo = this.handleAuthError(error)
        
        if (!errorInfo.shouldRetry || attempt === maxAttempts - 1) {
          throw error
        }

        this.incrementRetryAttempt(errorCode)
        const delay = errorInfo.retryDelay || this.getRetryDelay(errorCode)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    throw lastError
  }
}

// Network connectivity checker
export class NetworkChecker {
  private static isOnline = navigator.onLine
  private static listeners: ((online: boolean) => void)[] = []

  static {
    window.addEventListener('online', () => {
      this.isOnline = true
      this.notifyListeners(true)
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
      this.notifyListeners(false)
    })
  }

  static getStatus(): boolean {
    return this.isOnline
  }

  static addListener(callback: (online: boolean) => void): void {
    this.listeners.push(callback)
  }

  static removeListener(callback: (online: boolean) => void): void {
    this.listeners = this.listeners.filter(listener => listener !== callback)
  }

  private static notifyListeners(online: boolean): void {
    this.listeners.forEach(listener => listener(online))
  }

  static async checkConnectivity(): Promise<boolean> {
    if (!navigator.onLine) {
      return false
    }

    try {
      const response = await fetch('/favicon.ico', {
        method: 'HEAD',
        cache: 'no-cache'
      })
      return response.ok
    } catch {
      return false
    }
  }
}

// Session manager for handling token refresh
export class SessionManager {
  private static refreshPromise: Promise<any> | null = null
  private static isRefreshing = false

  static async refreshSession(supabase: any): Promise<any> {
    // Prevent multiple simultaneous refresh attempts
    if (this.isRefreshing && this.refreshPromise) {
      return this.refreshPromise
    }

    this.isRefreshing = true
    this.refreshPromise = this.performRefresh(supabase)

    try {
      const result = await this.refreshPromise
      return result
    } finally {
      this.isRefreshing = false
      this.refreshPromise = null
    }
  }

  private static async performRefresh(supabase: any): Promise<any> {
    try {
      const { data, error } = await supabase.auth.refreshSession()
      
      if (error) {
        throw error
      }

      return data
    } catch (error) {
      // If refresh fails, clear the session
      await supabase.auth.signOut()
      throw error
    }
  }

  static clearRefreshState(): void {
    this.isRefreshing = false
    this.refreshPromise = null
  }
}
