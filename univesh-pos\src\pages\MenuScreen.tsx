import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Button,
  Tabs,
  Tab,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Divider,
  IconButton,
  Badge,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  CircularProgress,
  Alert,
  Collapse,
  RadioGroup,
  FormControlLabel,
  Radio
} from '@mui/material'
import {
  Add,
  Remove,
  ShoppingCart,
  Delete,
  Restaurant,
  LocalDining,
  LocalCafe,
  Cake,
  Payment,
  ExpandMore,
  ExpandLess
} from '@mui/icons-material'
import { colors } from '../theme'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useErrorHandler } from '../components/error/ErrorBoundary'

interface Product {
  id: string
  name: string
  description: string | null
  price: number
  current_stock: number
  image_url: string | null
  category_id: string | null
  categories?: {
    name: string
  }
}

interface Category {
  id: string
  name: string
  description: string | null
}

interface CartItem {
  product: Product
  quantity: number
  subtotal: number
}

interface OrderData {
  orderType: 'dine_in' | 'delivery' | 'take_away'
  tableNumber: string
  cart: CartItem[]
  subtotal: number
  taxAmount: number
  deliveryFee: number
  total: number
}

interface PaymentData {
  method: 'Cash' | 'Card' | 'Credit'
  transactionReference: string
  customTaxRate?: number
  customDeliveryFee?: number
}

const MenuScreen: React.FC = () => {
  const { user } = useAuth()
  const { reportError } = useErrorHandler()

  // State management with proper initialization
  const [categories, setCategories] = useState<Category[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [cart, setCart] = useState<CartItem[]>([])
  const [orderData, setOrderData] = useState<OrderData>({
    orderType: 'dine_in',
    tableNumber: '',
    cart: [],
    subtotal: 0,
    taxAmount: 0,
    deliveryFee: 0,
    total: 0
  })

  // Payment state
  const [paymentData, setPaymentData] = useState<PaymentData>({
    method: 'Cash',
    transactionReference: '',
    customTaxRate: undefined,
    customDeliveryFee: undefined
  })
  const [showPaymentSection, setShowPaymentSection] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [processing, setProcessing] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    updateOrderTotals()
  }, [cart]) // Removed updateOrderTotals from dependencies to prevent infinite re-renders

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Validate user authentication
      if (!user) {
        throw new Error('User not authenticated')
      }

      // Fetch categories with error handling
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('categories')
        .select('*')
        .order('name')

      if (categoriesError) {
        reportError(categoriesError, 'MenuScreen.fetchCategories')
        throw new Error(`Failed to fetch categories: ${categoriesError.message}`)
      }

      // Fetch products with categories
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select(`
          *,
          categories(name)
        `)
        .eq('is_active', true)
        .order('name')

      if (productsError) {
        reportError(productsError, 'MenuScreen.fetchProducts')
        throw new Error(`Failed to fetch products: ${productsError.message}`)
      }

      // Validate data integrity
      if (!Array.isArray(categoriesData) || !Array.isArray(productsData)) {
        throw new Error('Invalid data format received from server')
      }

      setCategories(categoriesData)
      setProducts(productsData)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch menu data'
      console.error('Error fetching data:', error)
      reportError(error instanceof Error ? error : new Error(errorMessage), 'MenuScreen.fetchData')
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [user, reportError])

  const updateOrderTotals = useCallback(() => {
    const subtotal = cart.reduce((sum, item) => sum + item.subtotal, 0)

    // Calculate tax based on order type and custom rate
    let taxRate = 0.05 // Default 5%
    if (orderData.orderType === 'dine_in') {
      taxRate = paymentData.customTaxRate !== undefined ? paymentData.customTaxRate : 0.10 // 10% for dine-in
    }
    const taxAmount = subtotal * taxRate

    // Calculate delivery fee
    let deliveryFee = 0
    if (orderData.orderType === 'delivery') {
      deliveryFee = paymentData.customDeliveryFee !== undefined ? paymentData.customDeliveryFee : 1.00 // $1.00 default
    }

    const total = subtotal + taxAmount + deliveryFee

    setOrderData(prev => ({
      ...prev,
      cart,
      subtotal,
      taxAmount,
      deliveryFee,
      total
    }))
  }, [cart, orderData.orderType, paymentData.customTaxRate, paymentData.customDeliveryFee])

  const addToCart = (product: Product) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.product.id === product.id)

      if (existingItem) {
        return prevCart.map(item =>
          item.product.id === product.id
            ? { ...item, quantity: item.quantity + 1, subtotal: (item.quantity + 1) * product.price }
            : item
        )
      } else {
        return [...prevCart, {
          product,
          quantity: 1,
          subtotal: product.price
        }]
      }
    })
  }

  const updateCartItemQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId)
      return
    }

    setCart(prevCart =>
      prevCart.map(item =>
        item.product.id === productId
          ? { ...item, quantity: newQuantity, subtotal: newQuantity * item.product.price }
          : item
      )
    )
  }

  const removeFromCart = (productId: string) => {
    setCart(prevCart => prevCart.filter(item => item.product.id !== productId))
  }

  const clearCart = () => {
    setCart([])
  }

  const processOrderAndPayment = async () => {
    if (!user || cart.length === 0) return

    try {
      setProcessing(true)
      setError(null)

      // Validate required fields
      if (orderData.orderType === 'dine_in' && !orderData.tableNumber.trim()) {
        throw new Error('Table number is required for dine-in orders')
      }

      if (paymentData.method === 'Card' && !paymentData.transactionReference.trim()) {
        throw new Error('Transaction reference is required for card payments')
      }

      // Create order in database
      const orderInsertData = {
        table_number: orderData.orderType === 'dine_in' ? orderData.tableNumber : null,
        order_type: orderData.orderType,
        status: 'completed' as const, // Directly set to completed since payment is processed
        subtotal_amount: orderData.subtotal,
        tax_amount: orderData.taxAmount,
        discount_amount: 0,
        complementary_amount: orderData.deliveryFee, // Store delivery fee in complementary_amount
        total_amount: orderData.total,
        employee_id: user.id,
        customer_id: null,
        notes: `Payment: ${paymentData.method}${paymentData.transactionReference ? ` - Ref: ${paymentData.transactionReference}` : ''}`
      }

      const { data: orderResult, error: orderError } = await supabase
        .from('orders')
        .insert(orderInsertData)
        .select()
        .single()

      if (orderError) throw orderError

      // Create order items
      const orderItemsData = cart.map(item => ({
        order_id: orderResult.id,
        product_id: item.product.id,
        quantity: item.quantity,
        unit_price_at_order: item.product.price,
        item_subtotal: item.subtotal
      }))

      const { error: orderItemsError } = await supabase
        .from('order_items')
        .insert(orderItemsData)

      if (orderItemsError) throw orderItemsError

      // Create payment record
      const paymentInsertData = {
        order_id: orderResult.id,
        amount: orderData.total,
        payment_method: paymentData.method,
        status: 'completed' as const,
        transaction_reference: paymentData.transactionReference || null,
        processed_by_employee_id: user.id
      }

      const { error: paymentError } = await supabase
        .from('payments')
        .insert(paymentInsertData)

      if (paymentError) throw paymentError

      // Update inventory for each order item
      for (const item of cart) {
        const { error: inventoryError } = await supabase.rpc('decrement_product_stock', {
          product_id: item.product.id,
          quantity: item.quantity
        })

        if (inventoryError) {
          console.warn('Failed to update inventory for product:', item.product.id, inventoryError)
        }
      }

      // Clear cart and reset payment section
      clearCart()
      setShowPaymentSection(false)
      setPaymentData({
        method: 'Cash',
        transactionReference: '',
        customTaxRate: undefined,
        customDeliveryFee: undefined
      })

      setSuccess(`Order #${orderResult.id.slice(0, 8)} completed and paid successfully!`)
      setTimeout(() => setSuccess(null), 5000)

    } catch (error) {
      console.error('Error processing order and payment:', error)
      setError(error instanceof Error ? error.message : 'Failed to process order and payment')
    } finally {
      setProcessing(false)
    }
  }

  const processOrder = async () => {
    if (!showPaymentSection) {
      setShowPaymentSection(true)
      return
    }

    await processOrderAndPayment()
  }

  // Memoize filtered products to prevent unnecessary re-renders
  const filteredProducts = useMemo(() => {
    if (selectedCategory === 'all') {
      return products
    }
    return products.filter(product => product.category_id === selectedCategory)
  }, [products, selectedCategory])

  const getCategoryIcon = (categoryName: string) => {
    switch (categoryName.toLowerCase()) {
      case 'food':
        return <Restaurant />
      case 'beverages':
        return <LocalCafe />
      case 'desserts':
        return <Cake />
      default:
        return <LocalDining />
    }
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress size={60} />
      </Box>
    )
  }

  if (error) {
    return (
      <Box>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
          Menu & Orders
        </Typography>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      </Box>
    )
  }

  return (
    <Box sx={{ height: 'calc(100vh - 120px)', display: 'flex', gap: 3 }}>
      {/* Success/Error Messages */}
      {success && (
        <Alert severity="success" sx={{ position: 'fixed', top: 80, right: 20, zIndex: 1000, minWidth: 300 }}>
          {success}
        </Alert>
      )}
      {error && (
        <Alert severity="error" sx={{ position: 'fixed', top: 80, right: 20, zIndex: 1000, minWidth: 300 }}>
          {error}
        </Alert>
      )}

      {/* Left Panel - Menu */}
      <Box sx={{ flex: 2, display: 'flex', flexDirection: 'column' }}>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
          Menu & Orders
        </Typography>

        {/* Category Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={selectedCategory}
            onChange={(_, value) => setSelectedCategory(value)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab
              label="All Items"
              value="all"
              icon={<Restaurant />}
              iconPosition="start"
            />
            {categories.map(category => (
              <Tab
                key={category.id}
                label={category.name}
                value={category.id}
                icon={getCategoryIcon(category.name)}
                iconPosition="start"
              />
            ))}
          </Tabs>
        </Paper>

        {/* Products Grid */}
        <Box sx={{ flex: 1, overflow: 'auto' }}>
          <Grid container spacing={2}>
            {filteredProducts.map(product => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    cursor: 'pointer',
                    '&:hover': {
                      boxShadow: 4
                    }
                  }}
                  onClick={() => addToCart(product)}
                >
                  <CardMedia
                    component="div"
                    sx={{
                      height: 140,
                      backgroundColor: colors.grey[200],
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    {product.image_url ? (
                      <Box
                        component="img"
                        src={product.image_url}
                        alt={product.name}
                        sx={{ width: '100%', height: '100%', objectFit: 'cover' }}
                      />
                    ) : (
                      <Restaurant sx={{ fontSize: 48, color: colors.grey[400] }} />
                    )}
                  </CardMedia>
                  <CardContent sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      {product.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flex: 1 }}>
                      {product.description || 'No description available'}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="h6" sx={{ fontWeight: 700, color: colors.primary.main }}>
                        ${product.price.toFixed(2)}
                      </Typography>
                      <Chip
                        label={`Stock: ${product.current_stock}`}
                        size="small"
                        color={product.current_stock > 10 ? 'success' : product.current_stock > 0 ? 'warning' : 'error'}
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>

      {/* Right Panel - Cart & Order - FIXED POSITION */}
      <Box sx={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        minWidth: 400,
        position: 'sticky',
        top: 0,
        height: 'calc(100vh - 120px)'
      }}>
        <Paper sx={{ flex: 1, display: 'flex', flexDirection: 'column', p: 3 }}>
          {/* Order Header */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h5" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
              <ShoppingCart />
              Current Order
              <Badge badgeContent={cart.length} color="primary" />
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>Order Type</InputLabel>
                  <Select
                    value={orderData.orderType}
                    label="Order Type"
                    onChange={(e) => setOrderData(prev => ({ ...prev, orderType: e.target.value as 'dine_in' | 'delivery' | 'take_away' }))}
                  >
                    <MenuItem value="dine_in">Dine In</MenuItem>
                    <MenuItem value="take_away">Take Away</MenuItem>
                    <MenuItem value="delivery">Delivery</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Table Number"
                  value={orderData.tableNumber}
                  onChange={(e) => setOrderData(prev => ({ ...prev, tableNumber: e.target.value }))}
                  disabled={orderData.orderType !== 'dine_in'}
                  required={orderData.orderType === 'dine_in'}
                />
              </Grid>
            </Grid>
          </Box>

          <Divider sx={{ mb: 2 }} />

          {/* Cart Items - SCROLLABLE */}
          <Box sx={{ flex: 1, overflow: 'auto', mb: 2, maxHeight: 'calc(100vh - 500px)' }}>
            {cart.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <ShoppingCart sx={{ fontSize: 48, color: colors.grey[400], mb: 2 }} />
                <Typography variant="body1" color="text.secondary">
                  Your cart is empty
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Add items from the menu to get started
                </Typography>
              </Box>
            ) : (
              <List>
                {cart.map((item) => (
                  <ListItem key={item.product.id} sx={{ px: 0, py: 1 }}>
                    <ListItemText
                      primary={
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {item.product.name}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="body2" color="text.secondary">
                          ${item.product.price.toFixed(2)} each
                        </Typography>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => updateCartItemQuantity(item.product.id, item.quantity - 1)}
                        >
                          <Remove />
                        </IconButton>
                        <Typography variant="body1" sx={{ minWidth: 20, textAlign: 'center' }}>
                          {item.quantity}
                        </Typography>
                        <IconButton
                          size="small"
                          onClick={() => updateCartItemQuantity(item.product.id, item.quantity + 1)}
                        >
                          <Add />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => removeFromCart(item.product.id)}
                          sx={{ ml: 1 }}
                        >
                          <Delete />
                        </IconButton>
                        <Typography variant="body1" sx={{ fontWeight: 600, minWidth: 60, textAlign: 'right' }}>
                          ${item.subtotal.toFixed(2)}
                        </Typography>
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            )}
          </Box>

          {/* Order Summary */}
          {cart.length > 0 && (
            <>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Subtotal:</Typography>
                  <Typography variant="body1">${orderData.subtotal.toFixed(2)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">
                    Tax ({orderData.orderType === 'dine_in' ? '10%' : '5%'}):
                  </Typography>
                  <Typography variant="body1">${orderData.taxAmount.toFixed(2)}</Typography>
                </Box>
                {orderData.deliveryFee > 0 && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body1">Delivery Fee:</Typography>
                    <Typography variant="body1">${orderData.deliveryFee.toFixed(2)}</Typography>
                  </Box>
                )}
                <Divider sx={{ my: 1 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Total:</Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: colors.primary.main }}>
                    ${orderData.total.toFixed(2)}
                  </Typography>
                </Box>

                {/* Payment Section */}
                <Collapse in={showPaymentSection}>
                  <Box sx={{ mb: 3, p: 2, bgcolor: colors.grey[50], borderRadius: 1 }}>
                    <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Payment />
                      Payment Details
                    </Typography>

                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <FormControl component="fieldset">
                          <Typography variant="body2" sx={{ mb: 1 }}>Payment Method</Typography>
                          <RadioGroup
                            value={paymentData.method}
                            onChange={(e) => setPaymentData(prev => ({ ...prev, method: e.target.value as 'Cash' | 'Card' | 'Credit' }))}
                            row
                          >
                            <FormControlLabel value="Cash" control={<Radio />} label="Cash" />
                            <FormControlLabel value="Card" control={<Radio />} label="Card" />
                            <FormControlLabel value="Credit" control={<Radio />} label="Credit" />
                          </RadioGroup>
                        </FormControl>
                      </Grid>

                      {paymentData.method === 'Card' && (
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            label="Transaction Reference"
                            value={paymentData.transactionReference}
                            onChange={(e) => setPaymentData(prev => ({ ...prev, transactionReference: e.target.value }))}
                            placeholder="Card terminal ID, receipt number, etc."
                            required
                          />
                        </Grid>
                      )}

                      {/* Custom Tax Rate */}
                      <Grid item xs={6}>
                        <TextField
                          fullWidth
                          label="Custom Tax Rate (%)"
                          type="number"
                          value={paymentData.customTaxRate !== undefined ? paymentData.customTaxRate * 100 : ''}
                          onChange={(e) => setPaymentData(prev => ({
                            ...prev,
                            customTaxRate: e.target.value ? Number(e.target.value) / 100 : undefined
                          }))}
                          placeholder={orderData.orderType === 'dine_in' ? '10' : '5'}
                          inputProps={{ min: 0, max: 100, step: 0.1 }}
                        />
                      </Grid>

                      {/* Custom Delivery Fee */}
                      {orderData.orderType === 'delivery' && (
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            label="Custom Delivery Fee ($)"
                            type="number"
                            value={paymentData.customDeliveryFee !== undefined ? paymentData.customDeliveryFee : ''}
                            onChange={(e) => setPaymentData(prev => ({
                              ...prev,
                              customDeliveryFee: e.target.value ? Number(e.target.value) : undefined
                            }))}
                            placeholder="1.00"
                            inputProps={{ min: 0, step: 0.01 }}
                          />
                        </Grid>
                      )}
                    </Grid>
                  </Box>
                </Collapse>

                {/* Action Buttons */}
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={clearCart}
                      startIcon={<Delete />}
                      disabled={processing}
                    >
                      Clear Cart
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      fullWidth
                      variant="contained"
                      size="large"
                      disabled={cart.length === 0 || !user || processing}
                      onClick={processOrder}
                      startIcon={showPaymentSection ? <Payment /> : <ExpandMore />}
                    >
                      {processing ? (
                        <CircularProgress size={20} />
                      ) : showPaymentSection ? (
                        'Complete Payment'
                      ) : (
                        'Place Order & Pay'
                      )}
                    </Button>
                  </Grid>
                </Grid>
              </Box>
            </>
          )}
        </Paper>
      </Box>
    </Box>
  )
}

export default MenuScreen
