# =============================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# Univesh Restaurant POS System
# =============================================

# Application Environment
NODE_ENV=production
VITE_APP_ENV=production
VITE_APP_VERSION=1.0.0
VITE_APP_NAME="Univesh Restaurant POS"

# Supabase Configuration (Production)
VITE_SUPABASE_URL=https://kufsqfbiilphymiehwet.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU

# Security Configuration
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_CONSOLE_LOGS=false
VITE_ENABLE_ERROR_REPORTING=true

# Performance Configuration
VITE_ENABLE_BUNDLE_ANALYZER=false
VITE_ENABLE_PWA=true
VITE_ENABLE_SERVICE_WORKER=true

# Monitoring & Analytics
VITE_SENTRY_DSN=your-sentry-dsn
VITE_GOOGLE_ANALYTICS_ID=your-ga-id
VITE_HOTJAR_ID=your-hotjar-id

# Feature Flags
VITE_ENABLE_OFFLINE_MODE=true
VITE_ENABLE_REAL_TIME_UPDATES=true
VITE_ENABLE_PUSH_NOTIFICATIONS=false

# API Configuration
VITE_API_TIMEOUT=30000
VITE_MAX_RETRY_ATTEMPTS=3
VITE_RETRY_DELAY=1000

# Cache Configuration
VITE_CACHE_DURATION=3600000
VITE_ENABLE_CACHE=true

# Build Configuration
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_MINIFY=true
VITE_BUILD_COMPRESSION=true