// Input validation and sanitization utilities for production security

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  sanitizedValue?: any
}

// Email validation with comprehensive regex
export const validateEmail = (email: string): ValidationResult => {
  const errors: string[] = []
  
  if (!email || email.trim().length === 0) {
    errors.push('Email is required')
    return { isValid: false, errors }
  }

  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
  
  if (!emailRegex.test(email)) {
    errors.push('Invalid email format')
  }

  if (email.length > 254) {
    errors.push('Email is too long (max 254 characters)')
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: email.trim().toLowerCase()
  }
}

// Phone number validation and formatting
export const validatePhoneNumber = (phone: string): ValidationResult => {
  const errors: string[] = []
  
  if (!phone || phone.trim().length === 0) {
    return { isValid: true, errors: [], sanitizedValue: null } // Phone is optional
  }

  // Remove all non-digit characters
  const digitsOnly = phone.replace(/\D/g, '')
  
  if (digitsOnly.length < 10 || digitsOnly.length > 15) {
    errors.push('Phone number must be between 10-15 digits')
  }

  // Format as (XXX) XXX-XXXX for US numbers
  let formatted = digitsOnly
  if (digitsOnly.length === 10) {
    formatted = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6)}`
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: formatted
  }
}

// Password strength validation
export const validatePassword = (password: string): ValidationResult => {
  const errors: string[] = []
  
  if (!password) {
    errors.push('Password is required')
    return { isValid: false, errors }
  }

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }

  if (password.length > 128) {
    errors.push('Password is too long (max 128 characters)')
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }

  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: password // Don't sanitize passwords
  }
}

// Sanitize text input to prevent XSS
export const sanitizeText = (text: string, maxLength: number = 255): ValidationResult => {
  const errors: string[] = []
  
  if (!text) {
    return { isValid: true, errors: [], sanitizedValue: '' }
  }

  // Remove HTML tags and dangerous characters
  let sanitized = text
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/<[^>]*>/g, '') // Remove all HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim()

  if (sanitized.length > maxLength) {
    errors.push(`Text is too long (max ${maxLength} characters)`)
    sanitized = sanitized.substring(0, maxLength)
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitized
  }
}

// Validate and sanitize numeric input
export const validateNumber = (
  value: string | number, 
  min?: number, 
  max?: number, 
  allowDecimals: boolean = true
): ValidationResult => {
  const errors: string[] = []
  
  if (value === '' || value === null || value === undefined) {
    errors.push('Number is required')
    return { isValid: false, errors }
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value

  if (isNaN(numValue)) {
    errors.push('Invalid number format')
    return { isValid: false, errors }
  }

  if (!allowDecimals && numValue % 1 !== 0) {
    errors.push('Decimal numbers are not allowed')
  }

  if (min !== undefined && numValue < min) {
    errors.push(`Number must be at least ${min}`)
  }

  if (max !== undefined && numValue > max) {
    errors.push(`Number must be at most ${max}`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: allowDecimals ? numValue : Math.floor(numValue)
  }
}

// Validate Sales ID format
export const validateSalesId = (salesId: string): ValidationResult => {
  const errors: string[] = []
  
  if (!salesId || salesId.trim().length === 0) {
    errors.push('Sales ID is required')
    return { isValid: false, errors }
  }

  const sanitized = salesId.trim().toUpperCase()
  
  // Sales ID format: 3-4 letters followed by 3-4 numbers (e.g., EMP001, CASH123)
  const salesIdRegex = /^[A-Z]{3,4}\d{3,4}$/
  
  if (!salesIdRegex.test(sanitized)) {
    errors.push('Sales ID must be 3-4 letters followed by 3-4 numbers (e.g., EMP001)')
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitized
  }
}

// Validate table number
export const validateTableNumber = (tableNumber: string): ValidationResult => {
  const errors: string[] = []
  
  if (!tableNumber || tableNumber.trim().length === 0) {
    errors.push('Table number is required for dine-in orders')
    return { isValid: false, errors }
  }

  const sanitized = tableNumber.trim().toUpperCase()
  
  // Table number can be alphanumeric, max 10 characters
  if (sanitized.length > 10) {
    errors.push('Table number is too long (max 10 characters)')
  }

  if (!/^[A-Z0-9]+$/.test(sanitized)) {
    errors.push('Table number can only contain letters and numbers')
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitized
  }
}

// Comprehensive form validation
export const validateForm = (data: Record<string, any>, rules: Record<string, any>): ValidationResult => {
  const errors: string[] = []
  const sanitizedData: Record<string, any> = {}

  for (const [field, value] of Object.entries(data)) {
    const rule = rules[field]
    if (!rule) continue

    let result: ValidationResult

    switch (rule.type) {
      case 'email':
        result = validateEmail(value)
        break
      case 'phone':
        result = validatePhoneNumber(value)
        break
      case 'password':
        result = validatePassword(value)
        break
      case 'text':
        result = sanitizeText(value, rule.maxLength)
        break
      case 'number':
        result = validateNumber(value, rule.min, rule.max, rule.allowDecimals)
        break
      case 'salesId':
        result = validateSalesId(value)
        break
      case 'tableNumber':
        result = validateTableNumber(value)
        break
      default:
        result = { isValid: true, errors: [], sanitizedValue: value }
    }

    if (!result.isValid) {
      errors.push(...result.errors.map(error => `${field}: ${error}`))
    } else {
      sanitizedData[field] = result.sanitizedValue
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitizedData
  }
}
