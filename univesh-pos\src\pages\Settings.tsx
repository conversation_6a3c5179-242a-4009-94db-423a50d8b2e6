import React, { useState, useEffect, useCallback } from 'react'
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  Tabs,
  Tab,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material'
import {
  Person,
  Business,
  Security,
  Notifications,
  Edit,
  Save,
  Cancel
} from '@mui/icons-material'
import { colors } from '../theme'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'

interface UserSettings {
  receive_order_updates: boolean
  receive_sales_alerts: boolean
  low_stock_alerts_enabled: boolean
}

interface SystemSettings {
  default_tax_rate: number
  currency_symbol: string
  auto_print_receipts: boolean
}

interface ProfileData {
  full_name: string
  email: string
  phone_number: string | null
  date_of_birth: string | null
  designation: string | null
}

const Settings: React.FC = () => {
  const { user, profile, updateProfile } = useAuth()
  const [activeTab, setActiveTab] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // Profile settings
  const [profileData, setProfileData] = useState<ProfileData>({
    full_name: '',
    email: '',
    phone_number: '',
    date_of_birth: '',
    designation: ''
  })
  const [profileDialogOpen, setProfileDialogOpen] = useState(false)

  // User settings
  const [userSettings, setUserSettings] = useState<UserSettings>({
    receive_order_updates: true,
    receive_sales_alerts: true,
    low_stock_alerts_enabled: false
  })

  // System settings (admin only)
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    default_tax_rate: 0.05,
    currency_symbol: '$',
    auto_print_receipts: true
  })

  // Password change
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false)

  useEffect(() => {
    if (profile) {
      setProfileData({
        full_name: profile.full_name,
        email: profile.email,
        phone_number: profile.phone_number || '',
        date_of_birth: profile.date_of_birth || '',
        designation: profile.designation || ''
      })
    }
    fetchSettings()
  }, [profile, fetchSettings])

  const fetchSettings = useCallback(async () => {
    try {
      if (!user) return

      // Fetch user settings
      const { data: userSettingsData, error: userError } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (userError && userError.code !== 'PGRST116') {
        console.error('Error fetching user settings:', userError)
      } else if (userSettingsData) {
        setUserSettings({
          receive_order_updates: userSettingsData.receive_order_updates,
          receive_sales_alerts: userSettingsData.receive_sales_alerts,
          low_stock_alerts_enabled: userSettingsData.low_stock_alerts_enabled
        })
      }

      // Fetch system settings (if admin/manager)
      if (profile?.role?.role_name === 'admin' || profile?.role?.role_name === 'manager') {
        const { data: systemSettingsData, error: systemError } = await supabase
          .from('system_settings')
          .select('*')
          .single()

        if (systemError) {
          console.error('Error fetching system settings:', systemError)
        } else if (systemSettingsData) {
          setSystemSettings({
            default_tax_rate: Number(systemSettingsData.default_tax_rate),
            currency_symbol: systemSettingsData.currency_symbol,
            auto_print_receipts: systemSettingsData.auto_print_receipts
          })
        }
      }
    } catch (error) {
      console.error('Error fetching settings:', error)
    }
  }, [user, profile?.role?.role_name])

  const saveUserSettings = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!user) return

      const { error } = await supabase
        .from('user_settings')
        .upsert({
          user_id: user.id,
          receive_order_updates: userSettings.receive_order_updates,
          receive_sales_alerts: userSettings.receive_sales_alerts,
          low_stock_alerts_enabled: userSettings.low_stock_alerts_enabled,
          updated_at: new Date().toISOString()
        })

      if (error) throw error

      setSuccess('User settings saved successfully')
      setTimeout(() => setSuccess(null), 3000)
    } catch (error) {
      console.error('Error saving user settings:', error)
      setError(error instanceof Error ? error.message : 'Failed to save user settings')
    } finally {
      setLoading(false)
    }
  }

  const saveSystemSettings = async () => {
    try {
      setLoading(true)
      setError(null)

      const { error } = await supabase
        .from('system_settings')
        .update({
          default_tax_rate: systemSettings.default_tax_rate,
          currency_symbol: systemSettings.currency_symbol,
          auto_print_receipts: systemSettings.auto_print_receipts,
          updated_at: new Date().toISOString()
        })
        .eq('id', '00000000-0000-0000-0000-000000000001')

      if (error) throw error

      setSuccess('System settings saved successfully')
      setTimeout(() => setSuccess(null), 3000)
    } catch (error) {
      console.error('Error saving system settings:', error)
      setError(error instanceof Error ? error.message : 'Failed to save system settings')
    } finally {
      setLoading(false)
    }
  }

  const saveProfile = async () => {
    try {
      setLoading(true)
      setError(null)

      const { error } = await updateProfile({
        full_name: profileData.full_name,
        phone_number: profileData.phone_number || null,
        date_of_birth: profileData.date_of_birth || null,
        designation: profileData.designation || null
      })

      if (error) throw error

      setSuccess('Profile updated successfully')
      setProfileDialogOpen(false)
      setTimeout(() => setSuccess(null), 3000)
    } catch (error) {
      console.error('Error updating profile:', error)
      setError(error instanceof Error ? error.message : 'Failed to update profile')
    } finally {
      setLoading(false)
    }
  }

  const changePassword = async () => {
    try {
      setLoading(true)
      setError(null)

      if (passwordData.newPassword !== passwordData.confirmPassword) {
        setError('New passwords do not match')
        return
      }

      if (passwordData.newPassword.length < 6) {
        setError('Password must be at least 6 characters long')
        return
      }

      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      })

      if (error) throw error

      setSuccess('Password changed successfully')
      setPasswordDialogOpen(false)
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
      setTimeout(() => setSuccess(null), 3000)
    } catch (error) {
      console.error('Error changing password:', error)
      setError(error instanceof Error ? error.message : 'Failed to change password')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
        Settings
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Card>
        <CardContent>
          <Tabs value={activeTab} onChange={(_, value) => setActiveTab(value)} sx={{ mb: 3 }}>
            <Tab label="Profile" icon={<Person />} iconPosition="start" />
            <Tab label="Notifications" icon={<Notifications />} iconPosition="start" />
            <Tab label="Security" icon={<Security />} iconPosition="start" />
            {(profile?.role?.role_name === 'admin' || profile?.role?.role_name === 'manager') && (
              <Tab label="System" icon={<Business />} iconPosition="start" />
            )}
          </Tabs>

          {/* Profile Tab */}
          {activeTab === 0 && (
            <Box>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Avatar
                        sx={{
                          width: 100,
                          height: 100,
                          mx: 'auto',
                          mb: 2,
                          bgcolor: colors.primary.main,
                          fontSize: '2rem'
                        }}
                      >
                        {profile?.full_name?.charAt(0)}
                      </Avatar>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                        {profile?.full_name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {profile?.role?.role_name?.toUpperCase()}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        ID: {profile?.sales_id_number}
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<Edit />}
                        onClick={() => setProfileDialogOpen(true)}
                      >
                        Edit Profile
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={8}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 3 }}>Profile Information</Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Full Name"
                            value={profile?.full_name || ''}
                            disabled
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Email"
                            value={profile?.email || ''}
                            disabled
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Phone Number"
                            value={profile?.phone_number || 'Not provided'}
                            disabled
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Date of Birth"
                            value={profile?.date_of_birth ? new Date(profile.date_of_birth).toLocaleDateString() : 'Not provided'}
                            disabled
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            label="Designation"
                            value={profile?.designation || 'Not provided'}
                            disabled
                          />
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Notifications Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>Notification Preferences</Typography>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={userSettings.receive_order_updates}
                            onChange={(e) => setUserSettings(prev => ({ ...prev, receive_order_updates: e.target.checked }))}
                          />
                        }
                        label="Receive Order Updates"
                        sx={{ mb: 2 }}
                      />
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Get notified when orders are placed, updated, or completed
                      </Typography>

                      <FormControlLabel
                        control={
                          <Switch
                            checked={userSettings.receive_sales_alerts}
                            onChange={(e) => setUserSettings(prev => ({ ...prev, receive_sales_alerts: e.target.checked }))}
                          />
                        }
                        label="Receive Sales Alerts"
                        sx={{ mb: 2 }}
                      />
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Get notified about daily sales summaries and targets
                      </Typography>

                      <FormControlLabel
                        control={
                          <Switch
                            checked={userSettings.low_stock_alerts_enabled}
                            onChange={(e) => setUserSettings(prev => ({ ...prev, low_stock_alerts_enabled: e.target.checked }))}
                          />
                        }
                        label="Low Stock Alerts"
                        sx={{ mb: 2 }}
                      />
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Get notified when products are running low on stock
                      </Typography>

                      <Button
                        variant="contained"
                        onClick={saveUserSettings}
                        disabled={loading}
                        startIcon={<Save />}
                      >
                        Save Notification Settings
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Security Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>Security Settings</Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>Password</Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Change your account password to keep your account secure
                      </Typography>
                      <Button
                        variant="outlined"
                        onClick={() => setPasswordDialogOpen(true)}
                        startIcon={<Security />}
                      >
                        Change Password
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>Account Information</Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Account created: {profile ? new Date(profile.created_at).toLocaleDateString() : 'Unknown'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Last updated: {profile ? new Date(profile.updated_at).toLocaleDateString() : 'Unknown'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Role: {profile?.role?.role_name?.toUpperCase()}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* System Tab (Admin/Manager only) */}
          {activeTab === 3 && (profile?.role?.role_name === 'admin' || profile?.role?.role_name === 'manager') && (
            <Box>
              <Typography variant="h6" sx={{ mb: 3 }}>System Configuration</Typography>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 3 }}>General Settings</Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={4}>
                          <TextField
                            fullWidth
                            label="Default Tax Rate (%)"
                            type="number"
                            value={systemSettings.default_tax_rate * 100}
                            onChange={(e) => setSystemSettings(prev => ({
                              ...prev,
                              default_tax_rate: parseFloat(e.target.value) / 100
                            }))}
                            inputProps={{ min: 0, max: 100, step: 0.1 }}
                          />
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <FormControl fullWidth>
                            <InputLabel>Currency Symbol</InputLabel>
                            <Select
                              value={systemSettings.currency_symbol}
                              label="Currency Symbol"
                              onChange={(e) => setSystemSettings(prev => ({
                                ...prev,
                                currency_symbol: e.target.value
                              }))}
                            >
                              <MenuItem value="$">$ (USD)</MenuItem>
                              <MenuItem value="€">€ (EUR)</MenuItem>
                              <MenuItem value="£">£ (GBP)</MenuItem>
                              <MenuItem value="¥">¥ (JPY)</MenuItem>
                              <MenuItem value="₹">₹ (INR)</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={systemSettings.auto_print_receipts}
                                onChange={(e) => setSystemSettings(prev => ({
                                  ...prev,
                                  auto_print_receipts: e.target.checked
                                }))}
                              />
                            }
                            label="Auto Print Receipts"
                          />
                        </Grid>
                      </Grid>
                      <Divider sx={{ my: 3 }} />
                      <Button
                        variant="contained"
                        onClick={saveSystemSettings}
                        disabled={loading}
                        startIcon={<Save />}
                      >
                        Save System Settings
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Profile Edit Dialog */}
      <Dialog open={profileDialogOpen} onClose={() => setProfileDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Profile</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Full Name"
                value={profileData.full_name}
                onChange={(e) => setProfileData(prev => ({ ...prev, full_name: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Phone Number"
                value={profileData.phone_number}
                onChange={(e) => setProfileData(prev => ({ ...prev, phone_number: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Date of Birth"
                type="date"
                value={profileData.date_of_birth}
                onChange={(e) => setProfileData(prev => ({ ...prev, date_of_birth: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Designation"
                value={profileData.designation}
                onChange={(e) => setProfileData(prev => ({ ...prev, designation: e.target.value }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setProfileDialogOpen(false)} startIcon={<Cancel />}>
            Cancel
          </Button>
          <Button
            onClick={saveProfile}
            variant="contained"
            disabled={loading || !profileData.full_name}
            startIcon={<Save />}
          >
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Password Change Dialog */}
      <Dialog open={passwordDialogOpen} onClose={() => setPasswordDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Change Password</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Current Password"
                type="password"
                value={passwordData.currentPassword}
                onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="New Password"
                type="password"
                value={passwordData.newPassword}
                onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Confirm New Password"
                type="password"
                value={passwordData.confirmPassword}
                onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                required
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPasswordDialogOpen(false)} startIcon={<Cancel />}>
            Cancel
          </Button>
          <Button
            onClick={changePassword}
            variant="contained"
            disabled={loading || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
            startIcon={<Save />}
          >
            Change Password
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default Settings
