-- Update system_settings table to include new tax and delivery fee fields
-- Run this in Supabase SQL Editor

-- Add new columns to system_settings table
ALTER TABLE public.system_settings 
ADD COLUMN IF NOT EXISTS dine_in_tax_rate NUMERIC(5, 4) DEFAULT 0.10,
ADD COLUMN IF NOT EXISTS delivery_fee NUMERIC(8, 2) DEFAULT 1.00;

-- Update the existing row with default values
UPDATE public.system_settings 
SET 
  dine_in_tax_rate = 0.10,
  delivery_fee = 1.00,
  updated_at = NOW()
WHERE id = '00000000-0000-0000-0000-000000000001';

-- Verify the update
SELECT * FROM public.system_settings;
