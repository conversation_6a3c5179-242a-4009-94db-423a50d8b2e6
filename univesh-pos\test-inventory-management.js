/**
 * Inventory Management Test
 * 
 * This script tests the complete inventory management functionality:
 * 1. Category management (CRUD operations)
 * 2. Product management (CRUD operations)
 * 3. Stock tracking and updates
 * 4. Inventory analytics
 * 5. Low stock alerts
 */

import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMDMzMzIsImV4cCI6MjA2NTU3OTMzMn0.HdsERV6b0jeAviW307nNta4ME2qeSeBrtolzMKZ69qU';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testInventoryManagement() {
  console.log('🧪 Testing Inventory Management System...\n');
  
  try {
    // Authenticate
    console.log('🔐 Step 1: Authenticate...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (authError) {
      console.error('❌ Authentication failed:', authError);
      return false;
    }
    
    console.log('✅ Authenticated successfully');
    
    // Test category management
    console.log('\n📂 Step 2: Test category management...');
    
    // Create test category
    const testCategoryData = {
      name: 'Test Category',
      description: 'A test category for inventory testing'
    };
    
    const { data: newCategory, error: categoryError } = await supabase
      .from('categories')
      .insert(testCategoryData)
      .select()
      .single();
    
    if (categoryError) {
      console.error('❌ Error creating category:', categoryError);
      return false;
    }
    
    console.log(`✅ Category created: ${newCategory.name} (${newCategory.id})`);
    
    // Update category
    const updatedCategoryData = {
      name: 'Updated Test Category',
      description: 'Updated description for testing',
      updated_at: new Date().toISOString()
    };
    
    const { error: updateCategoryError } = await supabase
      .from('categories')
      .update(updatedCategoryData)
      .eq('id', newCategory.id);
    
    if (updateCategoryError) {
      console.error('❌ Error updating category:', updateCategoryError);
    } else {
      console.log('✅ Category updated successfully');
    }
    
    // Test product management
    console.log('\n🍽️ Step 3: Test product management...');
    
    // Create test products
    const testProducts = [
      {
        name: 'Test Product 1',
        description: 'First test product',
        price: 15.99,
        current_stock: 50,
        category_id: newCategory.id,
        is_active: true
      },
      {
        name: 'Test Product 2',
        description: 'Second test product',
        price: 8.50,
        current_stock: 25,
        category_id: newCategory.id,
        is_active: true
      },
      {
        name: 'Test Product 3',
        description: 'Third test product with low stock',
        price: 12.00,
        current_stock: 3, // Low stock for testing
        category_id: newCategory.id,
        is_active: true
      }
    ];
    
    const createdProducts = [];
    
    for (const productData of testProducts) {
      const { data: newProduct, error: productError } = await supabase
        .from('products')
        .insert(productData)
        .select()
        .single();
      
      if (productError) {
        console.error('❌ Error creating product:', productError);
        continue;
      }
      
      createdProducts.push(newProduct);
      console.log(`✅ Product created: ${newProduct.name} - $${newProduct.price} (Stock: ${newProduct.current_stock})`);
    }
    
    // Test product updates
    console.log('\n📝 Step 4: Test product updates...');
    
    if (createdProducts.length > 0) {
      const productToUpdate = createdProducts[0];
      const updatedProductData = {
        name: 'Updated Test Product 1',
        price: 18.99,
        current_stock: 75,
        updated_at: new Date().toISOString()
      };
      
      const { error: updateProductError } = await supabase
        .from('products')
        .update(updatedProductData)
        .eq('id', productToUpdate.id);
      
      if (updateProductError) {
        console.error('❌ Error updating product:', updateProductError);
      } else {
        console.log('✅ Product updated successfully');
      }
    }
    
    // Test inventory queries
    console.log('\n📊 Step 5: Test inventory queries...');
    
    // Get all products with categories
    const { data: allProducts, error: productsError } = await supabase
      .from('products')
      .select(`
        *,
        categories(id, name)
      `)
      .order('name');
    
    if (productsError) {
      console.error('❌ Error fetching products:', productsError);
    } else {
      console.log(`✅ Products query successful: ${allProducts?.length || 0} products found`);
      
      const testProductsFound = allProducts?.filter(p => 
        createdProducts.some(cp => cp.id === p.id)
      ) || [];
      
      console.log(`   Test products found: ${testProductsFound.length}`);
      testProductsFound.forEach(product => {
        console.log(`   - ${product.name}: $${product.price} (Stock: ${product.current_stock})`);
      });
    }
    
    // Test low stock detection
    console.log('\n⚠️ Step 6: Test low stock detection...');
    
    const { data: lowStockProducts, error: lowStockError } = await supabase
      .from('products')
      .select('*')
      .eq('is_active', true)
      .lt('current_stock', 10)
      .order('current_stock', { ascending: true });
    
    if (lowStockError) {
      console.error('❌ Error fetching low stock products:', lowStockError);
    } else {
      console.log(`✅ Low stock query successful: ${lowStockProducts?.length || 0} products with low stock`);
      
      const testLowStockProducts = lowStockProducts?.filter(p => 
        createdProducts.some(cp => cp.id === p.id)
      ) || [];
      
      if (testLowStockProducts.length > 0) {
        console.log('   Test products with low stock:');
        testLowStockProducts.forEach(product => {
          console.log(`   ⚠️  ${product.name}: ${product.current_stock} remaining`);
        });
      }
    }
    
    // Test stock updates (simulating sales)
    console.log('\n📦 Step 7: Test stock updates...');
    
    if (createdProducts.length > 0) {
      const productToUpdate = createdProducts[1];
      const stockReduction = 5;
      
      // Simulate stock reduction after sale
      const { data: updatedProduct, error: stockUpdateError } = await supabase
        .from('products')
        .update({
          current_stock: productToUpdate.current_stock - stockReduction,
          updated_at: new Date().toISOString()
        })
        .eq('id', productToUpdate.id)
        .select()
        .single();
      
      if (stockUpdateError) {
        console.error('❌ Error updating stock:', stockUpdateError);
      } else {
        console.log(`✅ Stock updated: ${productToUpdate.name}`);
        console.log(`   Previous stock: ${productToUpdate.current_stock}`);
        console.log(`   New stock: ${updatedProduct.current_stock}`);
        console.log(`   Reduction: ${stockReduction} units`);
      }
    }
    
    // Test inventory analytics
    console.log('\n📈 Step 8: Test inventory analytics...');
    
    // Total inventory value
    const { data: inventoryValue, error: valueError } = await supabase
      .from('products')
      .select('price, current_stock')
      .eq('is_active', true);
    
    if (valueError) {
      console.error('❌ Error calculating inventory value:', valueError);
    } else {
      const totalValue = inventoryValue?.reduce((sum, product) => 
        sum + (product.price * product.current_stock), 0
      ) || 0;
      
      console.log(`✅ Inventory analytics:`);
      console.log(`   Total products: ${inventoryValue?.length || 0}`);
      console.log(`   Total inventory value: $${totalValue.toFixed(2)}`);
    }
    
    // Category distribution
    const { data: categoryStats, error: categoryStatsError } = await supabase
      .from('products')
      .select(`
        category_id,
        categories(name)
      `)
      .eq('is_active', true);
    
    if (categoryStatsError) {
      console.error('❌ Error fetching category stats:', categoryStatsError);
    } else {
      const categoryDistribution = categoryStats?.reduce((acc, product) => {
        const categoryName = product.categories?.name || 'Uncategorized';
        acc[categoryName] = (acc[categoryName] || 0) + 1;
        return acc;
      }, {}) || {};
      
      console.log('   Category distribution:');
      Object.entries(categoryDistribution).forEach(([category, count]) => {
        console.log(`     ${category}: ${count} products`);
      });
    }
    
    // Test product deactivation
    console.log('\n🔄 Step 9: Test product deactivation...');
    
    if (createdProducts.length > 0) {
      const productToDeactivate = createdProducts[2];
      
      const { error: deactivateError } = await supabase
        .from('products')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', productToDeactivate.id);
      
      if (deactivateError) {
        console.error('❌ Error deactivating product:', deactivateError);
      } else {
        console.log(`✅ Product deactivated: ${productToDeactivate.name}`);
      }
    }
    
    // Cleanup test data
    console.log('\n🧹 Step 10: Cleanup test data...');
    
    // Delete test products
    for (const product of createdProducts) {
      await supabase.from('products').delete().eq('id', product.id);
    }
    
    // Delete test category
    await supabase.from('categories').delete().eq('id', newCategory.id);
    
    console.log('✅ Test data cleaned up');
    
    return true;
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    return false;
  } finally {
    await supabase.auth.signOut();
    console.log('\n👋 Signed out');
  }
}

// Run the test
testInventoryManagement().then(success => {
  if (success) {
    console.log('\n🎉 Inventory Management Test PASSED!');
    console.log('\n✅ Summary:');
    console.log('   • Category CRUD operations working');
    console.log('   • Product CRUD operations working');
    console.log('   • Stock tracking and updates working');
    console.log('   • Low stock detection working');
    console.log('   • Inventory analytics working');
    console.log('   • Product activation/deactivation working');
    console.log('   • Category-product relationships working');
    console.log('   • Complete inventory management functional');
  } else {
    console.log('\n❌ Inventory Management Test FAILED!');
  }
}).catch(error => {
  console.error('\n💥 Test execution failed:', error);
});
