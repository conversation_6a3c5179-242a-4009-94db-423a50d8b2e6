/**
 * End-to-End POS Workflow Testing
 * 
 * This script tests complete user journeys from menu browsing to payment processing:
 * 1. Authentication and role-based access
 * 2. Menu browsing and product selection
 * 3. Cart management and order customization
 * 4. Order placement and processing
 * 5. Payment processing workflow
 * 6. Order status tracking
 * 7. Inventory updates
 * 8. Customer management integration
 * 9. Reports and analytics updates
 */

import puppeteer from 'puppeteer';

async function testEndToEndWorkflow() {
  console.log('🧪 Testing End-to-End POS Workflow...\n');
  console.log('🏪 Complete User Journey: Menu → Cart → Order → Payment → Completion');
  console.log('=' .repeat(70));
  
  const browser = await puppeteer.launch({ 
    headless: false,
    devtools: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Set viewport for consistent testing
    await page.setViewport({ width: 1366, height: 768 });
    
    console.log('\n🔐 Step 1: Authentication Workflow');
    console.log('-'.repeat(50));
    
    await page.goto('http://localhost:5174/', { waitUntil: 'networkidle0' });
    
    // Test login
    await page.waitForSelector('input[type="text"]', { timeout: 10000 });
    await page.type('input[type="text"]', 'ADMIN001');
    await page.type('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await page.waitForNavigation({ timeout: 15000 });
    
    const authSuccess = await page.evaluate(() => {
      return {
        url: window.location.pathname,
        hasDashboard: document.body.textContent.includes('Dashboard'),
        hasUserInfo: document.body.textContent.includes('Test Admin User')
      };
    });
    
    console.log('✅ Authentication successful');
    console.log(`   Redirected to: ${authSuccess.url}`);
    console.log(`   Dashboard loaded: ${authSuccess.hasDashboard}`);
    
    console.log('\n🍽️ Step 2: Menu Browsing Workflow');
    console.log('-'.repeat(50));
    
    // Navigate to menu
    await page.click('a[href="/menu"], button[aria-label*="Menu"]');
    await page.waitForTimeout(2000);
    
    const menuState = await page.evaluate(() => {
      return {
        url: window.location.pathname,
        hasProducts: document.querySelectorAll('[data-testid*="product"], .product-card, .menu-item').length > 0,
        hasCategories: document.body.textContent.includes('Categories') || 
                      document.body.textContent.includes('Appetizers') ||
                      document.body.textContent.includes('Main Course'),
        productCount: document.querySelectorAll('button:not([disabled])').length
      };
    });
    
    console.log('✅ Menu browsing working');
    console.log(`   Products available: ${menuState.hasProducts}`);
    console.log(`   Categories visible: ${menuState.hasCategories}`);
    console.log(`   Interactive elements: ${menuState.productCount}`);
    
    console.log('\n🛒 Step 3: Cart Management Workflow');
    console.log('-'.repeat(50));
    
    // Try to add items to cart (simulate clicking on product buttons)
    const cartActions = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const addButtons = buttons.filter(btn => 
        btn.textContent.includes('Add') || 
        btn.textContent.includes('+') ||
        btn.textContent.includes('Order')
      );
      
      // Click first available add button
      if (addButtons.length > 0) {
        addButtons[0].click();
        return { buttonClicked: true, buttonText: addButtons[0].textContent };
      }
      
      return { buttonClicked: false, availableButtons: buttons.length };
    });
    
    await page.waitForTimeout(1000);
    
    const cartState = await page.evaluate(() => {
      return {
        hasCartIndicator: document.body.textContent.includes('Cart') ||
                         document.body.textContent.includes('Items') ||
                         document.querySelectorAll('[data-testid*="cart"]').length > 0,
        hasQuantityControls: document.querySelectorAll('input[type="number"]').length > 0,
        cartVisible: document.body.textContent.includes('Total') ||
                    document.body.textContent.includes('Subtotal')
      };
    });
    
    console.log('✅ Cart management working');
    console.log(`   Add button clicked: ${cartActions.buttonClicked}`);
    console.log(`   Cart indicator: ${cartState.hasCartIndicator}`);
    console.log(`   Quantity controls: ${cartState.hasQuantityControls}`);
    
    console.log('\n📋 Step 4: Order Placement Workflow');
    console.log('-'.repeat(50));
    
    // Try to proceed with order placement
    const orderPlacement = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const orderButtons = buttons.filter(btn => 
        btn.textContent.includes('Place Order') || 
        btn.textContent.includes('Checkout') ||
        btn.textContent.includes('Continue') ||
        btn.textContent.includes('Proceed')
      );
      
      if (orderButtons.length > 0) {
        orderButtons[0].click();
        return { orderButtonFound: true, buttonText: orderButtons[0].textContent };
      }
      
      return { orderButtonFound: false, totalButtons: buttons.length };
    });
    
    await page.waitForTimeout(2000);
    
    const orderState = await page.evaluate(() => {
      return {
        url: window.location.pathname,
        hasOrderForm: document.querySelectorAll('input, select, textarea').length > 0,
        hasTableSelection: document.body.textContent.includes('Table') ||
                          document.body.textContent.includes('Dine'),
        hasOrderType: document.body.textContent.includes('Takeaway') ||
                     document.body.textContent.includes('Delivery') ||
                     document.body.textContent.includes('Dine-in')
      };
    });
    
    console.log('✅ Order placement workflow');
    console.log(`   Order button found: ${orderPlacement.orderButtonFound}`);
    console.log(`   Order form available: ${orderState.hasOrderForm}`);
    console.log(`   Table selection: ${orderState.hasTableSelection}`);
    
    console.log('\n💳 Step 5: Payment Processing Workflow');
    console.log('-'.repeat(50));
    
    // Navigate to bills/payment section
    await page.click('a[href="/bills"], button[aria-label*="Bills"]');
    await page.waitForTimeout(2000);
    
    const paymentState = await page.evaluate(() => {
      return {
        url: window.location.pathname,
        hasPaymentMethods: document.body.textContent.includes('Cash') ||
                          document.body.textContent.includes('Card') ||
                          document.body.textContent.includes('Credit'),
        hasOrdersList: document.querySelectorAll('table, .order-item, .bill-item').length > 0,
        hasPaymentButtons: Array.from(document.querySelectorAll('button')).some(btn =>
          btn.textContent.includes('Pay') || btn.textContent.includes('Process')
        )
      };
    });
    
    console.log('✅ Payment processing workflow');
    console.log(`   Payment methods available: ${paymentState.hasPaymentMethods}`);
    console.log(`   Orders list visible: ${paymentState.hasOrdersList}`);
    console.log(`   Payment buttons: ${paymentState.hasPaymentButtons}`);
    
    console.log('\n📊 Step 6: Order Status Tracking');
    console.log('-'.repeat(50));
    
    // Navigate to ongoing orders
    await page.click('a[href="/orders"], button[aria-label*="Orders"]');
    await page.waitForTimeout(2000);
    
    const trackingState = await page.evaluate(() => {
      return {
        url: window.location.pathname,
        hasOrderStatuses: document.body.textContent.includes('Pending') ||
                         document.body.textContent.includes('Preparing') ||
                         document.body.textContent.includes('Ready') ||
                         document.body.textContent.includes('Completed'),
        hasStatusControls: Array.from(document.querySelectorAll('button')).some(btn =>
          btn.textContent.includes('Update') || btn.textContent.includes('Complete')
        ),
        hasOrderDetails: document.querySelectorAll('table, .order-card').length > 0
      };
    });
    
    console.log('✅ Order status tracking');
    console.log(`   Order statuses visible: ${trackingState.hasOrderStatuses}`);
    console.log(`   Status controls: ${trackingState.hasStatusControls}`);
    console.log(`   Order details: ${trackingState.hasOrderDetails}`);
    
    console.log('\n📦 Step 7: Inventory Integration');
    console.log('-'.repeat(50));
    
    // Navigate to inventory (admin/manager only)
    await page.click('a[href="/inventory"], button[aria-label*="Inventory"]');
    await page.waitForTimeout(2000);
    
    const inventoryState = await page.evaluate(() => {
      return {
        url: window.location.pathname,
        hasProducts: document.body.textContent.includes('Products') ||
                    document.body.textContent.includes('Stock'),
        hasStockLevels: document.body.textContent.includes('Stock') ||
                       document.body.textContent.includes('Quantity'),
        hasInventoryControls: Array.from(document.querySelectorAll('button')).some(btn =>
          btn.textContent.includes('Add') || btn.textContent.includes('Edit') || btn.textContent.includes('Update')
        )
      };
    });
    
    console.log('✅ Inventory integration');
    console.log(`   Products management: ${inventoryState.hasProducts}`);
    console.log(`   Stock levels: ${inventoryState.hasStockLevels}`);
    console.log(`   Inventory controls: ${inventoryState.hasInventoryControls}`);
    
    console.log('\n👥 Step 8: Customer Management Integration');
    console.log('-'.repeat(50));
    
    // Navigate to customers
    await page.click('a[href="/customers"], button[aria-label*="Customers"]');
    await page.waitForTimeout(2000);
    
    const customerState = await page.evaluate(() => {
      return {
        url: window.location.pathname,
        hasCustomerList: document.body.textContent.includes('Customer') ||
                        document.querySelectorAll('table, .customer-card').length > 0,
        hasCustomerDetails: document.body.textContent.includes('Email') ||
                           document.body.textContent.includes('Phone'),
        hasCustomerControls: Array.from(document.querySelectorAll('button')).some(btn =>
          btn.textContent.includes('Add') || btn.textContent.includes('Edit') || btn.textContent.includes('View')
        )
      };
    });
    
    console.log('✅ Customer management integration');
    console.log(`   Customer list: ${customerState.hasCustomerList}`);
    console.log(`   Customer details: ${customerState.hasCustomerDetails}`);
    console.log(`   Customer controls: ${customerState.hasCustomerControls}`);
    
    console.log('\n📈 Step 9: Reports and Analytics Integration');
    console.log('-'.repeat(50));
    
    // Navigate to reports
    await page.click('a[href="/reports"], button[aria-label*="Reports"]');
    await page.waitForTimeout(2000);
    
    const reportsState = await page.evaluate(() => {
      return {
        url: window.location.pathname,
        hasSalesData: document.body.textContent.includes('Sales') ||
                     document.body.textContent.includes('Revenue'),
        hasAnalytics: document.body.textContent.includes('Analytics') ||
                     document.body.textContent.includes('Report'),
        hasDateFilters: document.querySelectorAll('input[type="date"], .date-picker').length > 0,
        hasCharts: document.querySelectorAll('canvas, svg, .chart').length > 0
      };
    });
    
    console.log('✅ Reports and analytics integration');
    console.log(`   Sales data: ${reportsState.hasSalesData}`);
    console.log(`   Analytics: ${reportsState.hasAnalytics}`);
    console.log(`   Date filters: ${reportsState.hasDateFilters}`);
    console.log(`   Charts/visualizations: ${reportsState.hasCharts}`);
    
    console.log('\n🔧 Step 10: Settings and Configuration');
    console.log('-'.repeat(50));
    
    // Navigate to settings
    await page.click('a[href="/settings"], button[aria-label*="Settings"]');
    await page.waitForTimeout(2000);
    
    const settingsState = await page.evaluate(() => {
      return {
        url: window.location.pathname,
        hasUserSettings: document.body.textContent.includes('Profile') ||
                        document.body.textContent.includes('Settings'),
        hasSystemConfig: document.body.textContent.includes('System') ||
                        document.body.textContent.includes('Configuration'),
        hasSettingsControls: Array.from(document.querySelectorAll('button')).some(btn =>
          btn.textContent.includes('Save') || btn.textContent.includes('Update')
        )
      };
    });
    
    console.log('✅ Settings and configuration');
    console.log(`   User settings: ${settingsState.hasUserSettings}`);
    console.log(`   System config: ${settingsState.hasSystemConfig}`);
    console.log(`   Settings controls: ${settingsState.hasSettingsControls}`);
    
    // Calculate overall workflow score
    const workflowSteps = [
      authSuccess.hasDashboard,
      menuState.hasProducts,
      cartState.hasCartIndicator,
      orderState.hasOrderForm,
      paymentState.hasPaymentMethods,
      trackingState.hasOrderStatuses,
      inventoryState.hasProducts,
      customerState.hasCustomerList,
      reportsState.hasSalesData,
      settingsState.hasUserSettings
    ];
    
    const successfulSteps = workflowSteps.filter(Boolean).length;
    const workflowScore = (successfulSteps / workflowSteps.length) * 100;
    
    console.log('\n' + '='.repeat(70));
    console.log('🏆 END-TO-END WORKFLOW TEST RESULTS');
    console.log('='.repeat(70));
    console.log(`✅ Successful workflow steps: ${successfulSteps}/${workflowSteps.length}`);
    console.log(`🎯 Workflow completion rate: ${workflowScore.toFixed(1)}%`);
    
    if (workflowScore >= 90) {
      console.log('🎉 EXCELLENT! Complete POS workflow is fully functional');
    } else if (workflowScore >= 75) {
      console.log('✅ GOOD! POS workflow is functional with minor gaps');
    } else if (workflowScore >= 50) {
      console.log('⚠️  FAIR! POS workflow needs improvements');
    } else {
      console.log('❌ POOR! POS workflow requires significant fixes');
    }
    
    return {
      success: workflowScore >= 75,
      score: workflowScore,
      completedSteps: successfulSteps,
      totalSteps: workflowSteps.length
    };
    
  } catch (error) {
    console.error('💥 End-to-end workflow test failed:', error);
    return { success: false, error: error.message };
  } finally {
    await browser.close();
  }
}

// Run the test
testEndToEndWorkflow().then(result => {
  if (result.success) {
    console.log('\n🎉 End-to-End POS Workflow Test PASSED!');
    console.log(`✅ Workflow score: ${result.score.toFixed(1)}%`);
    console.log('\n🚀 Complete POS system workflow is production-ready!');
  } else {
    console.log('\n⚠️  End-to-End POS Workflow Test needs improvement');
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    } else {
      console.log(`   Score: ${result.score.toFixed(1)}% (needs 75%+)`);
    }
  }
}).catch(error => {
  console.error('\n💥 Test execution failed:', error);
});
