/**
 * Modern React 18+ Error Boundary Component
 * 
 * Implements comprehensive error handling with:
 * - React 18+ error boundary patterns
 * - User-friendly fallback UI
 * - Error reporting and logging
 * - Recovery mechanisms
 * - Development vs production error display
 */

import React, { Component, ErrorInfo, ReactNode } from 'react'
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Alert,
  Collapse,
  IconButton,
  Stack
} from '@mui/material'
import {
  ErrorOutline,
  Refresh,
  ExpandMore,
  ExpandLess,
  BugReport,
  Home
} from '@mui/icons-material'
import { colors } from '../../theme'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
  componentName?: string
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  showDetails: boolean
  retryCount: number
}

class ErrorBoundary extends Component<Props, State> {
  private retryTimeoutId: NodeJS.Timeout | null = null

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    // Update state with error info
    this.setState({
      error,
      errorInfo
    })

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Report error to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo)
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId)
    }
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // In a real application, send error to monitoring service
    // Example: Sentry, LogRocket, Bugsnag, etc.
    try {
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        componentName: this.props.componentName
      }
      
      // Send to monitoring service
      console.log('Error report:', errorReport)
      
      // Example: window.errorReportingService?.captureException(error, errorReport)
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
    }
  }

  private handleRetry = () => {
    const { retryCount } = this.state
    
    // Limit retry attempts
    if (retryCount >= 3) {
      console.warn('Maximum retry attempts reached')
      return
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: retryCount + 1
    })

    // Auto-retry with exponential backoff
    this.retryTimeoutId = setTimeout(() => {
      if (this.state.hasError) {
        this.handleRetry()
      }
    }, Math.pow(2, retryCount) * 1000)
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    window.location.href = '/dashboard'
  }

  private toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails
    }))
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI if provided
      if (this.props.fallback) {
        return this.props.fallback
      }

      const { error, errorInfo, showDetails, retryCount } = this.state
      const isDevelopment = process.env.NODE_ENV === 'development'
      const showDetailsOption = this.props.showDetails ?? isDevelopment

      return (
        <Box
          sx={{
            minHeight: '400px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: 3
          }}
        >
          <Card sx={{ maxWidth: 600, width: '100%' }}>
            <CardContent>
              <Stack spacing={3} alignItems="center">
                {/* Error Icon */}
                <ErrorOutline 
                  sx={{ 
                    fontSize: 64, 
                    color: colors.error.main 
                  }} 
                />

                {/* Error Title */}
                <Typography 
                  variant="h5" 
                  component="h1" 
                  textAlign="center"
                  sx={{ fontWeight: 600 }}
                >
                  Oops! Something went wrong
                </Typography>

                {/* Error Description */}
                <Typography 
                  variant="body1" 
                  textAlign="center" 
                  color="text.secondary"
                >
                  {isDevelopment 
                    ? `An error occurred in the ${this.props.componentName || 'component'}`
                    : 'We encountered an unexpected error. Please try again.'
                  }
                </Typography>

                {/* Error Details (Development/Optional) */}
                {showDetailsOption && error && (
                  <Box sx={{ width: '100%' }}>
                    <Button
                      startIcon={showDetails ? <ExpandLess /> : <ExpandMore />}
                      onClick={this.toggleDetails}
                      size="small"
                      sx={{ mb: 1 }}
                    >
                      {showDetails ? 'Hide' : 'Show'} Error Details
                    </Button>
                    
                    <Collapse in={showDetails}>
                      <Alert severity="error" sx={{ textAlign: 'left' }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                          Error Message:
                        </Typography>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace', mb: 2 }}>
                          {error.message}
                        </Typography>
                        
                        {errorInfo && (
                          <>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                              Component Stack:
                            </Typography>
                            <Typography 
                              variant="body2" 
                              sx={{ 
                                fontFamily: 'monospace', 
                                whiteSpace: 'pre-wrap',
                                fontSize: '0.75rem'
                              }}
                            >
                              {errorInfo.componentStack}
                            </Typography>
                          </>
                        )}
                      </Alert>
                    </Collapse>
                  </Box>
                )}

                {/* Action Buttons */}
                <Stack direction="row" spacing={2}>
                  <Button
                    variant="contained"
                    startIcon={<Refresh />}
                    onClick={this.handleRetry}
                    disabled={retryCount >= 3}
                  >
                    {retryCount >= 3 ? 'Max Retries' : 'Try Again'}
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<Home />}
                    onClick={this.handleGoHome}
                  >
                    Go to Dashboard
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<Refresh />}
                    onClick={this.handleReload}
                    color="secondary"
                  >
                    Reload Page
                  </Button>
                </Stack>

                {/* Retry Information */}
                {retryCount > 0 && (
                  <Typography variant="caption" color="text.secondary">
                    Retry attempts: {retryCount}/3
                  </Typography>
                )}
              </Stack>
            </CardContent>
          </Card>
        </Box>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary

// Functional component wrapper for easier usage
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Hook for error reporting in functional components
export const useErrorHandler = () => {
  const reportError = React.useCallback((error: Error, context?: string) => {
    console.error(`Error in ${context || 'component'}:`, error)
    
    // In production, report to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Example: errorReportingService.captureException(error, { context })
    }
  }, [])

  return { reportError }
}
