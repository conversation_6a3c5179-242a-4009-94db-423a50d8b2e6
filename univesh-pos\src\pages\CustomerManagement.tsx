import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Avatar,
  Tabs,
  Tab
} from '@mui/material'
import {
  Add,
  Edit,
  Delete,
  Search,
  Email,
  Phone,
  LocationOn,
  History
} from '@mui/icons-material'
import { colors } from '../theme'
import { supabase } from '../lib/supabase'

interface Customer {
  id: string
  full_name: string
  email: string | null
  phone_number: string | null
  billing_address: string | null
  shipping_address: string | null
  created_at: string
  updated_at: string
}

interface CustomerFormData {
  full_name: string
  email: string
  phone_number: string
  billing_address: string
  shipping_address: string
}

interface CustomerOrder {
  id: string
  order_type: string
  status: string
  total_amount: number
  created_at: string
}

const CustomerManagement: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Customer dialog state
  const [customerDialogOpen, setCustomerDialogOpen] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const [customerFormData, setCustomerFormData] = useState<CustomerFormData>({
    full_name: '',
    email: '',
    phone_number: '',
    billing_address: '',
    shipping_address: ''
  })

  // Customer details dialog
  const [customerDetailsOpen, setCustomerDetailsOpen] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [customerOrders, setCustomerOrders] = useState<CustomerOrder[]>([])
  const [detailsTab, setDetailsTab] = useState(0)

  // Filters
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    fetchCustomers()
  }, [])

  const fetchCustomers = async () => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .order('full_name')

      if (error) throw error

      setCustomers(data || [])
    } catch (error) {
      console.error('Error fetching customers:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch customers')
    } finally {
      setLoading(false)
    }
  }

  const fetchCustomerOrders = async (customerId: string) => {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select('id, order_type, status, total_amount, created_at')
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false })

      if (error) throw error

      setCustomerOrders(data || [])
    } catch (error) {
      console.error('Error fetching customer orders:', error)
    }
  }

  const openCustomerDialog = (customer?: Customer) => {
    if (customer) {
      setEditingCustomer(customer)
      setCustomerFormData({
        full_name: customer.full_name,
        email: customer.email || '',
        phone_number: customer.phone_number || '',
        billing_address: customer.billing_address || '',
        shipping_address: customer.shipping_address || ''
      })
    } else {
      setEditingCustomer(null)
      setCustomerFormData({
        full_name: '',
        email: '',
        phone_number: '',
        billing_address: '',
        shipping_address: ''
      })
    }
    setCustomerDialogOpen(true)
  }

  const closeCustomerDialog = () => {
    setCustomerDialogOpen(false)
    setEditingCustomer(null)
    setCustomerFormData({
      full_name: '',
      email: '',
      phone_number: '',
      billing_address: '',
      shipping_address: ''
    })
  }

  const saveCustomer = async () => {
    try {
      const customerData = {
        full_name: customerFormData.full_name,
        email: customerFormData.email || null,
        phone_number: customerFormData.phone_number || null,
        billing_address: customerFormData.billing_address || null,
        shipping_address: customerFormData.shipping_address || null,
        updated_at: new Date().toISOString()
      }

      if (editingCustomer) {
        // Update existing customer
        const { error } = await supabase
          .from('customers')
          .update(customerData)
          .eq('id', editingCustomer.id)

        if (error) throw error
      } else {
        // Create new customer
        const { error } = await supabase
          .from('customers')
          .insert(customerData)

        if (error) throw error
      }

      await fetchCustomers()
      closeCustomerDialog()
    } catch (error) {
      console.error('Error saving customer:', error)
      setError(error instanceof Error ? error.message : 'Failed to save customer')
    }
  }

  const deleteCustomer = async (customerId: string) => {
    if (!confirm('Are you sure you want to delete this customer? This action cannot be undone.')) return

    try {
      const { error } = await supabase
        .from('customers')
        .delete()
        .eq('id', customerId)

      if (error) throw error

      await fetchCustomers()
    } catch (error) {
      console.error('Error deleting customer:', error)
      setError(error instanceof Error ? error.message : 'Failed to delete customer')
    }
  }

  const openCustomerDetails = async (customer: Customer) => {
    setSelectedCustomer(customer)
    setDetailsTab(0)
    await fetchCustomerOrders(customer.id)
    setCustomerDetailsOpen(true)
  }

  const closeCustomerDetails = () => {
    setCustomerDetailsOpen(false)
    setSelectedCustomer(null)
    setCustomerOrders([])
  }

  // Filter customers
  const filteredCustomers = customers.filter(customer =>
    customer.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (customer.phone_number && customer.phone_number.includes(searchTerm))
  )

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress size={60} />
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Customer Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => openCustomerDialog()}
        >
          Add Customer
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Card>
        <CardContent>
          {/* Search */}
          <Box sx={{ mb: 3 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  placeholder="Search customers by name, email, or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary" align="right">
                  {filteredCustomers.length} customers
                </Typography>
              </Grid>
            </Grid>
          </Box>

          {/* Customers Table */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Customer</TableCell>
                  <TableCell>Contact</TableCell>
                  <TableCell>Address</TableCell>
                  <TableCell align="center">Joined</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredCustomers.map((customer) => (
                  <TableRow key={customer.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: colors.primary.main }}>
                          {customer.full_name.charAt(0).toUpperCase()}
                        </Avatar>
                        <Box>
                          <Typography variant="body1" sx={{ fontWeight: 500 }}>
                            {customer.full_name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Customer ID: {customer.id.slice(0, 8)}...
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        {customer.email && (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Email sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="body2">{customer.email}</Typography>
                          </Box>
                        )}
                        {customer.phone_number && (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Phone sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="body2">{customer.phone_number}</Typography>
                          </Box>
                        )}
                        {!customer.email && !customer.phone_number && (
                          <Typography variant="body2" color="text.secondary">
                            No contact info
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      {customer.billing_address ? (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LocationOn sx={{ fontSize: 16, color: 'text.secondary' }} />
                          <Typography variant="body2">
                            {customer.billing_address.length > 30
                              ? `${customer.billing_address.substring(0, 30)}...`
                              : customer.billing_address
                            }
                          </Typography>
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No address
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">
                        {new Date(customer.created_at).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        size="small"
                        onClick={() => openCustomerDetails(customer)}
                        sx={{ mr: 1 }}
                      >
                        <History />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => openCustomerDialog(customer)}
                        sx={{ mr: 1 }}
                      >
                        <Edit />
                      </IconButton>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => deleteCustomer(customer.id)}
                      >
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredCustomers.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <Typography variant="body2" color="text.secondary" sx={{ py: 4 }}>
                        No customers found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Customer Dialog */}
      <Dialog open={customerDialogOpen} onClose={closeCustomerDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingCustomer ? 'Edit Customer' : 'Add New Customer'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Full Name"
                value={customerFormData.full_name}
                onChange={(e) => setCustomerFormData(prev => ({ ...prev, full_name: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={customerFormData.email}
                onChange={(e) => setCustomerFormData(prev => ({ ...prev, email: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={customerFormData.phone_number}
                onChange={(e) => setCustomerFormData(prev => ({ ...prev, phone_number: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Billing Address"
                value={customerFormData.billing_address}
                onChange={(e) => setCustomerFormData(prev => ({ ...prev, billing_address: e.target.value }))}
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Shipping Address"
                value={customerFormData.shipping_address}
                onChange={(e) => setCustomerFormData(prev => ({ ...prev, shipping_address: e.target.value }))}
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeCustomerDialog}>Cancel</Button>
          <Button
            onClick={saveCustomer}
            variant="contained"
            disabled={!customerFormData.full_name}
          >
            {editingCustomer ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Customer Details Dialog */}
      <Dialog open={customerDetailsOpen} onClose={closeCustomerDetails} maxWidth="lg" fullWidth>
        <DialogTitle>
          Customer Details - {selectedCustomer?.full_name}
        </DialogTitle>
        <DialogContent>
          <Tabs value={detailsTab} onChange={(_, value) => setDetailsTab(value)} sx={{ mb: 3 }}>
            <Tab label="Information" />
            <Tab label="Order History" />
          </Tabs>

          {detailsTab === 0 && selectedCustomer && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>Contact Information</Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Email sx={{ color: 'text.secondary' }} />
                        <Typography>{selectedCustomer.email || 'No email provided'}</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Phone sx={{ color: 'text.secondary' }} />
                        <Typography>{selectedCustomer.phone_number || 'No phone provided'}</Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>Addresses</Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">Billing Address:</Typography>
                        <Typography>{selectedCustomer.billing_address || 'No billing address'}</Typography>
                      </Box>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">Shipping Address:</Typography>
                        <Typography>{selectedCustomer.shipping_address || 'No shipping address'}</Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {detailsTab === 1 && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Order ID</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell align="right">Amount</TableCell>
                    <TableCell>Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {customerOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                          {order.id.slice(0, 8)}...
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={order.order_type.replace('_', ' ').toUpperCase()}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={order.status.toUpperCase()}
                          size="small"
                          color={
                            order.status === 'completed' ? 'success' :
                            order.status === 'cancelled' ? 'error' :
                            order.status === 'pending' ? 'warning' : 'default'
                          }
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          ${order.total_amount.toFixed(2)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(order.created_at).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                  {customerOrders.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} align="center">
                        <Typography variant="body2" color="text.secondary" sx={{ py: 4 }}>
                          No orders found for this customer
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={closeCustomerDetails}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default CustomerManagement
