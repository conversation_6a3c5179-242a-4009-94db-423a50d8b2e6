/**
 * Hook for fetching and managing system settings
 */

import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'

interface SystemSettings {
  default_tax_rate: number
  dine_in_tax_rate: number
  delivery_fee: number
  currency_symbol: string
  auto_print_receipts: boolean
}

export const useSystemSettings = () => {
  const [settings, setSettings] = useState<SystemSettings>({
    default_tax_rate: 0.05,
    dine_in_tax_rate: 0.10,
    delivery_fee: 1.00,
    currency_symbol: '$',
    auto_print_receipts: true
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchSystemSettings()
  }, [])

  const fetchSystemSettings = async () => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await supabase
        .from('system_settings')
        .select('*')
        .single()

      if (error) {
        throw error
      }

      if (data) {
        setSettings({
          default_tax_rate: Number(data.default_tax_rate),
          dine_in_tax_rate: Number(data.dine_in_tax_rate),
          delivery_fee: Number(data.delivery_fee),
          currency_symbol: data.currency_symbol,
          auto_print_receipts: data.auto_print_receipts
        })
      }
    } catch (error) {
      console.error('Error fetching system settings:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch system settings')
    } finally {
      setLoading(false)
    }
  }

  return {
    settings,
    loading,
    error,
    refetch: fetchSystemSettings
  }
}
