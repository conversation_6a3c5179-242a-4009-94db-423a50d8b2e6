/**
 * Simple Fixes Verification Test
 * 
 * Quick verification that all critical fixes are working:
 * 1. MenuScreen component loads without errors
 * 2. Navigation works properly
 * 3. Loading states resolve correctly
 * 4. Browser extension errors are suppressed
 */

import puppeteer from 'puppeteer';

async function verifyFixes() {
  console.log('🔍 Verifying Critical Error Fixes...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    devtools: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    let jsErrors = 0;
    let extensionErrors = 0;
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        const text = msg.text();
        if (text.includes('runtime.lastError') || text.includes('extension')) {
          extensionErrors++;
        } else {
          jsErrors++;
          console.log(`❌ JS Error: ${text}`);
        }
      }
    });
    
    page.on('pageerror', error => {
      jsErrors++;
      console.log(`❌ Page Error: ${error.message}`);
    });
    
    console.log('🔐 Testing Authentication...');
    await page.goto('http://localhost:5175/', { waitUntil: 'networkidle0' });
    
    // Login
    await page.waitForSelector('input[type="text"]', { timeout: 10000 });
    await page.type('input[type="text"]', 'ADMIN001');
    await page.type('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard
    await page.waitForNavigation({ timeout: 15000 });
    
    const dashboardLoaded = await page.evaluate(() => {
      return document.body.textContent.includes('Dashboard') && 
             !document.body.textContent.includes('Loading');
    });
    
    console.log(`✅ Authentication: ${dashboardLoaded ? 'SUCCESS' : 'FAILED'}`);
    
    console.log('\n🍽️ Testing MenuScreen Component...');
    await page.goto('http://localhost:5175/menu', { waitUntil: 'networkidle0' });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const menuState = await page.evaluate(() => {
      return {
        hasMenu: document.body.textContent.includes('Menu'),
        hasProducts: document.querySelectorAll('.MuiCard-root').length > 0,
        hasCart: document.body.textContent.includes('Current Order'),
        hasLoading: document.body.textContent.includes('Loading'),
        hasError: document.body.textContent.includes('Something went wrong')
      };
    });
    
    console.log(`✅ MenuScreen loaded: ${menuState.hasMenu ? 'SUCCESS' : 'FAILED'}`);
    console.log(`✅ Products visible: ${menuState.hasProducts ? 'SUCCESS' : 'FAILED'}`);
    console.log(`✅ Cart functional: ${menuState.hasCart ? 'SUCCESS' : 'FAILED'}`);
    console.log(`✅ No loading stuck: ${!menuState.hasLoading ? 'SUCCESS' : 'FAILED'}`);
    console.log(`✅ No error boundary: ${!menuState.hasError ? 'SUCCESS' : 'FAILED'}`);
    
    console.log('\n🧭 Testing Navigation...');
    const pages = ['/dashboard', '/orders', '/bills', '/settings'];
    let navigationSuccess = 0;
    
    for (const pagePath of pages) {
      await page.goto(`http://localhost:5175${pagePath}`, { waitUntil: 'networkidle0' });
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const pageLoaded = await page.evaluate(() => {
        return document.body.textContent.length > 500 && 
               !document.body.textContent.includes('Loading');
      });
      
      if (pageLoaded) navigationSuccess++;
      console.log(`   ${pagePath}: ${pageLoaded ? 'SUCCESS' : 'FAILED'}`);
    }
    
    console.log(`✅ Navigation: ${navigationSuccess}/${pages.length} pages working`);
    
    console.log('\n🛡️ Error Summary:');
    console.log(`   JavaScript errors: ${jsErrors}`);
    console.log(`   Extension errors: ${extensionErrors} (should be suppressed)`);
    
    const overallSuccess = dashboardLoaded && 
                          menuState.hasMenu && 
                          !menuState.hasError && 
                          navigationSuccess >= 3 && 
                          jsErrors === 0;
    
    console.log(`\n🎯 Overall Status: ${overallSuccess ? '✅ ALL FIXES WORKING' : '⚠️ NEEDS ATTENTION'}`);
    
    return {
      success: overallSuccess,
      details: {
        authentication: dashboardLoaded,
        menuComponent: menuState.hasMenu && !menuState.hasError,
        navigation: navigationSuccess >= 3,
        noJsErrors: jsErrors === 0,
        jsErrorCount: jsErrors,
        extensionErrorCount: extensionErrors
      }
    };
    
  } catch (error) {
    console.error('💥 Verification failed:', error);
    return { success: false, error: error.message };
  } finally {
    await browser.close();
  }
}

// Run verification
verifyFixes().then(result => {
  console.log('\n' + '='.repeat(50));
  console.log('🏆 FIXES VERIFICATION COMPLETE');
  console.log('='.repeat(50));
  
  if (result.success) {
    console.log('🎉 ALL CRITICAL FIXES VERIFIED SUCCESSFULLY!');
    console.log('\n✅ Fixed Issues:');
    console.log('   • MenuScreen React errors resolved');
    console.log('   • Navigation state management improved');
    console.log('   • Loading screen persistence fixed');
    console.log('   • Error boundaries implemented');
    console.log('   • Browser extension conflicts handled');
  } else {
    console.log('⚠️ Some issues still need attention');
    if (result.details) {
      console.log('\n📊 Status Details:');
      Object.entries(result.details).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
      });
    }
  }
}).catch(error => {
  console.error('\n💥 Verification execution failed:', error);
});
