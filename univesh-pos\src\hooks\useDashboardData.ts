import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'

interface DashboardMetrics {
  dailySales: { value: string; change: string; trend: 'up' | 'down' }
  totalIncome: { value: string; change: string; trend: 'up' | 'down' }
  totalOrders: { value: string; change: string; trend: 'up' | 'down' }
  newCustomers: { value: string; change: string; trend: 'up' | 'down' }
}

interface TrendingDish {
  name: string
  sales: string
  quantity: number
}

interface BestEmployee {
  name: string
  role: string
  sales: string
  orders: number
}

interface DashboardData {
  metrics: DashboardMetrics
  trendingDishes: TrendingDish[]
  bestEmployees: BestEmployee[]
  loading: boolean
  error: string | null
}

export const useDashboardData = (): DashboardData => {
  const [data, setData] = useState<DashboardData>({
    metrics: {
      dailySales: { value: '$0.00', change: '0%', trend: 'up' },
      totalIncome: { value: '$0.00', change: '0%', trend: 'up' },
      totalOrders: { value: '0', change: '0%', trend: 'up' },
      newCustomers: { value: '0', change: '0%', trend: 'up' }
    },
    trendingDishes: [],
    bestEmployees: [],
    loading: true,
    error: null
  })

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setData(prev => ({ ...prev, loading: true, error: null }))

      // Get today's date range
      const today = new Date()
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

      // Get yesterday's date range for comparison
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      const startOfYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate())
      const endOfYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate() + 1)

      // Fetch daily sales
      const { data: todayOrders, error: todayError } = await supabase
        .from('orders')
        .select('total_amount')
        .eq('status', 'completed')
        .gte('created_at', startOfDay.toISOString())
        .lt('created_at', endOfDay.toISOString())

      if (todayError) throw todayError

      const { data: yesterdayOrders, error: yesterdayError } = await supabase
        .from('orders')
        .select('total_amount')
        .eq('status', 'completed')
        .gte('created_at', startOfYesterday.toISOString())
        .lt('created_at', endOfYesterday.toISOString())

      if (yesterdayError) throw yesterdayError

      // Calculate daily sales
      const todaySales = todayOrders?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0
      const yesterdaySales = yesterdayOrders?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0
      const salesChange = yesterdaySales > 0 ? ((todaySales - yesterdaySales) / yesterdaySales * 100) : 0

      // Fetch total income (last 30 days)
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      const { data: monthlyOrders, error: monthlyError } = await supabase
        .from('orders')
        .select('total_amount')
        .eq('status', 'completed')
        .gte('created_at', thirtyDaysAgo.toISOString())

      if (monthlyError) throw monthlyError

      const totalIncome = monthlyOrders?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0

      // Fetch total orders count
      const { count: totalOrdersCount, error: ordersCountError } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'completed')
        .gte('created_at', startOfDay.toISOString())
        .lt('created_at', endOfDay.toISOString())

      if (ordersCountError) throw ordersCountError

      const { count: yesterdayOrdersCount, error: yesterdayOrdersError } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'completed')
        .gte('created_at', startOfYesterday.toISOString())
        .lt('created_at', endOfYesterday.toISOString())

      if (yesterdayOrdersError) throw yesterdayOrdersError

      const ordersChange = yesterdayOrdersCount && yesterdayOrdersCount > 0 
        ? ((totalOrdersCount || 0) - yesterdayOrdersCount) / yesterdayOrdersCount * 100 
        : 0

      // Fetch new customers count
      const { count: newCustomersCount, error: customersError } = await supabase
        .from('customers')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', startOfDay.toISOString())
        .lt('created_at', endOfDay.toISOString())

      if (customersError) throw customersError

      // Fetch trending dishes (top 5 by quantity sold today)
      const { data: trendingDishesData, error: dishesError } = await supabase
        .from('order_items')
        .select(`
          quantity,
          unit_price_at_order,
          products!inner(name)
        `)
        .gte('created_at', startOfDay.toISOString())
        .lt('created_at', endOfDay.toISOString())

      if (dishesError) throw dishesError

      // Group by product and calculate totals
      const dishMap = new Map()
      trendingDishesData?.forEach(item => {
        const productName = item.products.name
        if (dishMap.has(productName)) {
          const existing = dishMap.get(productName)
          existing.quantity += item.quantity
          existing.sales += item.quantity * Number(item.unit_price_at_order)
        } else {
          dishMap.set(productName, {
            name: productName,
            quantity: item.quantity,
            sales: item.quantity * Number(item.unit_price_at_order)
          })
        }
      })

      const trendingDishes = Array.from(dishMap.values())
        .sort((a, b) => b.quantity - a.quantity)
        .slice(0, 5)
        .map(dish => ({
          name: dish.name,
          sales: `$${dish.sales.toFixed(2)}`,
          quantity: dish.quantity
        }))

      // Fetch best employees (mock data for now since we need more complex queries)
      const bestEmployees = [
        { name: 'Sarah Johnson', role: 'Cashier', sales: '$3,245.00', orders: 67 },
        { name: 'Mike Chen', role: 'Manager', sales: '$2,890.50', orders: 54 },
        { name: 'Emily Davis', role: 'Cashier', sales: '$2,456.25', orders: 48 },
        { name: 'James Wilson', role: 'Cashier', sales: '$2,123.75', orders: 42 }
      ]

      setData({
        metrics: {
          dailySales: {
            value: `$${todaySales.toFixed(2)}`,
            change: `${salesChange >= 0 ? '+' : ''}${salesChange.toFixed(1)}%`,
            trend: salesChange >= 0 ? 'up' : 'down'
          },
          totalIncome: {
            value: `$${totalIncome.toFixed(2)}`,
            change: '+8.2%', // Mock change for now
            trend: 'up'
          },
          totalOrders: {
            value: (totalOrdersCount || 0).toString(),
            change: `${ordersChange >= 0 ? '+' : ''}${ordersChange.toFixed(1)}%`,
            trend: ordersChange >= 0 ? 'up' : 'down'
          },
          newCustomers: {
            value: (newCustomersCount || 0).toString(),
            change: '+5.2%', // Mock change for now
            trend: 'up'
          }
        },
        trendingDishes,
        bestEmployees,
        loading: false,
        error: null
      })
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      setData(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch dashboard data'
      }))
    }
  }

  return data
}
