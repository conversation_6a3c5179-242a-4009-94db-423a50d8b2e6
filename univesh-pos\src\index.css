/* =============================================
   PRODUCTION-READY GLOBAL STYLES
   Univesh Restaurant POS System
   ============================================= */

/* CSS Custom Properties for theming */
:root {
  --primary-color: #1049B8;
  --secondary-color: #F8BF24;
  --success-color: #36D084;
  --error-color: #E15F71;
  --warning-color: #FDC548;
  --info-color: #9E8BCE;
  --text-primary: #000000;
  --text-secondary: #616161;
  --background-default: #ffffff;
  --background-paper: #ffffff;
  --border-color: #E2E3E4;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-default);
  color: var(--text-primary);
  line-height: 1.5;
}

#root {
  min-height: 100vh;
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus management */
*:focus-visible {
  outline: 3px solid var(--primary-color);
  outline-offset: 2px;
}

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
}

.skip-link:focus {
  top: 6px;
}

/* Remove default button styles */
button {
  font-family: inherit;
  min-height: 44px; /* Accessibility: minimum touch target */
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #000080;
    --secondary-color: #FFD700;
    --success-color: #008000;
    --error-color: #FF0000;
    --warning-color: #FFA500;
    --info-color: #800080;
    --border-color: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Mobile-first responsive design */
@media (max-width: 599px) {
  body {
    font-size: 14px;
  }

  /* Ensure touch targets are at least 44px */
  button,
  input,
  select,
  textarea,
  [role="button"],
  [role="tab"] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
