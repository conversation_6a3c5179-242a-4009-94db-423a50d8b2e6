/**
 * Debug MenuScreen Component Issues
 * 
 * This script tests the MenuScreen component to identify and fix React errors
 */

import { chromium } from 'playwright'

async function debugMenuScreen() {
  console.log('🔍 Starting MenuScreen Debug Test...')
  
  const browser = await chromium.launch({ 
    headless: false,
    devtools: true
  })
  
  const context = await browser.newContext()
  const page = await context.newPage()
  
  // Listen for console errors
  page.on('console', msg => {
    if (msg.type() === 'error') {
      console.error('❌ Browser Console Error:', msg.text())
    }
  })
  
  // Listen for page errors
  page.on('pageerror', error => {
    console.error('❌ Page Error:', error.message)
    console.error('Stack:', error.stack)
  })
  
  try {
    // Navigate to login
    console.log('📱 Navigating to login page...')
    await page.goto('http://localhost:5174/login')
    await page.waitForLoadState('networkidle')
    
    // Login
    console.log('🔐 Logging in...')
    await page.getByLabel('Sales ID Number').fill('ADMIN001')
    await page.getByLabel('Password').fill('admin123')
    await page.getByRole('button', { name: 'Sign In' }).click()
    
    // Wait for dashboard
    console.log('⏳ Waiting for dashboard...')
    await page.waitForURL('**/dashboard', { timeout: 15000 })
    await page.waitForLoadState('networkidle')
    
    // Navigate to menu
    console.log('🍽️ Navigating to menu screen...')
    await page.getByText('Menu & Orders').click()
    
    // Wait for menu page and check for errors
    console.log('⏳ Waiting for menu page to load...')
    await page.waitForURL('**/menu', { timeout: 10000 })
    
    // Wait a bit for React to render
    await page.waitForTimeout(3000)
    
    // Check if error boundary is shown
    const errorBoundary = await page.locator('text=Oops! Something went wrong').count()
    if (errorBoundary > 0) {
      console.error('❌ Error boundary is displayed!')
      
      // Try to get error details
      const showDetailsBtn = page.locator('text=Show Error Details')
      if (await showDetailsBtn.count() > 0) {
        await showDetailsBtn.click()
        await page.waitForTimeout(1000)
        
        const errorMessage = await page.locator('.MuiAlert-root').textContent()
        console.error('Error details:', errorMessage)
      }
    } else {
      console.log('✅ Menu screen loaded without error boundary')
    }
    
    // Check if menu content is visible
    const menuTitle = await page.locator('text=Menu & Orders').count()
    const categoryTabs = await page.locator('.MuiTabs-root').count()
    const productGrid = await page.locator('.MuiGrid-container').count()
    
    console.log('📊 Menu Screen Elements:')
    console.log(`  - Menu Title: ${menuTitle > 0 ? '✅' : '❌'}`)
    console.log(`  - Category Tabs: ${categoryTabs > 0 ? '✅' : '❌'}`)
    console.log(`  - Product Grid: ${productGrid > 0 ? '✅' : '❌'}`)
    
    // Check for loading states
    const loadingSpinner = await page.locator('.MuiCircularProgress-root').count()
    console.log(`  - Loading Spinner: ${loadingSpinner > 0 ? '⏳ Still loading' : '✅ Loaded'}`)
    
    // Check for error alerts
    const errorAlerts = await page.locator('.MuiAlert-standardError').count()
    console.log(`  - Error Alerts: ${errorAlerts > 0 ? '❌ ' + errorAlerts : '✅ None'}`)
    
    // Try to interact with the menu
    if (categoryTabs > 0) {
      console.log('🔄 Testing category tab interaction...')
      const firstTab = page.locator('.MuiTab-root').first()
      await firstTab.click()
      await page.waitForTimeout(1000)
      console.log('✅ Category tab clicked successfully')
    }
    
    // Check if products are loaded
    const productCards = await page.locator('.MuiCard-root').count()
    console.log(`  - Product Cards: ${productCards} found`)
    
    if (productCards > 0) {
      console.log('🛒 Testing add to cart functionality...')
      const firstProduct = page.locator('.MuiCard-root').first()
      await firstProduct.click()
      await page.waitForTimeout(1000)
      
      const cartBadge = await page.locator('.MuiBadge-badge').textContent()
      console.log(`  - Cart items: ${cartBadge || '0'}`)
    }
    
    console.log('✅ MenuScreen debug test completed')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('Stack:', error.stack)
  } finally {
    await browser.close()
  }
}

// Run the test
debugMenuScreen().catch(console.error)
