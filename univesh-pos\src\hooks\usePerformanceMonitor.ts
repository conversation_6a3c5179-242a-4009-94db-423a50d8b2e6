import { useEffect, useRef } from 'react'

interface PerformanceMetrics {
  componentName: string
  renderTime: number
  timestamp: number
}

interface PerformanceConfig {
  enableLogging?: boolean
  threshold?: number // Log only if render time exceeds threshold (ms)
  sampleRate?: number // Percentage of renders to monitor (0-100)
}

export const usePerformanceMonitor = (
  componentName: string,
  config: PerformanceConfig = {}
) => {
  const {
    enableLogging = process.env.NODE_ENV === 'development',
    threshold = 16, // 16ms = 60fps
    sampleRate = 100
  } = config

  const renderStartTime = useRef<number>(0)
  const renderCount = useRef<number>(0)
  const totalRenderTime = useRef<number>(0)

  // Start timing on component mount/update
  useEffect(() => {
    renderStartTime.current = performance.now()
  })

  // Measure render time after DOM update
  useEffect(() => {
    const renderEndTime = performance.now()
    const renderTime = renderEndTime - renderStartTime.current
    
    renderCount.current += 1
    totalRenderTime.current += renderTime

    // Sample rate check
    if (Math.random() * 100 > sampleRate) {
      return
    }

    const metrics: PerformanceMetrics = {
      componentName,
      renderTime,
      timestamp: Date.now()
    }

    // Log performance metrics
    if (enableLogging && renderTime > threshold) {
      console.warn(`🐌 Slow render detected in ${componentName}:`, {
        renderTime: `${renderTime.toFixed(2)}ms`,
        threshold: `${threshold}ms`,
        renderCount: renderCount.current,
        avgRenderTime: `${(totalRenderTime.current / renderCount.current).toFixed(2)}ms`
      })
    }

    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production' && renderTime > threshold * 2) {
      sendPerformanceMetrics(metrics)
    }
  })

  const getPerformanceStats = () => ({
    componentName,
    renderCount: renderCount.current,
    totalRenderTime: totalRenderTime.current,
    avgRenderTime: renderCount.current > 0 ? totalRenderTime.current / renderCount.current : 0
  })

  return { getPerformanceStats }
}

// Send performance metrics to monitoring service
const sendPerformanceMetrics = (metrics: PerformanceMetrics) => {
  // In a real application, you would send this to your monitoring service
  // For now, we'll just log it
  console.log('Performance metrics:', metrics)
  
  // Example: Send to analytics service
  // analytics.track('component_performance', metrics)
}

// Hook for monitoring API call performance
export const useApiPerformanceMonitor = () => {
  const measureApiCall = async <T>(
    apiCall: () => Promise<T>,
    operationName: string
  ): Promise<T> => {
    const startTime = performance.now()
    
    try {
      const result = await apiCall()
      const endTime = performance.now()
      const duration = endTime - startTime

      // Log slow API calls
      if (duration > 1000) { // 1 second threshold
        console.warn(`🐌 Slow API call detected: ${operationName}`, {
          duration: `${duration.toFixed(2)}ms`,
          timestamp: new Date().toISOString()
        })
      }

      // Send to monitoring service in production
      if (process.env.NODE_ENV === 'production') {
        sendApiMetrics({
          operationName,
          duration,
          success: true,
          timestamp: Date.now()
        })
      }

      return result
    } catch (error) {
      const endTime = performance.now()
      const duration = endTime - startTime

      console.error(`❌ API call failed: ${operationName}`, {
        duration: `${duration.toFixed(2)}ms`,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      // Send error metrics
      if (process.env.NODE_ENV === 'production') {
        sendApiMetrics({
          operationName,
          duration,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: Date.now()
        })
      }

      throw error
    }
  }

  return { measureApiCall }
}

interface ApiMetrics {
  operationName: string
  duration: number
  success: boolean
  error?: string
  timestamp: number
}

const sendApiMetrics = (metrics: ApiMetrics) => {
  // Send to monitoring service
  console.log('API metrics:', metrics)
}

// Hook for monitoring memory usage
export const useMemoryMonitor = (componentName: string) => {
  useEffect(() => {
    if (!('memory' in performance)) {
      return // Memory API not supported
    }

    const checkMemoryUsage = () => {
      const memory = (performance as any).memory
      const usedMB = memory.usedJSHeapSize / 1024 / 1024
      const totalMB = memory.totalJSHeapSize / 1024 / 1024
      const limitMB = memory.jsHeapSizeLimit / 1024 / 1024

      // Warn if memory usage is high
      if (usedMB > limitMB * 0.8) {
        console.warn(`🧠 High memory usage in ${componentName}:`, {
          used: `${usedMB.toFixed(2)}MB`,
          total: `${totalMB.toFixed(2)}MB`,
          limit: `${limitMB.toFixed(2)}MB`,
          percentage: `${((usedMB / limitMB) * 100).toFixed(1)}%`
        })
      }
    }

    const interval = setInterval(checkMemoryUsage, 10000) // Check every 10 seconds

    return () => clearInterval(interval)
  }, [componentName])
}

// Hook for monitoring network performance
export const useNetworkMonitor = () => {
  useEffect(() => {
    if (!('connection' in navigator)) {
      return // Network Information API not supported
    }

    const connection = (navigator as any).connection
    
    const logNetworkInfo = () => {
      console.log('📡 Network status:', {
        effectiveType: connection.effectiveType,
        downlink: `${connection.downlink}Mbps`,
        rtt: `${connection.rtt}ms`,
        saveData: connection.saveData
      })
    }

    // Log initial network status
    logNetworkInfo()

    // Monitor network changes
    connection.addEventListener('change', logNetworkInfo)

    return () => {
      connection.removeEventListener('change', logNetworkInfo)
    }
  }, [])
}
