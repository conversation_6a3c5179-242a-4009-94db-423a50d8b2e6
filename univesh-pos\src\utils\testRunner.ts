// Production Test Runner for Univesh POS System
// Automated testing utilities for critical functionality

interface TestResult {
  testName: string
  passed: boolean
  error?: string
  duration: number
  timestamp: string
}

interface TestSuite {
  suiteName: string
  tests: TestResult[]
  totalTests: number
  passedTests: number
  failedTests: number
  duration: number
}

class ProductionTestRunner {
  private results: TestSuite[] = []
  private currentSuite: TestSuite | null = null

  // Start a new test suite
  startSuite(suiteName: string): void {
    this.currentSuite = {
      suiteName,
      tests: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      duration: 0
    }
  }

  // Run a single test
  async runTest(testName: string, testFunction: () => Promise<void>): Promise<TestResult> {
    const startTime = performance.now()
    const timestamp = new Date().toISOString()

    try {
      await testFunction()
      const duration = performance.now() - startTime
      
      const result: TestResult = {
        testName,
        passed: true,
        duration,
        timestamp
      }

      this.addTestResult(result)
      return result
    } catch (error) {
      const duration = performance.now() - startTime
      
      const result: TestResult = {
        testName,
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
        timestamp
      }

      this.addTestResult(result)
      return result
    }
  }

  // Add test result to current suite
  private addTestResult(result: TestResult): void {
    if (!this.currentSuite) {
      throw new Error('No active test suite. Call startSuite() first.')
    }

    this.currentSuite.tests.push(result)
    this.currentSuite.totalTests++
    
    if (result.passed) {
      this.currentSuite.passedTests++
    } else {
      this.currentSuite.failedTests++
    }
  }

  // Finish current test suite
  finishSuite(): TestSuite {
    if (!this.currentSuite) {
      throw new Error('No active test suite to finish.')
    }

    this.currentSuite.duration = this.currentSuite.tests.reduce(
      (total, test) => total + test.duration, 0
    )

    this.results.push(this.currentSuite)
    const finishedSuite = this.currentSuite
    this.currentSuite = null

    return finishedSuite
  }

  // Get all test results
  getResults(): TestSuite[] {
    return this.results
  }

  // Generate test report
  generateReport(): string {
    let report = '# 🧪 PRODUCTION TEST REPORT\n\n'
    report += `**Generated:** ${new Date().toISOString()}\n\n`

    const totalSuites = this.results.length
    const totalTests = this.results.reduce((sum, suite) => sum + suite.totalTests, 0)
    const totalPassed = this.results.reduce((sum, suite) => sum + suite.passedTests, 0)
    const totalFailed = this.results.reduce((sum, suite) => sum + suite.failedTests, 0)
    const totalDuration = this.results.reduce((sum, suite) => sum + suite.duration, 0)

    report += '## 📊 SUMMARY\n\n'
    report += `- **Test Suites:** ${totalSuites}\n`
    report += `- **Total Tests:** ${totalTests}\n`
    report += `- **Passed:** ${totalPassed} ✅\n`
    report += `- **Failed:** ${totalFailed} ❌\n`
    report += `- **Success Rate:** ${totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : 0}%\n`
    report += `- **Total Duration:** ${(totalDuration / 1000).toFixed(2)}s\n\n`

    // Overall status
    const overallStatus = totalFailed === 0 ? '✅ PASSED' : '❌ FAILED'
    report += `## 🎯 OVERALL STATUS: ${overallStatus}\n\n`

    // Detailed results for each suite
    this.results.forEach(suite => {
      report += `### 📋 ${suite.suiteName}\n\n`
      report += `- **Tests:** ${suite.totalTests}\n`
      report += `- **Passed:** ${suite.passedTests}\n`
      report += `- **Failed:** ${suite.failedTests}\n`
      report += `- **Duration:** ${(suite.duration / 1000).toFixed(2)}s\n\n`

      if (suite.failedTests > 0) {
        report += '#### ❌ Failed Tests:\n\n'
        suite.tests
          .filter(test => !test.passed)
          .forEach(test => {
            report += `- **${test.testName}**\n`
            report += `  - Error: ${test.error}\n`
            report += `  - Duration: ${test.duration.toFixed(2)}ms\n\n`
          })
      }

      if (suite.passedTests > 0) {
        report += '#### ✅ Passed Tests:\n\n'
        suite.tests
          .filter(test => test.passed)
          .forEach(test => {
            report += `- ${test.testName} (${test.duration.toFixed(2)}ms)\n`
          })
        report += '\n'
      }
    })

    return report
  }
}

// Test utilities for common scenarios
export class TestUtils {
  // Simulate user input
  static async simulateInput(element: HTMLElement, value: string): Promise<void> {
    if (element instanceof HTMLInputElement) {
      element.value = value
      element.dispatchEvent(new Event('input', { bubbles: true }))
      element.dispatchEvent(new Event('change', { bubbles: true }))
    }
  }

  // Simulate button click
  static async simulateClick(element: HTMLElement): Promise<void> {
    element.dispatchEvent(new MouseEvent('click', { bubbles: true }))
  }

  // Wait for element to appear
  static async waitForElement(selector: string, timeout = 5000): Promise<HTMLElement> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      
      const checkElement = () => {
        const element = document.querySelector(selector) as HTMLElement
        
        if (element) {
          resolve(element)
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`Element ${selector} not found within ${timeout}ms`))
        } else {
          setTimeout(checkElement, 100)
        }
      }
      
      checkElement()
    })
  }

  // Wait for condition to be true
  static async waitForCondition(
    condition: () => boolean, 
    timeout = 5000
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      
      const checkCondition = () => {
        if (condition()) {
          resolve()
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`Condition not met within ${timeout}ms`))
        } else {
          setTimeout(checkCondition, 100)
        }
      }
      
      checkCondition()
    })
  }

  // Check if element is visible
  static isElementVisible(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect()
    return rect.width > 0 && rect.height > 0 && 
           window.getComputedStyle(element).visibility !== 'hidden'
  }

  // Get element text content
  static getElementText(selector: string): string {
    const element = document.querySelector(selector)
    return element?.textContent?.trim() || ''
  }

  // Check for console errors
  static checkConsoleErrors(): string[] {
    // This would need to be implemented with a console error listener
    // For now, return empty array
    return []
  }
}

// Export the test runner
export default ProductionTestRunner

// Example usage:
/*
const testRunner = new ProductionTestRunner()

// Authentication Tests
testRunner.startSuite('Authentication Tests')

await testRunner.runTest('Login with valid credentials', async () => {
  const loginButton = await TestUtils.waitForElement('[data-testid="login-button"]')
  const emailInput = await TestUtils.waitForElement('[data-testid="email-input"]')
  const passwordInput = await TestUtils.waitForElement('[data-testid="password-input"]')
  
  await TestUtils.simulateInput(emailInput, '<EMAIL>')
  await TestUtils.simulateInput(passwordInput, 'password123')
  await TestUtils.simulateClick(loginButton)
  
  await TestUtils.waitForElement('[data-testid="dashboard"]')
})

const authSuite = testRunner.finishSuite()
console.log(testRunner.generateReport())
*/
