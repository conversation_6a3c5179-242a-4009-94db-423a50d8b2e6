// Test Execution Script for Production Validation
import CriticalJourneyTests from './tests/criticalJourneys.test'

async function runProductionTests() {
  console.log('🚀 Starting Production Validation Tests...')
  console.log('=' .repeat(50))
  
  const testSuite = new CriticalJourneyTests()
  
  try {
    const startTime = performance.now()
    
    // Run all critical journey tests
    await testSuite.runAllTests()
    
    const endTime = performance.now()
    const totalDuration = (endTime - startTime) / 1000
    
    console.log('=' .repeat(50))
    console.log(`✅ All tests completed in ${totalDuration.toFixed(2)} seconds`)
    console.log('🎉 Production validation PASSED!')
    
    return true
  } catch (error) {
    console.error('=' .repeat(50))
    console.error('❌ Production validation FAILED!')
    console.error('Error:', error)
    
    return false
  } finally {
    // Cleanup test data
    await testSuite.cleanup()
  }
}

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  runProductionTests()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('Test execution failed:', error)
      process.exit(1)
    })
}

export { runProductionTests }
