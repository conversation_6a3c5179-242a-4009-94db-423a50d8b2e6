/**
 * Settings Page with Error Boundary and Navigation State Management
 * 
 * Wraps the Settings component with:
 * - Error boundary for graceful error handling
 * - Navigation state management to prevent loading persistence
 * - Loading state timeout handling
 * - Recovery mechanisms
 */

import React, { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import ErrorBoundary from '../components/error/ErrorBoundary'
import Settings from './Settings'
import { useNavigationState } from '../utils/navigationStateManager'
import { Box, CircularProgress, Typography } from '@mui/material'

const SettingsContent: React.FC = () => {
  const location = useLocation()
  const { isLoading, error, startLoading, completeLoading, setError, clearState } = useNavigationState(location.pathname)

  useEffect(() => {
    // Start loading when component mounts
    startLoading(15000) // 15 second timeout for settings page

    // Simulate loading completion after component is ready
    const timer = setTimeout(() => {
      completeLoading()
    }, 100)

    // Cleanup on unmount
    return () => {
      clearTimeout(timer)
      clearState()
    }
  }, [location.pathname, startLoading, completeLoading, clearState])

  // Handle error state
  if (error) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="error" gutterBottom>
          Settings Loading Error
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {error}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please try refreshing the page or navigating to another section.
        </Typography>
      </Box>
    )
  }

  // Handle loading state
  if (isLoading) {
    return (
      <Box sx={{ 
        display: 'flex', 
        flexDirection: 'column',
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '50vh',
        gap: 2
      }}>
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          Loading Settings...
        </Typography>
      </Box>
    )
  }

  // Render the actual Settings component
  return <Settings />
}

const SettingsWithErrorBoundary: React.FC = () => {
  return (
    <ErrorBoundary
      componentName="Settings"
      fallback={
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" color="error" gutterBottom>
            Settings Error
          </Typography>
          <Typography variant="body2" color="text.secondary">
            There was an error loading the settings page. Please try refreshing or contact support.
          </Typography>
        </Box>
      }
      onError={(error, errorInfo) => {
        console.error('Settings page error:', error, errorInfo)
        // You could send this to an error reporting service
      }}
    >
      <SettingsContent />
    </ErrorBoundary>
  )
}

export default SettingsWithErrorBoundary
