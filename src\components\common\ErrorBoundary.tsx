import React, { Component, ErrorInfo, ReactNode } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Al<PERSON>,
  Container
} from '@mui/material'
import {
  ErrorOutline,
  Refresh,
  Home,
  BugReport
} from '@mui/icons-material'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null
  }

  public static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('🚨 [ErrorBoundary] Uncaught error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // Log to external service in production
    if (import.meta.env.PROD) {
      // TODO: Send to error reporting service
      console.error('Production error:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack
      })
    }
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    window.location.href = '/'
  }

  private handleReportBug = () => {
    const errorReport = {
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      url: window.location.href
    }

    // Copy error details to clipboard
    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2))
      .then(() => {
        alert('Error details copied to clipboard. Please paste this information when reporting the bug.')
      })
      .catch(() => {
        console.error('Failed to copy error details to clipboard')
      })
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Container maxWidth="md" sx={{ py: 8 }}>
          <Card sx={{ textAlign: 'center' }}>
            <CardContent sx={{ p: 6 }}>
              <ErrorOutline 
                sx={{ 
                  fontSize: 80, 
                  color: '#d32f2f',
                  mb: 3 
                }} 
              />
              
              <Typography variant="h4" sx={{ fontWeight: 600, mb: 2 }}>
                Oops! Something went wrong
              </Typography>
              
              <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                We're sorry, but something unexpected happened. Our team has been notified and is working on a fix.
              </Typography>

              {this.state.error && import.meta.env.DEV && (
                <Alert severity="error" sx={{ mb: 4, textAlign: 'left' }}>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', mb: 1 }}>
                    <strong>Error:</strong> {this.state.error.message}
                  </Typography>
                  {this.state.error.stack && (
                    <Typography variant="caption" sx={{ fontFamily: 'monospace', display: 'block', whiteSpace: 'pre-wrap' }}>
                      {this.state.error.stack}
                    </Typography>
                  )}
                </Alert>
              )}

              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  startIcon={<Refresh />}
                  onClick={this.handleReload}
                  size="large"
                >
                  Reload Page
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<Home />}
                  onClick={this.handleGoHome}
                  size="large"
                >
                  Go Home
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<BugReport />}
                  onClick={this.handleReportBug}
                  size="large"
                  color="error"
                >
                  Report Bug
                </Button>
              </Box>

              <Typography variant="caption" color="text.secondary" sx={{ mt: 4, display: 'block' }}>
                Error ID: {Date.now().toString(36)}
              </Typography>
            </CardContent>
          </Card>
        </Container>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
