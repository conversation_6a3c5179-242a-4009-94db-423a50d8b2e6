/**
 * Reports with Error Boundary Wrapper
 */

import React from 'react'
import { withErrorBoundary } from '../components/error/ErrorBoundary'
import Reports from './Reports'
import { Box, Typography, Button } from '@mui/material'
import { Assessment, Refresh } from '@mui/icons-material'
import { colors } from '../theme'

// Custom fallback UI for Reports errors
const ReportsErrorFallback: React.FC = () => {
  const handleRetry = () => {
    window.location.reload()
  }

  return (
    <Box sx={{ p: 3, textAlign: 'center' }}>
      <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
        Reports & Analytics
      </Typography>
      
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 300,
          bgcolor: colors.grey[50],
          borderRadius: 2,
          p: 4
        }}
      >
        <Assessment 
          sx={{ 
            fontSize: 64, 
            color: colors.grey[400], 
            mb: 2 
          }} 
        />
        
        <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
          Reports Temporarily Unavailable
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          We're having trouble loading the reports data. Please try refreshing the page.
        </Typography>

        <Button
          variant="contained"
          startIcon={<Refresh />}
          onClick={handleRetry}
        >
          Refresh Page
        </Button>
      </Box>
    </Box>
  )
}

// Create the wrapped component
const ReportsWithErrorBoundary = withErrorBoundary(Reports, {
  fallback: <ReportsErrorFallback />,
  componentName: 'Reports',
  onError: (error, errorInfo) => {
    console.error('Reports Error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    })
  }
})

export default ReportsWithErrorBoundary
