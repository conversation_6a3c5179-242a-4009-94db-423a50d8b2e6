/**
 * Critical Error Fixes Test
 * 
 * Tests all the fixes implemented for:
 * 1. MenuScreen component React errors
 * 2. Browser extension conflicts
 * 3. Navigation state management
 * 4. Loading screen persistence issues
 */

import puppeteer from 'puppeteer';

async function testCriticalErrorFixes() {
  console.log('🧪 Testing Critical Error Fixes...\n');
  console.log('🔧 Verifying React 18+ Error Handling & Navigation Fixes');
  console.log('=' .repeat(70));
  
  const browser = await puppeteer.launch({ 
    headless: false,
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Capture all console messages and errors
    const consoleMessages = [];
    const jsErrors = [];
    const networkErrors = [];
    const extensionErrors = [];
    
    page.on('console', msg => {
      const text = msg.text();
      const type = msg.type();
      
      consoleMessages.push({ type, text, timestamp: Date.now() });
      
      // Categorize errors
      if (type === 'error') {
        if (text.includes('runtime.lastError') || text.includes('extension')) {
          extensionErrors.push(text);
        } else {
          jsErrors.push(text);
        }
      }
    });
    
    page.on('requestfailed', request => {
      networkErrors.push(`${request.url()} - ${request.failure().errorText}`);
    });
    
    page.on('pageerror', error => {
      jsErrors.push(`Page Error: ${error.message}`);
    });
    
    console.log('\n🔐 Step 1: Test Authentication & Initial Loading');
    console.log('-'.repeat(50));
    
    await page.goto('http://localhost:5174/', { waitUntil: 'networkidle0' });
    
    // Wait for initial load and check for loading screen resolution
    await page.waitForSelector('input[type="text"]', { timeout: 10000 });
    
    const initialState = await page.evaluate(() => {
      return {
        hasLoginForm: !!document.querySelector('input[type="text"]'),
        hasLoadingSpinner: !!document.querySelector('.MuiCircularProgress-root'),
        bodyText: document.body.textContent.substring(0, 200),
        jsErrorsInConsole: window.__JS_ERRORS__ || []
      };
    });
    
    console.log('✅ Initial page load successful');
    console.log(`   Login form present: ${initialState.hasLoginForm}`);
    console.log(`   Loading spinner: ${initialState.hasLoadingSpinner}`);
    
    // Test login process
    console.log('\n🔑 Step 2: Test Login Process & State Management');
    console.log('-'.repeat(50));
    
    await page.type('input[type="text"]', 'ADMIN001');
    await page.type('input[type="password"]', 'admin123');
    
    // Monitor loading states during login
    let loadingStates = [];
    const startTime = Date.now();
    
    await page.click('button[type="submit"]');
    
    // Monitor state changes for 15 seconds max
    for (let i = 0; i < 15; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const currentState = await page.evaluate(() => {
        return {
          url: window.location.pathname,
          hasLoadingText: document.body.textContent.includes('Loading'),
          hasLoginForm: !!document.querySelector('input[type="text"]'),
          hasDashboardContent: document.body.textContent.includes('Dashboard') ||
                              document.body.textContent.includes('Today\'s Sales'),
          hasMenuContent: document.body.textContent.includes('Menu'),
          bodyTextLength: document.body.textContent.length
        };
      });
      
      loadingStates.push({
        time: i + 1,
        ...currentState
      });
      
      // Break if we've successfully loaded content
      if (!currentState.hasLoadingText && !currentState.hasLoginForm && 
          (currentState.hasDashboardContent || currentState.url !== '/')) {
        console.log(`✅ Login successful after ${i + 1} seconds`);
        break;
      }
      
      if (i === 14) {
        console.log('⚠️  Login process took longer than expected');
      }
    }
    
    console.log(`   Final URL: ${loadingStates[loadingStates.length - 1]?.url}`);
    console.log(`   Loading resolved: ${!loadingStates[loadingStates.length - 1]?.hasLoadingText}`);
    
    console.log('\n🍽️ Step 3: Test MenuScreen Component (Critical Fix)');
    console.log('-'.repeat(50));
    
    // Navigate to menu page to test the fixed MenuScreen component
    const menuErrorsBefore = jsErrors.length;
    
    await page.goto('http://localhost:5174/menu', { waitUntil: 'networkidle0' });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const menuState = await page.evaluate(() => {
      return {
        url: window.location.pathname,
        hasMenuContent: document.body.textContent.includes('Menu') ||
                       document.body.textContent.includes('Categories'),
        hasProducts: document.querySelectorAll('.MuiCard-root').length > 0,
        hasCart: document.body.textContent.includes('Cart') ||
                document.body.textContent.includes('Current Order'),
        hasLoadingSpinner: !!document.querySelector('.MuiCircularProgress-root'),
        hasErrorBoundary: document.body.textContent.includes('Something went wrong'),
        reactErrors: window.__REACT_ERROR_OVERLAY_GLOBAL_HOOK__?.errors || []
      };
    });
    
    const menuErrorsAfter = jsErrors.length;
    const newMenuErrors = menuErrorsAfter - menuErrorsBefore;
    
    console.log('✅ MenuScreen component test results:');
    console.log(`   Menu content loaded: ${menuState.hasMenuContent}`);
    console.log(`   Products visible: ${menuState.hasProducts}`);
    console.log(`   Cart functionality: ${menuState.hasCart}`);
    console.log(`   Loading spinner: ${menuState.hasLoadingSpinner}`);
    console.log(`   Error boundary triggered: ${menuState.hasErrorBoundary}`);
    console.log(`   New JS errors: ${newMenuErrors}`);
    
    console.log('\n🧭 Step 4: Test Navigation Between Pages');
    console.log('-'.repeat(50));
    
    const navigationTests = [
      { path: '/dashboard', name: 'Dashboard' },
      { path: '/orders', name: 'Orders' },
      { path: '/bills', name: 'Bills' },
      { path: '/inventory', name: 'Inventory' },
      { path: '/customers', name: 'Customers' },
      { path: '/reports', name: 'Reports' },
      { path: '/settings', name: 'Settings' },
      { path: '/menu', name: 'Menu (Return Test)' }
    ];
    
    const navigationResults = [];
    
    for (const test of navigationTests) {
      const navStartTime = Date.now();
      
      await page.goto(`http://localhost:5174${test.path}`, { waitUntil: 'networkidle0' });
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const navState = await page.evaluate(() => {
        return {
          hasLoadingSpinner: !!document.querySelector('.MuiCircularProgress-root'),
          hasContent: document.body.textContent.length > 500,
          hasError: document.body.textContent.includes('Error') ||
                   document.body.textContent.includes('Something went wrong'),
          contentPreview: document.body.textContent.substring(0, 100)
        };
      });
      
      const navDuration = Date.now() - navStartTime;
      
      navigationResults.push({
        page: test.name,
        path: test.path,
        duration: navDuration,
        success: navState.hasContent && !navState.hasLoadingSpinner && !navState.hasError,
        ...navState
      });
      
      console.log(`   ${test.name}: ${navState.hasContent && !navState.hasLoadingSpinner ? '✅' : '❌'} (${navDuration}ms)`);
    }
    
    console.log('\n🛡️ Step 5: Test Browser Extension Error Handling');
    console.log('-'.repeat(50));
    
    // Check if extension errors are being suppressed
    const extensionErrorsFiltered = extensionErrors.filter(error => 
      !error.includes('Browser Extension Handler') && 
      !error.includes('Suppressed')
    );
    
    console.log(`   Total console messages: ${consoleMessages.length}`);
    console.log(`   Extension errors detected: ${extensionErrors.length}`);
    console.log(`   Extension errors not suppressed: ${extensionErrorsFiltered.length}`);
    console.log(`   JavaScript errors: ${jsErrors.length}`);
    console.log(`   Network errors: ${networkErrors.length}`);
    
    console.log('\n📊 Step 6: Overall System Health Check');
    console.log('-'.repeat(50));
    
    const healthCheck = {
      authenticationWorking: loadingStates.some(state => state.hasDashboardContent),
      menuComponentFixed: menuState.hasMenuContent && newMenuErrors === 0,
      navigationWorking: navigationResults.filter(r => r.success).length >= 6,
      extensionErrorsSuppressed: extensionErrorsFiltered.length === 0,
      noJavaScriptErrors: jsErrors.length === 0,
      loadingStatesResolve: !loadingStates[loadingStates.length - 1]?.hasLoadingText
    };
    
    const healthScore = Object.values(healthCheck).filter(Boolean).length;
    const totalChecks = Object.keys(healthCheck).length;
    
    console.log('🏥 System Health Report:');
    Object.entries(healthCheck).forEach(([check, passed]) => {
      console.log(`   ${passed ? '✅' : '❌'} ${check.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
    });
    
    console.log(`\n🎯 Overall Health Score: ${healthScore}/${totalChecks} (${((healthScore/totalChecks)*100).toFixed(1)}%)`);
    
    // Detailed error analysis
    if (jsErrors.length > 0) {
      console.log('\n❌ JavaScript Errors Found:');
      jsErrors.slice(0, 5).forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.substring(0, 100)}...`);
      });
    }
    
    if (extensionErrorsFiltered.length > 0) {
      console.log('\n⚠️  Unfiltered Extension Errors:');
      extensionErrorsFiltered.slice(0, 3).forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.substring(0, 100)}...`);
      });
    }
    
    return {
      success: healthScore >= 5, // At least 5/6 checks should pass
      healthScore,
      totalChecks,
      details: {
        authentication: healthCheck.authenticationWorking,
        menuComponent: healthCheck.menuComponentFixed,
        navigation: healthCheck.navigationWorking,
        extensionHandling: healthCheck.extensionErrorsSuppressed,
        jsErrors: jsErrors.length,
        loadingResolution: healthCheck.loadingStatesResolve
      }
    };
    
  } catch (error) {
    console.error('💥 Critical error fixes test failed:', error);
    return { success: false, error: error.message };
  } finally {
    await browser.close();
  }
}

// Run the test
testCriticalErrorFixes().then(result => {
  console.log('\n' + '='.repeat(70));
  console.log('🏆 CRITICAL ERROR FIXES TEST RESULTS');
  console.log('='.repeat(70));
  
  if (result.success) {
    console.log('🎉 Critical Error Fixes Test PASSED!');
    console.log(`✅ System health: ${result.healthScore}/${result.totalChecks} checks passed`);
    console.log('\n🚀 All critical issues have been resolved:');
    console.log('   • MenuScreen React errors fixed');
    console.log('   • Browser extension conflicts handled');
    console.log('   • Navigation state management improved');
    console.log('   • Loading screen persistence resolved');
  } else {
    console.log('⚠️  Critical Error Fixes Test needs attention');
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    } else {
      console.log(`   Health score: ${result.healthScore}/${result.totalChecks} (needs improvement)`);
    }
  }
}).catch(error => {
  console.error('\n💥 Test execution failed:', error);
});
