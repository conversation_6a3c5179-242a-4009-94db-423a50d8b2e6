import React from 'react'
import { Button, ButtonProps, Tooltip } from '@mui/material'
import { styled } from '@mui/material/styles'

interface AccessibleButtonProps extends ButtonProps {
  ariaLabel?: string
  tooltip?: string
  loading?: boolean
  loadingText?: string
  focusRipple?: boolean
}

const StyledButton = styled(Button)(({ theme }) => ({
  // Enhanced focus styles for accessibility
  '&:focus-visible': {
    outline: `3px solid ${theme.palette.primary.main}`,
    outlineOffset: '2px',
    boxShadow: `0 0 0 3px ${theme.palette.primary.main}40`
  },
  
  // High contrast mode support
  '@media (prefers-contrast: high)': {
    border: '2px solid currentColor',
    '&:hover': {
      backgroundColor: 'ButtonHighlight',
      color: 'ButtonText'
    }
  },
  
  // Reduced motion support
  '@media (prefers-reduced-motion: reduce)': {
    transition: 'none',
    '& .MuiTouchRipple-root': {
      display: 'none'
    }
  },
  
  // Minimum touch target size (44px)
  minHeight: '44px',
  minWidth: '44px',
  
  // Better text contrast
  '&.Mui-disabled': {
    opacity: 0.6,
    cursor: 'not-allowed'
  }
}))

const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  children,
  ariaLabel,
  tooltip,
  loading = false,
  loadingText = 'Loading...',
  disabled,
  focusRipple = true,
  ...props
}) => {
  const buttonElement = (
    <StyledButton
      {...props}
      disabled={disabled || loading}
      aria-label={ariaLabel || (typeof children === 'string' ? children : undefined)}
      aria-busy={loading}
      aria-describedby={loading ? 'loading-description' : undefined}
      focusRipple={focusRipple}
      role="button"
      tabIndex={disabled ? -1 : 0}
    >
      {loading ? loadingText : children}
      {loading && (
        <span id="loading-description" className="sr-only">
          Please wait, operation in progress
        </span>
      )}
    </StyledButton>
  )

  if (tooltip) {
    return (
      <Tooltip 
        title={tooltip} 
        arrow
        enterDelay={500}
        leaveDelay={200}
        placement="top"
      >
        <span>
          {buttonElement}
        </span>
      </Tooltip>
    )
  }

  return buttonElement
}

export default AccessibleButton
