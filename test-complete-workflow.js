/**
 * Comprehensive POS System Workflow Test
 * 
 * Tests the complete workflow including:
 * 1. Direct payment integration in MenuScreen
 * 2. Bills page as transaction history
 * 3. Settings page functionality
 * 4. Navigation state management
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://kufsqfbiilphymiehwet.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt1ZnNxZmJpaWxwaHltaWVod2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzE0NzQsImV4cCI6MjA1MDU0NzQ3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testCompleteWorkflow() {
  console.log('🧪 Starting Comprehensive POS System Workflow Test...\n')

  try {
    // Step 1: Test Authentication
    console.log('📋 Step 1: Testing Authentication System...')
    
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    })

    if (authError) {
      console.error('❌ Authentication failed:', authError.message)
      return
    }

    console.log('✅ Authentication successful')
    const userId = authData.user.id

    // Step 2: Test Profile Fetch
    console.log('\n📋 Step 2: Testing Profile Data...')
    
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select(`
        *,
        role:roles(role_name, permissions)
      `)
      .eq('id', userId)
      .single()

    if (profileError) {
      console.error('❌ Profile fetch failed:', profileError.message)
      return
    }

    console.log('✅ Profile data retrieved successfully')
    console.log(`   User: ${profile.full_name} (${profile.role.role_name})`)

    // Step 3: Test Menu Data for Direct Payment
    console.log('\n📋 Step 3: Testing Menu Data for Direct Payment...')
    
    const { data: categories, error: catError } = await supabase
      .from('categories')
      .select('*')
      .order('name')

    const { data: products, error: prodError } = await supabase
      .from('products')
      .select(`
        *,
        categories(name)
      `)
      .eq('is_active', true)
      .order('name')

    if (catError || prodError) {
      console.error('❌ Menu data fetch failed:', catError || prodError)
      return
    }

    console.log('✅ Menu data retrieved successfully')
    console.log(`   Categories: ${categories.length}`)
    console.log(`   Products: ${products.length}`)

    // Step 4: Test Direct Payment Order Creation
    console.log('\n📋 Step 4: Testing Direct Payment Order Creation...')
    
    const testProduct = products[0]
    const orderData = {
      table_number: 'T99',
      order_type: 'dine_in',
      status: 'completed',
      subtotal_amount: testProduct.price,
      tax_amount: testProduct.price * 0.10, // 10% for dine-in
      discount_amount: 0,
      complementary_amount: 0,
      total_amount: testProduct.price * 1.10,
      employee_id: userId,
      customer_id: null,
      notes: 'Test direct payment order'
    }

    const { data: orderResult, error: orderError } = await supabase
      .from('orders')
      .insert(orderData)
      .select()
      .single()

    if (orderError) {
      console.error('❌ Order creation failed:', orderError.message)
      return
    }

    console.log('✅ Direct payment order created successfully')
    console.log(`   Order ID: ${orderResult.id.slice(0, 8)}`)

    // Step 5: Create Order Items
    console.log('\n📋 Step 5: Creating Order Items...')
    
    const orderItemData = {
      order_id: orderResult.id,
      product_id: testProduct.id,
      quantity: 1,
      unit_price_at_order: testProduct.price,
      item_subtotal: testProduct.price
    }

    const { error: itemError } = await supabase
      .from('order_items')
      .insert(orderItemData)

    if (itemError) {
      console.error('❌ Order item creation failed:', itemError.message)
      return
    }

    console.log('✅ Order items created successfully')

    // Step 6: Test Direct Payment Processing
    console.log('\n📋 Step 6: Testing Direct Payment Processing...')
    
    const paymentData = {
      order_id: orderResult.id,
      amount: orderResult.total_amount,
      payment_method: 'Card',
      status: 'completed',
      transaction_reference: `TEST_${Date.now()}`,
      processed_by_employee_id: userId
    }

    const { data: paymentResult, error: paymentError } = await supabase
      .from('payments')
      .insert(paymentData)
      .select()
      .single()

    if (paymentError) {
      console.error('❌ Payment processing failed:', paymentError.message)
      return
    }

    console.log('✅ Direct payment processed successfully')
    console.log(`   Payment ID: ${paymentResult.id}`)
    console.log(`   Amount: $${paymentResult.amount.toFixed(2)}`)
    console.log(`   Method: ${paymentResult.payment_method}`)

    // Step 7: Test Transaction History Query
    console.log('\n📋 Step 7: Testing Transaction History Query...')
    
    const { data: completedOrders, error: historyError } = await supabase
      .from('orders')
      .select(`
        *,
        customers(full_name),
        order_items(
          id,
          product_id,
          quantity,
          unit_price_at_order,
          item_subtotal,
          products(name, description)
        ),
        payments(
          id,
          amount,
          payment_method,
          status,
          transaction_reference,
          processed_by_employee_id,
          created_at,
          updated_at
        )
      `)
      .eq('status', 'completed')
      .order('created_at', { ascending: false })
      .limit(10)

    if (historyError) {
      console.error('❌ Transaction history query failed:', historyError.message)
      return
    }

    console.log('✅ Transaction history retrieved successfully')
    console.log(`   Completed orders: ${completedOrders.length}`)
    
    const testOrder = completedOrders.find(order => order.id === orderResult.id)
    if (testOrder) {
      console.log(`   Test order found in history: ${testOrder.id.slice(0, 8)}`)
      console.log(`   Payment method: ${testOrder.payments[0]?.payment_method}`)
    }

    // Step 8: Test Settings Data
    console.log('\n📋 Step 8: Testing Settings Data...')
    
    // Test user settings
    const { data: userSettings, error: userSettingsError } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (userSettingsError && userSettingsError.code !== 'PGRST116') {
      console.error('❌ User settings query failed:', userSettingsError.message)
    } else {
      console.log('✅ User settings query successful')
    }

    // Test system settings (admin only)
    if (profile.role.role_name === 'admin') {
      const { data: systemSettings, error: systemSettingsError } = await supabase
        .from('system_settings')
        .select('*')
        .single()

      if (systemSettingsError && systemSettingsError.code !== 'PGRST116') {
        console.error('❌ System settings query failed:', systemSettingsError.message)
      } else {
        console.log('✅ System settings query successful')
      }
    }

    // Step 9: Test Inventory Update
    console.log('\n📋 Step 9: Testing Inventory Update...')
    
    const { error: inventoryError } = await supabase.rpc('decrement_product_stock', {
      product_id: testProduct.id,
      quantity: 1
    })

    if (inventoryError) {
      console.warn('⚠️ Inventory update failed:', inventoryError.message)
    } else {
      console.log('✅ Inventory updated successfully')
    }

    // Step 10: Test Data Filtering (Bills page functionality)
    console.log('\n📋 Step 10: Testing Data Filtering...')
    
    // Test payment method filter
    const { data: cardPayments, error: filterError } = await supabase
      .from('orders')
      .select(`
        *,
        payments!inner(payment_method)
      `)
      .eq('status', 'completed')
      .eq('payments.payment_method', 'Card')
      .limit(5)

    if (filterError) {
      console.error('❌ Payment method filter failed:', filterError.message)
    } else {
      console.log('✅ Payment method filtering successful')
      console.log(`   Card payments found: ${cardPayments.length}`)
    }

    // Final Summary
    console.log('\n🎉 Comprehensive Workflow Test Summary:')
    console.log('✅ Authentication system working')
    console.log('✅ Profile data retrieval working')
    console.log('✅ Menu data loading working')
    console.log('✅ Direct payment order creation working')
    console.log('✅ Payment processing working')
    console.log('✅ Transaction history query working')
    console.log('✅ Settings data access working')
    console.log('✅ Inventory updates working')
    console.log('✅ Data filtering working')
    console.log('\n🚀 All core POS workflows are functioning correctly!')

  } catch (error) {
    console.error('💥 Unexpected error during testing:', error)
  }
}

// Run the test
testCompleteWorkflow()
