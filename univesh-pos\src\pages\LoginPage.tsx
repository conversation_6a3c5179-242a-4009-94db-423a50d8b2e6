import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Tab,
  Tabs,
  Alert,
  CircularProgress,
  Divider,
  Grid,
  <PERSON>
} from '@mui/material'
import {
  Google,
  Facebook,
  WifiOff as WifiOffIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'
import { useAuth } from '../contexts/AuthContext'
import { colors } from '../theme'
import NetworkStatus from '../components/NetworkStatus'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`auth-tabpanel-${index}`}
      aria-labelledby={`auth-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  )
}

const LoginPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  
  // Sign In Form
  const [signInData, setSignInData] = useState({
    salesId: '',
    password: ''
  })

  // Sign Up Form
  const [signUpData, setSignUpData] = useState({
    salesId: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    email: ''
  })

  const { signIn, signUp, isOnline, refreshSession } = useAuth()

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
    setError(null)
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Check network connectivity
    if (!isOnline) {
      setError('No internet connection. Please check your network and try again.')
      setLoading(false)
      return
    }

    const { error } = await signIn(signInData.salesId, signInData.password)

    if (error) {
      setError(error.userMessage || error.message || 'Failed to sign in')

      // If it's a network error, suggest retry
      if (error.code === 'NETWORK_ERROR') {
        setRetryCount(prev => prev + 1)
      }
    }

    setLoading(false)
  }

  const handleRetry = async () => {
    if (!isOnline) {
      try {
        await refreshSession()
      } catch (error) {
        console.error('Failed to refresh session:', error)
      }
    }

    // Clear error and retry the last action
    setError(null)
    if (tabValue === 0) {
      handleSignIn({ preventDefault: () => {} } as React.FormEvent)
    }
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Check network connectivity
    if (!isOnline) {
      setError('No internet connection. Please check your network and try again.')
      setLoading(false)
      return
    }

    if (signUpData.password !== signUpData.confirmPassword) {
      setError('Passwords do not match')
      setLoading(false)
      return
    }

    const { error } = await signUp(
      signUpData.salesId,
      signUpData.password,
      signUpData.fullName,
      signUpData.email
    )

    if (error) {
      setError(error.userMessage || error.message || 'Failed to create account')

      // If it's a network error, suggest retry
      if (error.code === 'NETWORK_ERROR') {
        setRetryCount(prev => prev + 1)
      }
    } else {
      setError(null)
      // Switch to sign in tab after successful registration
      setTabValue(0)
      setSignInData({ salesId: signUpData.salesId, password: '' })
    }

    setLoading(false)
  }

  return (
    <>
      <NetworkStatus />
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          backgroundColor: colors.background.default
        }}
      >
      {/* Left Section - Branding */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: colors.primary.main,
          color: colors.primary.contrastText,
          p: 4
        }}
      >
        <Typography variant="h2" sx={{ fontWeight: 700, mb: 2 }}>
          Univesh
        </Typography>
        <Typography variant="h5" sx={{ fontWeight: 400, textAlign: 'center', mb: 4 }}>
          Restaurant POS System
        </Typography>
        <Typography variant="body1" sx={{ textAlign: 'center', maxWidth: 400 }}>
          Streamline your restaurant operations with our comprehensive point-of-sale solution. 
          Manage orders, inventory, customers, and analytics all in one place.
        </Typography>
      </Box>

      {/* Right Section - Authentication Form */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          p: 4
        }}
      >
        <Card
          sx={{
            width: '100%',
            maxWidth: 450,
            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)'
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 3, textAlign: 'center' }}>
              Welcome Back
            </Typography>

            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              variant="fullWidth"
              sx={{ mb: 2 }}
            >
              <Tab label="Sign In" />
              <Tab label="Get Started" />
            </Tabs>

            {error && (
              <Alert
                severity="error"
                sx={{ mb: 2 }}
                action={
                  (error.includes('network') || error.includes('connection') || !isOnline) && (
                    <Button
                      color="inherit"
                      size="small"
                      onClick={handleRetry}
                      startIcon={<RefreshIcon />}
                    >
                      Retry
                    </Button>
                  )
                }
              >
                {error}
                {!isOnline && (
                  <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <WifiOffIcon fontSize="small" />
                    <Typography variant="caption">
                      You're currently offline
                    </Typography>
                  </Box>
                )}
              </Alert>
            )}

            {/* Sign In Tab */}
            <TabPanel value={tabValue} index={0}>
              <Box component="form" onSubmit={handleSignIn}>
                <TextField
                  fullWidth
                  label="Sales ID Number"
                  variant="outlined"
                  value={signInData.salesId}
                  onChange={(e) => setSignInData({ ...signInData, salesId: e.target.value })}
                  required
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Password"
                  type="password"
                  variant="outlined"
                  value={signInData.password}
                  onChange={(e) => setSignInData({ ...signInData, password: e.target.value })}
                  required
                  sx={{ mb: 3 }}
                />
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  sx={{ mb: 2 }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Sign In'}
                </Button>
              </Box>
            </TabPanel>

            {/* Sign Up Tab */}
            <TabPanel value={tabValue} index={1}>
              <Box component="form" onSubmit={handleSignUp}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      variant="outlined"
                      value={signUpData.fullName}
                      onChange={(e) => setSignUpData({ ...signUpData, fullName: e.target.value })}
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      type="email"
                      variant="outlined"
                      value={signUpData.email}
                      onChange={(e) => setSignUpData({ ...signUpData, email: e.target.value })}
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Sales ID Number"
                      variant="outlined"
                      value={signUpData.salesId}
                      onChange={(e) => setSignUpData({ ...signUpData, salesId: e.target.value })}
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Password"
                      type="password"
                      variant="outlined"
                      value={signUpData.password}
                      onChange={(e) => setSignUpData({ ...signUpData, password: e.target.value })}
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Confirm Password"
                      type="password"
                      variant="outlined"
                      value={signUpData.confirmPassword}
                      onChange={(e) => setSignUpData({ ...signUpData, confirmPassword: e.target.value })}
                      required
                    />
                  </Grid>
                </Grid>
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  sx={{ mt: 3, mb: 2 }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Create Account'}
                </Button>
              </Box>
            </TabPanel>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            {/* Social Login Buttons */}
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google />}
                sx={{ py: 1.5 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook />}
                sx={{ py: 1.5 }}
              >
                Facebook
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Box>
    </>
  )
}

export default LoginPage
