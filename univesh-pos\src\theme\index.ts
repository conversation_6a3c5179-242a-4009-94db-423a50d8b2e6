import { createTheme } from '@mui/material/styles'

// Color palette from Style Guide.png
export const colors = {
  primary: {
    main: '#1049B8', // Primary Blue
    light: '#4E77B5', // Dark Blue
    dark: '#0D3A8F',
    contrastText: '#FFFFFF'
  },
  secondary: {
    main: '#F8BF24', // Yellow
    light: '#FDC548', // Orange
    dark: '#E6A800',
    contrastText: '#000000'
  },
  success: {
    main: '#36D084', // Green
    light: '#5DD89A',
    dark: '#2BA66B',
    contrastText: '#FFFFFF'
  },
  error: {
    main: '#E15F71', // Red
    light: '#E77A8A',
    dark: '#D4455A',
    contrastText: '#FFFFFF'
  },
  warning: {
    main: '#FDC548', // Orange
    light: '#FDD26B',
    dark: '#E6B041',
    contrastText: '#000000'
  },
  info: {
    main: '#9E8BCE', // Purple
    light: '#B5A5D6',
    dark: '#8A7AB8',
    contrastText: '#FFFFFF'
  },
  grey: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E2E3E4', // Grey from style guide
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121'
  },
  background: {
    default: '#FFFFFF', // White
    paper: '#FFFFFF'
  },
  text: {
    primary: '#000000', // Black
    secondary: '#616161',
    disabled: '#9E9E9E'
  }
}

// Typography configuration with Gilroy as primary font
const typography = {
  fontFamily: '"Gilroy", "Inter", "Roboto", "Helvetica", "Arial", sans-serif',
  h1: {
    fontFamily: '"Gilroy", "Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    fontWeight: 700,
    fontSize: '2.5rem',
    lineHeight: 1.2
  },
  h2: {
    fontFamily: '"Gilroy", "Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    fontWeight: 600,
    fontSize: '2rem',
    lineHeight: 1.3
  },
  h3: {
    fontFamily: '"Gilroy", "Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    fontWeight: 600,
    fontSize: '1.75rem',
    lineHeight: 1.3
  },
  h4: {
    fontFamily: '"Gilroy", "Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    fontWeight: 600,
    fontSize: '1.5rem',
    lineHeight: 1.4
  },
  h5: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    fontWeight: 600,
    fontSize: '1.25rem',
    lineHeight: 1.4
  },
  h6: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    fontWeight: 600,
    fontSize: '1rem',
    lineHeight: 1.5
  },
  body1: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    fontWeight: 400,
    fontSize: '1rem',
    lineHeight: 1.5
  },
  body2: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    fontWeight: 400,
    fontSize: '0.875rem',
    lineHeight: 1.43
  },
  button: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    fontWeight: 500,
    fontSize: '0.875rem',
    lineHeight: 1.75,
    textTransform: 'none' as const
  }
}

// Create the theme with responsive breakpoints and accessibility
export const theme = createTheme({
  palette: {
    primary: colors.primary,
    secondary: colors.secondary,
    success: colors.success,
    error: colors.error,
    warning: colors.warning,
    info: colors.info,
    grey: colors.grey,
    background: colors.background,
    text: colors.text
  },
  typography,
  shape: {
    borderRadius: 8
  },
  spacing: 8,
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536
    }
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          padding: '10px 24px',
          fontWeight: 500,
          textTransform: 'none',
          boxShadow: 'none',
          minHeight: '44px', // Accessibility: minimum touch target
          '&:hover': {
            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)'
          },
          '&:focus-visible': {
            outline: `3px solid ${colors.primary.main}`,
            outlineOffset: '2px'
          },
          // Responsive padding
          '@media (max-width: 600px)': {
            padding: '8px 16px',
            fontSize: '0.875rem'
          }
        },
        containedPrimary: {
          backgroundColor: colors.primary.main,
          color: colors.primary.contrastText,
          '&:hover': {
            backgroundColor: colors.primary.dark
          }
        }
      }
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
          border: `1px solid ${colors.grey[300]}`
        }
      }
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            '& fieldset': {
              borderColor: colors.grey[300]
            },
            '&:hover fieldset': {
              borderColor: colors.primary.main
            },
            '&.Mui-focused fieldset': {
              borderColor: colors.primary.main
            }
          }
        }
      }
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: colors.background.paper,
          color: colors.text.primary,
          boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.1)'
        }
      }
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: colors.background.paper,
          borderRight: `1px solid ${colors.grey[300]}`
        }
      }
    }
  }
})

export default theme
