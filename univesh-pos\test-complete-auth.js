/**
 * Complete Authentication Flow Test
 * 
 * This script comprehensively tests the authentication system:
 * 1. No timeout errors during initialization
 * 2. Successful login with valid credentials
 * 3. Proper redirect to dashboard
 * 4. User profile loading
 * 5. Protected route access
 */

import puppeteer from 'puppeteer';

async function testCompleteAuthFlow() {
  console.log('🧪 Testing Complete Authentication Flow...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    devtools: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Listen for console messages
    const consoleMessages = [];
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      if (text.includes('ERROR') || text.includes('timeout') || text.includes('failed')) {
        console.log(`⚠️  Console: ${text}`);
      }
    });
    
    console.log('🌐 Step 1: Navigate to application...');
    await page.goto('http://localhost:5173/', { waitUntil: 'networkidle0' });
    
    // Wait for auth initialization
    await page.waitForSelector('input[type="text"]', { timeout: 10000 });
    console.log('✅ Login page loaded');
    
    // Wait a bit more to ensure auth initialization completes
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('🔍 Step 2: Check for timeout errors...');
    const hasTimeoutError = consoleMessages.some(msg => 
      msg.includes('Auth initialization timeout reached') ||
      msg.includes('timeout') && msg.includes('15s')
    );
    
    if (hasTimeoutError) {
      console.log('❌ TIMEOUT ERROR DETECTED');
      return false;
    } else {
      console.log('✅ No timeout errors found');
    }
    
    console.log('🔐 Step 3: Test login functionality...');
    
    // Fill in the login form
    await page.type('input[type="text"]', 'ADMIN001');
    await page.type('input[type="password"]', 'admin123');
    console.log('📝 Credentials entered');
    
    // Click sign in button
    await page.click('button[type="submit"]');
    console.log('🖱️ Sign in button clicked');
    
    // Wait for navigation
    try {
      await page.waitForNavigation({ timeout: 15000 });
      console.log('✅ Navigation occurred');
      
      const currentUrl = page.url();
      console.log(`📍 Current URL: ${currentUrl}`);
      
      if (currentUrl.includes('/dashboard')) {
        console.log('✅ Successfully redirected to dashboard');
      } else {
        console.log(`❌ Unexpected URL: ${currentUrl}`);
        return false;
      }
    } catch (error) {
      console.log('❌ Navigation timeout - checking for errors...');
      
      // Check for error messages
      const errorElement = await page.$('.MuiAlert-message');
      if (errorElement) {
        const errorText = await page.evaluate(el => el.textContent, errorElement);
        console.log(`❌ Error message: ${errorText}`);
        return false;
      }
      
      console.log('❌ No navigation occurred and no error message found');
      return false;
    }
    
    console.log('👤 Step 4: Verify user profile loading...');
    
    // Wait for dashboard content to load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if profile loaded successfully
    const profileLoaded = consoleMessages.some(msg => 
      msg.includes('Profile fetched successfully') ||
      msg.includes('✅ [AuthContext] Profile fetched successfully')
    );
    
    if (profileLoaded) {
      console.log('✅ User profile loaded successfully');
    } else {
      console.log('⚠️  Profile loading status unclear');
    }
    
    console.log('🛡️ Step 5: Test protected route access...');
    
    // Try to access a protected route (settings)
    await page.goto('http://localhost:5173/settings', { waitUntil: 'networkidle0' });
    
    const settingsUrl = page.url();
    if (settingsUrl.includes('/settings')) {
      console.log('✅ Protected route accessible');
    } else if (settingsUrl.includes('/login')) {
      console.log('❌ Redirected to login - authentication failed');
      return false;
    } else {
      console.log(`⚠️  Unexpected URL for protected route: ${settingsUrl}`);
    }
    
    console.log('🔄 Step 6: Test logout functionality...');
    
    // Look for logout button or user menu
    try {
      // Try to find and click logout (this might vary based on UI)
      const logoutButton = await page.$('[data-testid="logout-button"]') || 
                          await page.$('button:contains("Logout")') ||
                          await page.$('button:contains("Sign Out")');
      
      if (logoutButton) {
        await logoutButton.click();
        console.log('🖱️ Logout button clicked');
        
        // Wait for redirect to login
        await page.waitForNavigation({ timeout: 5000 });
        
        const loginUrl = page.url();
        if (loginUrl.includes('/login') || loginUrl === 'http://localhost:5173/') {
          console.log('✅ Successfully logged out and redirected');
        } else {
          console.log(`⚠️  Logout redirect unclear: ${loginUrl}`);
        }
      } else {
        console.log('⚠️  Logout button not found - skipping logout test');
      }
    } catch (error) {
      console.log('⚠️  Logout test failed:', error.message);
    }
    
    return true;
    
  } catch (error) {
    console.error('💥 Test failed:', error);
    return false;
  } finally {
    await browser.close();
  }
}

// Run the test
testCompleteAuthFlow().then(success => {
  if (success) {
    console.log('\n🎉 Complete Authentication Flow Test PASSED!');
    console.log('\n✅ Summary:');
    console.log('   • No timeout errors during initialization');
    console.log('   • Successful login with ADMIN001/admin123');
    console.log('   • Proper redirect to dashboard');
    console.log('   • Protected routes accessible');
    console.log('   • Authentication system working correctly');
    process.exit(0);
  } else {
    console.log('\n❌ Complete Authentication Flow Test FAILED!');
    process.exit(1);
  }
}).catch(error => {
  console.error('\n💥 Test execution failed:', error);
  process.exit(1);
});
